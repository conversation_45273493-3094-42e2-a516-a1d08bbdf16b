codecov:
  notify:
    require_ci_to_pass: yes

coverage:
  precision: 2
  round: down
  range: "70...100"

  status:
    project:
      default:
        target: 85%
        threshold: 1%
    patch: no
    changes: no

  ignore:
    - "external"
    - "tests"
    - "tools"
    - "build"

parsers:
  gcov:
    branch_detection:
      conditional: yes
      loop: yes
      method: no
      macro: no

comment:
  layout: "reach, diff, flags, files, footer"
  behavior: default
  require_changes: no
