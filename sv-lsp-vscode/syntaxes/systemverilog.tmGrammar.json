{"$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "name": "SystemVerilog", "scopeName": "source.systemverilog", "patterns": [{"include": "#comments"}, {"include": "#keywords"}, {"include": "#strings"}, {"include": "#numbers"}, {"include": "#operators"}, {"include": "#identifiers"}], "repository": {"comments": {"patterns": [{"name": "comment.line.double-slash.systemverilog", "begin": "//", "end": "$"}, {"name": "comment.block.systemverilog", "begin": "/\\*", "end": "\\*/"}]}, "keywords": {"patterns": [{"name": "keyword.control.systemverilog", "match": "\\b(if|else|case|casex|casez|default|for|while|repeat|forever|do|break|continue|return|begin|end|fork|join|join_any|join_none|unique|unique0|priority)\\b"}, {"name": "keyword.other.systemverilog", "match": "\\b(module|endmodule|interface|endinterface|package|endpackage|class|endclass|function|endfunction|task|endtask|generate|endgenerate|property|endproperty|sequence|endsequence|clocking|endclocking|covergroup|endcovergroup|program|endprogram|primitive|endprimitive|config|endconfig|library|endlibrary)\\b"}, {"name": "storage.type.systemverilog", "match": "\\b(logic|bit|byte|shortint|int|longint|real|shortreal|string|chandle|event|mailbox|semaphore|process|void|struct|union|enum|typedef|parameter|localparam|const|static|automatic|virtual|pure|extern|export|import|bind|alias|type|packed|unpacked|signed|unsigned)\\b"}, {"name": "storage.modifier.systemverilog", "match": "\\b(input|output|inout|ref|wire|reg|supply0|supply1|tri|triand|trior|tri0|tri1|uwire|wand|wor|trireg|scalared|vectored|small|medium|large|weak0|weak1|pull0|pull1|strong0|strong1|highz0|highz1)\\b"}, {"name": "keyword.other.special.systemverilog", "match": "\\b(always|always_ff|always_comb|always_latch|initial|final|assign|deassign|force|release|disable|wait|wait_order|expect|assume|assert|cover|restrict|randcase|randsequence|dist|inside|with|solve|before|soft|iff|throughout|within|first_match|intersect|and|or|not)\\b"}, {"name": "keyword.other.directive.systemverilog", "match": "\\b(modport|bind|alias|checker|endchecker|let|constraint|solve|before|soft|local|protected|rand|randc|this|super|null|new|randomize|srandom|get_randstate|set_randstate)\\b"}]}, "strings": {"patterns": [{"name": "string.quoted.double.systemverilog", "begin": "\"", "end": "\"", "patterns": [{"name": "constant.character.escape.systemverilog", "match": "\\\\."}]}]}, "numbers": {"patterns": [{"name": "constant.numeric.systemverilog", "match": "\\b\\d+('([bBoOdDhH])[0-9a-fA-F_xXzZ]+|(\\.\\d*)?([eE][+-]?\\d+)?)\\b"}, {"name": "constant.numeric.systemverilog", "match": "'([bBoOdDhH])[0-9a-fA-F_xXzZ]+"}]}, "operators": {"patterns": [{"name": "keyword.operator.systemverilog", "match": "(\\+\\+|--|\\+=|-=|\\*=|/=|%=|&=|\\|=|\\^=|<<=|>>=|<<<|>>>|==|!=|===|!==|<=|>=|&&|\\|\\||\\*\\*|->|<->)"}, {"name": "keyword.operator.systemverilog", "match": "(\\+|-|\\*|/|%|&|\\||\\^|~|!|<|>|=)"}]}, "identifiers": {"patterns": [{"name": "variable.other.systemverilog", "match": "\\b[a-zA-Z_][a-zA-Z0-9_]*\\b"}]}}}