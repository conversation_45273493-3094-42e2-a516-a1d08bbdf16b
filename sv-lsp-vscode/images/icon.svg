<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#1976D2" stroke-width="2"/>
  
  <!-- Circuit pattern -->
  <g stroke="#FFFFFF" stroke-width="2" fill="none">
    <!-- Horizontal lines -->
    <line x1="20" y1="40" x2="108" y2="40"/>
    <line x1="20" y1="64" x2="108" y2="64"/>
    <line x1="20" y1="88" x2="108" y2="88"/>
    
    <!-- Vertical lines -->
    <line x1="40" y1="20" x2="40" y2="108"/>
    <line x1="64" y1="20" x2="64" y2="108"/>
    <line x1="88" y1="20" x2="88" y2="108"/>
    
    <!-- Connection points -->
    <circle cx="40" cy="40" r="3" fill="#FFFFFF"/>
    <circle cx="64" cy="40" r="3" fill="#FFFFFF"/>
    <circle cx="88" cy="40" r="3" fill="#FFFFFF"/>
    <circle cx="40" cy="64" r="3" fill="#FFFFFF"/>
    <circle cx="88" cy="64" r="3" fill="#FFFFFF"/>
    <circle cx="40" cy="88" r="3" fill="#FFFFFF"/>
    <circle cx="64" cy="88" r="3" fill="#FFFFFF"/>
    <circle cx="88" cy="88" r="3" fill="#FFFFFF"/>
  </g>
  
  <!-- Central processor -->
  <rect x="52" y="52" width="24" height="24" fill="#FFFFFF" stroke="#1976D2" stroke-width="2" rx="2"/>
  
  <!-- Text "SV" -->
  <text x="64" y="70" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="#1976D2">SV</text>
  
  <!-- LSP indicator -->
  <circle cx="100" cy="28" r="12" fill="#FF9800" stroke="#FFFFFF" stroke-width="2"/>
  <text x="100" y="32" font-family="Arial, sans-serif" font-size="8" font-weight="bold" text-anchor="middle" fill="#FFFFFF">LSP</text>
</svg>
