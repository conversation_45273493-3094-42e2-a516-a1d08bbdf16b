// This file is an input to the diagnostic_gen.py script, to generate C++ source
// for built-in compiler diagnostics.

subsystem General
error ExpectedExpression "expected expression"
note NotePreviousDefinition "previous definition here"
note NotePreviousUsage "previous usage here"
note NoteDeclarationHere "declared here"
note Note<PERSON>re<PERSON><PERSON>atch "previous match here"
note Note<PERSON><PERSON>erenced<PERSON>ere "referenced here"
note NoteAssigned<PERSON>ere "also assigned here"
note <PERSON><PERSON><PERSON><PERSON><PERSON>ere "driven here"
note NoteOriginalAssign "original assignment here"
note NoteConfigRule "from configuration here"
note NoteFromHere2 "from '{}' and '{}'"
note NoteExpandedHere "expanded here"
error AttributesNotAllowed "attributes are not allowed here"
error WrongLanguageVersion "language feature is not supported in version {}; use the --std option to select a supported language version"

subsystem Lexer
error NonPrintableChar "non-printable control character in source text"
error UTF8Char "UTF-8 sequence in source text; SystemVerilog identifiers must be ASCII"
error UnicodeBOM "UTF-16 BOM at start of source text; source encoding must be UTF-8"
error EmbeddedNull "embedded NUL in source text"
error MisplacedDirectiveChar "expected directive name"
error EscapedWhitespace "unexpected whitespace after escape character"
error ExpectedClosingQuote "missing closing quote"
error UnterminatedBlockComment "block comment unclosed at end of file"
error NestedBlockComment "nested block comments are disallowed by SystemVerilog"
error ExpectedIntegerBaseAfterSigned "expected integer base specifier after signed specifier"
error OctalEscapeCodeTooBig "octal escape code is too large to be an ASCII character"
error InvalidHexEscapeCode "invalid hexadecimal number"
error DecimalDigitMultipleUnknown "decimal literals cannot have multiple digits if at least one of them is X or Z"
error BadBinaryDigit "expected binary digit"
error BadOctalDigit "expected octal digit"
error BadDecimalDigit "expected decimal digit"
error BadHexDigit "expected hexadecimal digit"
error TooManyLexerErrors "lexer has encountered too many errors (input is a binary file?)"
error UnclosedTranslateOff "missing '{}' counterpart for this directive"
warning unknown-escape-code UnknownEscapeCode "unknown character escape sequence '\\\\{}'"
warning nonstandard-escape-code NonstandardEscapeCode "non-standard character escape sequence '\\\\{}'"
warning invalid-source-encoding InvalidUTF8Seq "invalid UTF-8 sequence in source text"
warning protect-encoding-bytes ProtectEncodingBytes "{} bytes decoded in protected envelope but encoding pragma said to expect {}"
warning invalid-encoding-byte InvalidEncodingByte "invalid {} byte '{}' -- attempting to skip remaining encoded text"
warning raw-protect-eof RawProtectEOF "reached end of file while processing 'raw' encoded protected envelope"

subsystem Numeric
error LiteralSizeIsZero "size of vector literal cannot be zero"
error LiteralSizeTooLarge "size of vector literal is too large (> {} bits)"
error ValueMustBeIntegral "value must be integral"
error ValueMustNotBeUnknown "value must not have any unknown bits"
error ValueMustBePositive "value must be positive"
error ValueExceedsMaxBitWidth "value exceeds maximum vector width ({} bits)"
error ValueOutOfRange "{} is out of allowed range ({} to {})"
error ExpectedVectorDigits "expected vector literal digits"
error MissingFractionalDigits "expected fractional digits after decimal"
error MissingExponentDigits "expected exponent digits"
error DigitsLeadingUnderscore "numeric literals must not start with a leading underscore"
warning real-underflow RealLiteralUnderflow "value of real literal is too small; minimum is {:.5e}"
warning real-overflow RealLiteralOverflow "value of real literal is too large; maximum is {:.5e}"
warning vector-overflow VectorLiteralOverflow "vector literal too large for the given number of bits ({} bits needed)"
warning int-overflow SignedIntegerOverflow "signed integer literal overflows 32 bits, will be truncated to {}"

subsystem Preprocessor
error CouldNotOpenIncludeFile "'{}': {}"
error ExceededMaxIncludeDepth "exceeded max include depth"
error UnknownDirective "unknown macro or compiler directive '{}'"
error UnexpectedConditionalDirective "unexpected conditional directive"
error MissingEndIfDirective "missing `endif directive"
error UnbalancedMacroArgDims "unbalanced macro argument delimiters ((), [], or {{}}); didn't see an end '{}'"
error ExpectedMacroArgs "expected macro arguments for function-like macro"
error ExpectedNetType "expected net type specifier"
error ExpectedDriveStrength "expected pull1 or pull0 strength"
error InvalidMacroName "can't redefine compiler directive as a macro"
error TooManyActualMacroArgs "too many arguments provided to function-like macro"
error NotEnoughMacroArgs "not enough arguments provided to function-like macro"
error InvalidLineDirectiveLevel "invalid level for `line directive, must be 0, 1, or 2"
error UndefineBuiltinDirective "cannot `undef built-in directives"
error UnrecognizedKeywordVersion "unsupported keyword version specified for `begin_keywords"
error MismatchedEndKeywordsDirective "no opening `begin_keywords directive"
error InvalidTimeScaleSpecifier "time scale must have a value of 1, 10, or 100"
error InvalidTimeScalePrecision "time precision must be at least as precise as the time unit (i.e. it cannot be a longer unit of time)"
error InvalidInferredTimeScale "invalid inferred time scale of {} for scope; time precision must be at least as precise as the time unit (i.e. it cannot be a longer unit of time)"
error RecursiveMacro "expansion of macro '{}' is recursive"
error MacroOpsOutsideDefinition "macro operators may only be used within a macro definition"
error ExpectedIncludeFileName "expected an include file name"
error ExpectedTimeLiteral "expected time literal"
error ExpectedMacroStringifyEnd "expected closing quote to end macro stringification"
error ExpectedPragmaName "expected pragma name"
error ExpectedPragmaExpression "expected pragma expression"
error DirectiveInsideDesignElement "directive is not allowed inside a design element"
error MacroTokensAfterPragmaProtect "unexpected macro expansion after pragma protect directive"
note NoteDirectiveHere "after protect directive here"
warning ignored-macro-paste IgnoredMacroPaste "paste token is pointless because it is adjacent to whitespace"
warning redef-macro RedefiningMacro "macro '{}' redefined"
warning unknown-pragma UnknownPragma "unknown pragma '{}'"
warning extra-pragma-args ExtraPragmaArgs "too many arguments provided for pragma '{}'"
warning expected-diag-arg ExpectedDiagPragmaArg "expected diagnostic pragma argument"
warning unknown-diag-arg UnknownDiagPragmaArg "unknown diagnostic pragma argument '{}'"
warning pragma-diag-level ExpectedDiagPragmaLevel "expected diagnostic severity (ignore,warn,error,fatal)"
warning expected-protect-keyword ExpectedProtectKeyword "expected pragma protect keyword"
warning unknown-protect-keyword UnknownProtectKeyword "unknown pragma protect keyword '{}'"
warning expected-protect-arg ExpectedProtectArg "'{}' pragma expects a single string argument"
warning nested-protect-begin NestedProtectBegin "nested pragma protect begin-end blocks are not allowed"
warning extra-protect-end ExtraProtectEnd "pragma protect '{}' seen without corresponding start of block"
warning protect-arglist ProtectArgList "protect {} directive expects a parenthesized list of key=value options"
warning unknown-protect-encoding UnknownProtectEncoding "unknown protect encoding '{}', treating as 'raw' -- source text may not be processed correctly"
warning unknown-protect-option UnknownProtectOption "unknown protect {} option '{}'"
warning invalid-pragma-number InvalidPragmaNumber "expected a 32-bit integer value for pragma option"
warning invalid-pragma-viewport InvalidPragmaViewport "pragma protect viewport requires options to be specified in the form (object = <string>, access = <string>)"
warning protected-envelope ProtectedEnvelope "protected envelopes cannot be decrypted and will be skipped entirely"

subsystem Parser
error ExpectedIdentifier "expected identifier"
error ExpectedStringLiteral "expected string literal"
error ExpectedIntegerLiteral "expected integer literal"
error ExpectedToken "expected '{}'"
error MisplacedTrailingSeparator "misplaced trailing '{}'"
error ImplicitNotAllowed "expected data type (implicit type name not allowed)"
error InvalidAccessDotColon "invalid access token; '{}' should be '{}'"
error ExpectedMember "expected member"
error ExpectedStatement "expected statement"
error ExpectedParameterPort "expected parameter declaration"
error ExpectedNonAnsiPort "expected non-ansi port declaration"
error ExpectedAnsiPort "expected ansi port declaration"
error ExpectedModportPort "expected modport item port declaration"
error ExpectedFunctionPort "expected subroutine port declaration"
error ExpectedAssertionItemPort "expected assertion item construct port declaration"
error ExpectedForInitializer "expected for loop initializer"
error ExpectedValueRangeElement "expected value range element"
error ExpectedStreamExpression "expected stream expression"
error ExpectedArgument "expected argument"
error ExpectedDeclarator "expected a declaration name"
error ExpectedConditionalPattern "expected conditional pattern"
error ExpectedAttribute "expected attribute"
error ExpectedPackageImport "expected package import"
error ExpectedHierarchicalInstantiation "expected hierarchical instantiation"
error ExpectedPortConnection "expected port connection"
error ExpectedVariableAssignment "expected variable assignment"
error ExpectedContinuousAssignment "expected continuous assignment"
error ExpectedInterfaceClassName "expected interface class name"
error ExpectedAssignmentKey "expected assignment key"
error ExpectedDistItem "expected dist item"
error ExpectedIfOrCase "expected 'if' or 'case' after '{}' keyword"
error ExpectedNetStrength "expected net strength keyword"
error ExpectedEnumBase "expected enum base type"
error ExpectedCaseItem "expected case item"
error ExpectedSubroutineName "expected subroutine name"
error ExpectedConstraintName "invalid constraint block name"
error ExpectedUdpPort "expected UDP port"
error ExpectedUdpSymbol "expected UDP symbol"
error ExpectedPathOp "expected path operator ('*>' or '=>')"
error ExpectedEdgeDescriptor "expected edge descriptor"
error ExpectedPortList "expected parameter list or port list"
error ExpectedRsRule "expected randsequence production rule"
error ExpectedPattern "expected pattern"
error ExpectedFunctionPortList "expected function port list"
error NoLabelOnSemicolon "labels are not allowed on empty semicolon"
error DeferredDelayMustBeZero "deferred assertion delay must be zero"
error InvalidGenvarIterExpression "invalid genvar iteration expression"
error ExpectedGenvarIterVar "expected genvar iteration variable"
error ConstFunctionPortRequiresRef "'const' in subroutine formal port requires 'ref' direction specifier"
error ExpectedClockingSkew "expected clocking skew"
error InOutDefaultSkew "inout direction for default skew not allowed"
error GlobalClockingEmpty "global clocking block cannot declare items"
error ClockingNameEmpty "expected clocking block name"
error ExpectedDPISpecString "expected DPI spec string"
error ParseTreeTooDeep "language constructs are too deeply nested"
error MissingModportPortDirection "modport port declaration is missing direction specifier"
error CaseStatementEmpty "{} statement has no items"
error MultipleDefaultCases "{} statement has multiple default cases"
error DirectionOnInterfacePort "direction specifier on interface port is not allowed"
error UnexpectedQualifiers "unexpected qualifiers on class member"
error ExpectedSampleKeyword "expected 'sample' keyword"
error MultipleGenerateDefaultCases "case generate has multiple default cases"
error CaseGenerateEmpty "case generate has no items"
error LabelAndName "cannot specify both a label and a name for the same block"
error EndNameMismatch "'{}' doesn't match previous name '{}'"
error EndNameNotEmpty "trailing name not allowed on previously unnamed block"
error DeclarationsAtStart "declaration must come before all statements in the block"
error VoidNotAllowed "'void' type not allowed"
error DuplicateDeclModifier "duplicate '{}' modifier in declaration"
error DeclModifierOrdering "modifier '{}' must come before '{}' in a declaration"
error DeclModifierConflict "modifier '{}' cannot be used together with '{}' in a declaration"
error DuplicateQualifier "duplicate '{}' qualifier in declaration"
error QualifierConflict "qualifier '{}' cannot be used together with '{}' in a declaration"
error QualifierNotFirst "'{}' must come first in a list of qualifiers"
error PureRequiresVirtual "'pure' keyword must be followed by 'virtual'"
error InvalidMethodQualifier "invalid method qualifier '{}'"
error InvalidPropertyQualifier "invalid property qualifier '{}'"
error InvalidConstraintQualifier "invalid constraint qualifier '{}'"
error InvalidQualifierForMember "invalid qualifier for member"
error InvalidQualifierForIfaceMember "invalid qualifier for interface class member"
error InvalidQualifierForConstructor "invalid qualifier for constructor"
error MethodStaticLifetime "class methods cannot have static lifetime"
error TypeRefDeclVar "a type reference cannot be used in a variable declaration without a preceeding 'var' keyword"
error ScopedClassCopy "typed constructor calls are not allowed for a shallow copy"
error NotAllowedInCU "member not allowed at compilation unit scope"
error NotAllowedInGenerate "member not allowed in generate block"
error NotAllowedInModule "member not allowed in module declaration"
error NotAllowedInInterface "member not allowed in interface declaration"
error NotAllowedInProgram "member not allowed in program declaration"
error NotAllowedInAnonymousProgram "member not allowed in anonymous program declaration"
error NotAllowedInPackage "member not allowed in package"
error NotAllowedInClocking "member not allowed in clocking block"
error NotAllowedInClass "member not allowed in class declaration"
error NotAllowedInIfaceClass "member not allowed in interface class declaration"
error NotAllowedInChecker "member not allowed in checker declaration"
error TaskReturnType "tasks cannot declare a return type"
error TaskConstructor "class constructors cannot be tasks"
error ConstructorReturnType "class constructors cannot declare a return type"
error NewKeywordQualified "invalid scope access on 'new' keyword"
error SubroutinePrototypeScoped "subroutine prototype cannot have a scoped name"
error QualifiersOnOutOfBlock "qualifiers are not allowed on out-of-block method definitions"
error ConstraintQualOutOfBlock "'{}' qualifier is not allowed on out-of-block constraint definitions"
error ConstructorOutsideClass "constructor cannot be declared outside of a class"
error InvalidSuperNew "super class constructor can only be called from the first statement in a derived class's constructor"
error IfaceMethodPure "method in interface class must be marked 'pure'"
error NestedIface "interface classes cannot be nested"
error RegAfterNettype "net type keyword cannot be followed by the 'reg' keyword"
error ChargeWithTriReg "charge strength can only be specified for 'trireg' nets"
error InitializerRequired "initializer expression required"
error DriveStrengthInvalid "combination of drive strengths is invalid; need one for value '0' and one for '1'"
error DriveStrengthHighZ "combination of drive strengths is invalid; cannot combine 'highz0' and 'highz1'"
error PullStrengthHighZ "pullup and pulldown strengths cannot be 'highz0' or 'highz1'"
error InvalidPullStrength "invalid strength for '{}' gate"
error DriveStrengthNotAllowed "drive strengths are not allowed on '{}' gates"
error DelaysNotAllowed "delays are not allowed on '{}' gates"
error Delay3NotAllowed "'{}' gates can have a maximum of two delay values"
error Delay3UdpNotAllowed "instances of user-defined primitives can have a maximum of two delay values"
error RandOnPackedMember "packed members cannot have random modifiers"
error RandOnUnionMember "members of unions cannot have random modifiers"
error SolveBeforeDisallowed "constraint ordering directive is not allowed within a nested constraint block"
error InvalidPackageDecl "invalid package declaration"
error InvalidRepeatRange "invalid repetition range"
error InvalidPropertyIndex "only a single count of cycles is allowed for '{}' expression"
error InvalidPropertyRange "expected a range of cycles for '{}' expression"
error WireDataType "expected a data type here; 'wire' is not a data type, it's a net type"
error InvalidDelayValue "invalid delay value expression"
error AssertionPortDirNoLocal "assertion port cannot specify direction without also using the 'local' keyword"
error AssertionPortPropOutput "local variable formal arguments in properties cannot have direction 'output' or 'inout'"
error AssertionPortRef "assertion item port direction cannot be 'ref'"
error CheckerPortInout "checker port direction cannot be 'inout'"
error UnexpectedLetPortKeyword "unexpected use of '{}' in 'let' port declaration"
error PackedDimsOnUnpacked "unpacked types cannot have packed dimensions"
error UnpackedSigned "unpacked type cannot have signedness declared"
error TaggedStruct "structs cannot be declared '{}'"
error RandJoinProdItem "rand join list can only contain simple references to other productions"
error RandJoinNotEnough "rand join list must contain at least two productions"
error InvalidDistExpression "cannot use a 'dist' expression in this context"
error InvalidForInitializer "for loop initializer must be an assignment expression"
error CoverCrossItems "cover cross declaration requires at least two coverage items"
error InvalidCoverageOption "invalid coverage option assignment"
error CoverageBinDefaultWildcard "default coverage bins cannot be marked 'wildcard'"
error CoverageBinDefSeqSize "default sequence bin cannot be declared as an array"
error CoverageBinDefaultIgnore "default coverage bins cannot be ignored"
error CoverageBinTransSize "transition coverage bins cannot have an explicit size"
error InterconnectTypeSyntax "interconnect nets cannot have an explicit type"
error InterconnectDelaySyntax "interconnect net delays can only be a single value"
error InterconnectInitializer "interconnect nets cannot have initializers"
error InvalidEdgeDescriptor "invalid edge descriptor (expected 01, 10, or x/z transitions)"
error MultipleParallelTerminals "cannot specify multiple terminals for parallel path connection"
error WrongSpecifyDelayCount "expected one, two, three, six, or twelve delay values assigned to module path"
error IfNoneEdgeSensitive "ifnone specify path cannot be an edge-sensitive path"
error PulseControlSpecifyParent "pulse control specparams can only be declared in specify blocks"
error PulseControlPATHPULSE "pulse control specparam names must start with 'PATHPULSE$'"
error TooManyEdgeDescriptors "cannot have more than six pairs of edge transitions in list"
error EdgeDescWrongKeyword "edge descriptor list cannot be used after '{}'"
error BindDirectiveInvalidName "bind target name must be a simple module or interface identifier when a list of instances is provided"
error UdpInvalidTransition "transition specifiers are only allowed in input fields"
error UdpInvalidSymbol "invalid symbol '{}' in state table"
error UdpTransitionLength "transition specifier requires exactly two symbols"
error UdpInvalidEdgeSymbol "invalid symbol '{}' in edge transition specifier"
error UdpSingleChar "state table entry can only be a single symbol"
error UdpInvalidInputOnly "'{}' is only allowed in input fields of sequential primitives"
error UdpInvalidMinus "'-' is only allowed in the output field of a sequential primitive"
error UdpInvalidOutput "'{}' is not allowed in output field of state table"
error UdpDupTransition "cannot have more than one input transition per row in a state table"
error UdpTransSameChar "edge transition must be between two different values"
error UdpEdgeInComb "edge transition can only be used in a sequential primitive -- are you missing a 'reg' specifier on the output port?"
error UdpCombState "combinational primitive row should not have a current state field"
error UdpSequentialState "sequential primitive row is missing a current state field"
error ExternWildcardPortList "extern declaration cannot have a wildcard port list"
error UniquePriorityAfterElse "cannot use 'unique' or 'priority' keyword after 'else'"
error MultipleDefaultRules "configuration cannot have more than one default rule"
error ConfigMissingName "cannot use 'config' without also specifying a target name"
error ConfigParamsOrdered "parameter assignments in configurations must use explicit name notation, not positional notation"
error ConfigSpecificCellLiblist "cannot specify a liblist when using a cell rule that targets a specific library already"
error ConfigParamLiteral "parameters in configurations can only be set to literal values"
error NoCommaInList "this list is delimited by spaces, not commas"
error ExpectedClassSpecifier "expected class specifier"
error DefaultArgNotAllowed "'default' argument is not allowed here"
error SpecifiersNotAllowed "class override specifiers are not allowed here"
error DuplicateClassSpecifier "duplicate '{}' specifier"
error ClassSpecifierConflict "cannot specify both '{}' and '{}' on the same declaration"
error FinalSpecifierLast "'final' specifier must come last in a list of class override specifiers"
error FinalWithPure "'final' specifier cannot be used with a pure virtual member"
error StaticFuncSpecifier "static members cannot have override specifiers"
error OverridingExtends "'{}' is marked 'extends' but doesn't override a base class virtual member"
error DerivedCovergroupNotInClass "covergroup inheritance can only be used inside a class"
error DerivedCovergroupNoBase "covergroup is marked 'extends' but is in a class that has no base class"
error UnexpectedEndDelim "unexpected '{}' delimiter"
error InvalidSelectExpression "select expression is not allowed here"
error ImplicitEventInAssertion "implicit event list not allowed in sequence and property expressions"
error EventTriggerCycleDelay "cycle delay not allowed in event trigger statements"
warning dpi-pure-task DPIPureTask "DPI tasks cannot be marked 'pure'"
warning nonstandard-generate NonStandardGenBlock "standalone generate block without loop or condition is not allowed in SystemVerilog"
warning empty-pattern EmptyAssignmentPattern "empty assignment patterns are disallowed by SystemVerilog"
warning nonstandard-foreach NonstandardForeach "non-standard foreach loop variable list"
warning lifetime-prototype LifetimeForPrototype "lifetime specifier is not allowed on prototype declaration"
warning nonstandard-dist NonstandardDist "use of parentheses around 'dist' expression is non-standard"
warning empty-body EmptyBody "{} has empty body (put the semicolon on a separate line to silence this warning)"
warning split-distweight-op SplitDistWeightOp "split dist weight operator is not allowed; SystemVerilog requires that they be joined together"
note NoteToMatchThis "to match this '{}'"
note NoteLastBlockStarted "last complete block started here"
note NoteLastBlockEnded "and ended here"

subsystem Declarations
error LocalParamNoInitializer "local parameter is missing an initializer"
error BodyParamNoInitializer "parameter declaration is missing an initializer"
error InvalidDimensionRange "invalid dimension range"
error DimensionRequiresConstRange "dimension requires a constant range"
error PackedDimsRequireFullRange "packed dimensions require a full range specification"
error MixingOrderedAndNamedParams "mixing ordered and named parameter assignments is not allowed"
error DuplicateParamAssignment "duplicate assignment for parameter '{}'"
error ParamHasNoValue "instance of '{}' does not provide a value for parameter '{}' and it does not have a default value"
error TooManyParamAssignments "too many parameter assignments given for '{}' ({} given, expected {})"
error AssignedToLocalPortParam "can't assign a value to a localparam"
error AssignedToLocalBodyParam "can't assign a value to a localparam (parameters in the body of a module are implicitly local when you have a parameter port list)"
error ParameterDoesNotExist "parameter '{}' does not exist in '{}'"
error BadTypeParamExpr "invalid initializer for type parameter '{}'"
error PortTypeNotInterfaceOrData "port type '{}' is neither an interface nor a data type"
error VarWithInterfacePort "'var' keyword may not be used with an interface port"
error DirectionWithInterfacePort "port direction not allowed on an interface port"
error InOutPortCannotBeVariable "variable port '{}' cannot have direction 'inout'"
error InOutUWirePort "'uwire' port '{}' cannot have direction 'inout'"
error RefPortMustBeVariable "ref port '{}' cannot be of net type"
error MissingPortIODeclaration "port '{}' has no I/O member declaration"
error CantDeclarePortSigned "'{}' cannot be declared '{}' because its type {} is not integral"
error PortDeclDimensionsMismatch "inconsistent dimensions declared for '{}'"
error PortDeclInANSIModule "can't use port declaration in module with ANSI style port list"
error UnusedPortDecl "port declaration '{}' does not match any port in the module's port list"
error UnknownPackage "unknown package '{}'"
error UnknownModule "unknown module '{}'"
error UnknownInterface "unknown interface '{}'"
error UnknownPrimitive "unknown user-defined primitive '{}'"
error InstanceNameRequired "instance name is required"
error InstanceWithStrength "cannot instantiate '{}' with a drive strength"
error InstanceWithDelay "missing '(' in parameter list"
error MixingOrderedAndNamedPorts "mixing ordered and named port connections is not allowed"
error DuplicateWildcardPortConnection "duplicate wildcard port connection"
error DuplicatePortConnection "duplicate connection for port '{}'"
error TooManyPortConnections "too many port connections given to instantiation of '{}' ({} given, expected {})"
error PrimitivePortCountWrong "wrong number of port connections for '{}' instance ({} given, expected {})"
error InvalidPrimitivePortConn "only simple expressions are allowed for primitive port connections"
error InvalidNGateCount "instance of '{}' requires at least one input and one output connection"
error PortDoesNotExist "port '{}' does not exist in '{}'"
error PortConnDimensionsMismatch "dimensions of connection for port '{}' do not match its declaration"
error InterfacePortNotConnected "interface port '{}' not connected"
error InterfacePortTypeMismatch "cannot connect instance of interface '{}' to port of interface '{}'"
error MismatchedTimeScales "duplicate time scale declaration must match previous value"
error TimeScaleFirstInScope "time scale declaration must come before all other items in scope"
error GenvarDuplicate "loop generation value {} repeats more than once"
error GenvarUnknownBits "value of {} is invalid for loop generation value"
error ImplicitNamedPortNotFound "could not find connection for implicit named port '{}'"
error WildcardPortGenericIface "cannot use wildcard connection for generic interface port '{}' (use a named connection instead)"
error MaxGenerateStepsExceeded "generate loop hit maximum step limit; possible infinite loop?"
error MixingSubroutinePortKinds "port declarations not allowed when subroutine defines a formal argument list"
error UnpackedArrayParamType "unpacked array parameter requires explicit data type"
error AutomaticNotAllowed "automatic variables can only be declared in procedural contexts"
error NoMemberImplFound "no implementation found in parent scope for '{}'"
error MethodKindMismatch "mismatch between 'function' and 'task' declared in prototype"
error MethodReturnMismatch "return type {} of method '{}' does not match {} declared in its prototype"
error MethodArgCountMismatch "definition of method '{}' has different number of arguments from its prototype"
error MethodArgNameMismatch "method argument name '{}' doesn't match '{}' given in its prototype"
error MethodArgTypeMismatch "method argument '{}' is of type {} which differs from {} given in its prototype"
error MethodArgNoDefault "method argument '{}' provides a default value but its prototype does not"
error MethodArgDefaultMismatch "default value for method argument '{}' must exactly match the expression given in its prototype"
error MethodArgDirectionMismatch "direction of method argument '{}' differs from the direction given in its prototype"
error MemberDefinitionBeforeClass "definition of '{}' must come after its containing class '{}'"
error MethodReturnTypeScoped "return type for out-of-block method '{}' must use class scope '{}::' to access nested type"
error VirtualKindMismatch "mismatch between 'function' and 'task' declared in superclass method"
error VirtualVisibilityMismatch "virtual method '{}' is '{}' but declared '{}' in superclass method"
error VirtualReturnMismatch "return type {} of virtual method '{}' does not match {} declared in its superclass"
error VirtualArgCountMismatch "virtual method '{}' has different number of arguments from its superclass method"
error VirtualArgNameMismatch "virtual method argument name '{}' doesn't match '{}' given in its superclass method"
error VirtualArgTypeMismatch "virtual method argument '{}' is of type {} which differs from {} given in its superclass method"
error VirtualArgNoParentDefault "virtual method argument '{}' provides a default value but its superclass method argument does not"
error VirtualArgNoDerivedDefault "virtual method argument '{}' does not provide a default value but its superclass method argument does"
error VirtualArgDirectionMismatch "direction of virtual method argument '{}' differs from the direction given in its superclass method"
error BaseConstructorDuplicate "cannot provide both 'super.new' as well as class constructor arguments in 'extends' clause"
error BaseConstructorNotCalled "class '{}' must provide arguments for constructing base class '{}' via 'super.new' or the 'extends' clause"
error ConstVarNoInitializer "constant variable must have an initializer"
error StaticConstNoInitializer "static constant class properties must have initializers"
error PureInAbstract "pure virtual methods can only be declared in virtual classes"
error PureConstraintInAbstract "pure constraints can only be declared in virtual classes"
error BodyForPure "pure virtual methods cannot have a function body defined"
error BodyForPureConstraint "pure constraints cannot have a body defined"
error InheritFromAbstract "'{}' cannot inherit from virtual class '{}' without providing an implementation for pure virtual method '{}'"
error InheritFromAbstractConstraint "'{}' cannot inherit from virtual class '{}' without providing an implementation for pure constraint '{}'"
error InvalidPortType "{} is not a valid type for a port"
error InvalidPortSubType "{} is not a valid type for a port because it contains type {}"
error InvalidNetType "{} is not a valid type for a net; only 4-state integral types and unpacked structs/unions and arrays of such types are allowed"
error InvalidUserDefinedNetType "{} is not a valid type for a user-defined nettype; only integral types, floating types, and unpacked structs, unions, and arrays of such types are allowed"
error ImplicitConnNetInconsistent "net type '{}' of implicit named connection is inconsistent with net type '{}' of port"
error ExtendIfaceFromClass "a normal (or virtual) class cannot extend interface class '{}'"
error ExtendClassFromIface "an interface class cannot extend non-interface class '{}'"
error ExtendFromFinal "cannot extend '{}' because it is marked 'final'"
error ImplementNonIface "cannot implement non-interface class '{}'"
error IfaceNameConflict "'{}' in '{}' conflicts with another member already inherited from '{}'"
error IfaceMethodHidden "'{}' hides method inherited from '{}'"
error IfaceMethodNoImpl "class '{}' must provide an implementation for method '{}' specified in interface class '{}'"
error IfaceMethodNotVirtual "class '{}' must provide an implementation for method '{}' specified in interface class '{}' (existing method is not virtual)"
error NotAllowedInModport "'{}' cannot be listed in a modport"
error ModportConnMismatch "cannot connect modport '{}.{}' to port requiring modport '{}.{}'"
error ExpectedImportExport "expected 'import' or 'export' for subroutine '{}' in modport"
error SingleBitVectored "net of width 1 cannot be 'vectored' or 'scalared'"
error VarDeclWithDelay "cannot specify a net delay for a variable declaration"
error InvalidRandType "{} is not a valid type for a '{}' property"
error InvalidMethodOverride "cannot override built-in method '{}'"
error InvalidRandomizeOverride "override for '{}' should be a public non-static function that returns void and takes no parameters"
error MismatchStaticConstraint "mismatch of 'static' keyword between constraint prototype and declaration"
error MismatchConstraintSpecifiers "mismatch of override specifiers between constraint prototype and declaration"
error DPIRefArg "DPI subroutines cannot have 'ref' arguments"
error DPIPureArg "DPI imports marked 'pure' cannot have 'output' or 'inout' arguments"
error DPIPureReturn "DPI imports marked 'pure' cannot return 'void'"
error InvalidDPIReturnType "{} is not a valid return type for a DPI subroutine"
error InvalidDPIArgType "{} is not a valid argument type in a DPI subroutine"
error Delay3OnVar "continuous assignments to variables cannot have multi-value delays"
error DefParamTarget "target of defparam must be a parameter"
error DefParamLocal "'{}' is a localparam and so cannot be the target of a defparam"
error PrimitivePortUnknown "port declaration '{}' does not match any port in the primitive's port list"
error PrimitivePortDup "port '{}' already declared"
error PrimitivePortMissing "port '{}' is missing a corresponding body declaration"
error PrimitiveRegDup "only one 'reg' port is allowed per primitive"
error PrimitiveRegInput "input port '{}' cannot be declared as a 'reg'"
error PrimitiveTwoPorts "primitives must have at least one input and one output port"
error PrimitiveOutputFirst "the first port in a primitive must be an output port"
error PrimitiveDupOutput "primitives must have one and only one output port"
error PrimitiveAnsiMix "primitives cannot mix an ansi-style header with port declarations inside their bodies"
error PrimitiveInitialInComb "combinational primitives cannot contain an initial statement"
error PrimitiveDupInitial "primitive cannot have both an initial statement and an output initializer"
error PrimitiveWrongInitial "'{}' is not this primitive's output port"
error PrimitiveInitVal "initial value for a primitive output must be a single-bit binary literal"
error RefArgAutomaticFunc "'ref' arguments can only be used in 'automatic' subroutines"
error InvalidInstanceForParent "cannot instantiate {} in {}"
error InvalidPrimInstanceForParent "cannot instantiate a primitive in this scope"
error NullPortExpression "expression given for null port"
error PackageNetInit "net declarations in packages cannot have assignments"
error MultipleDefaultInputSkew "default input skew already defined for this clocking block"
error MultipleDefaultOutputSkew "default output skew already defined for this clocking block"
error InvalidClockingSignal "clocking signal '{}' must refer to a net or variable"
error GlobalClockingGenerate "global clocking cannot be declared in a generate block"
error PropertyPortInSeq "port of type 'property' is not allowed in a sequence"
error PropertyPortInLet "port of type '{}' is not allowed in a 'let' declaration"
error LocalVarTypeRequired "local variable argument must specify an explicit type"
error AssertionPortOutputDefault "assertion port with 'output' or 'inout' direction cannot specify a default value"
error NotAProduction "'{}' is not a randsequence production"
error RandJoinNotNumeric "rand join expression type {} is not numeric"
error NotAClockingBlock "'{}' is not a clocking block"
error CovergroupOutArg "covergroups cannot have 'output' or 'inout' arguments"
error CoverageOptionDup "duplicate option assignment for '{}'"
error CoverageSetType "invalid set covergroup expression type {} -- expected non-associative array of coverpoint type {}"
error CoverageBinTargetName "expected 'with' target name to match the name of the parent coverpoint"
error ConstPortNotAllowed "'const' is not allowed on port declarations"
error IfacePortInExpr "invalid use of interface port '{}' in port expression"
error PortConcatInOut "port concatentation can't contain any 'inout' ports because not all of the ports are resolvable nets"
error PortConcatRef "port concatentation can't contain any 'ref' ports because not all of the ports are variables"
error DisallowedPortDefault "only 'output' variable port declarations may have an initializer expression"
error AnsiIfacePortDefault "interface port cannot have an initializer expression"
error RefPortUnconnected "'ref' port '{}' cannot be left unconnected"
error RefPortUnnamedUnconnected "an unnamed 'ref' port has been left unconnected"
error MismatchedUserDefPortConn "user-defined nettype '{}' on one side of this connection means that the type of the port {} must match the type of the connection {}"
error MismatchedUserDefPortDir "user-defined nettype '{}' on one side of this connection means that the port must have 'input' or 'output' direction"
error UserDefPortTwoSided "user-defined nettype '{}' of the port does not match '{}' of the connection"
error UserDefPortMixedConcat "cannot mix and match nets with user-defined nettypes in port concatenation (see '{}' and '{}')"
error InterconnectMultiPort "cannot use interconnect nets in a port concatenation"
error InterconnectPortVar "cannot connect '{}' to an interconnect port"
error IfaceMethodNotExtern "cannot provide an implementation for an interface method '{}' that is not declared 'extern'"
error DupInterfaceExternMethod "more than one implementation provided for extern {} {}.{}"
error ExternFuncForkJoin "only extern tasks can be declared 'forkjoin'"
error InstanceMissingParens "{} instantiation is missing port list parentheses"
error MaxInstanceArrayExceeded "{} array exceeded maximum size of {}"
error ConstraintNotInClass "constraint block can only be declared inside a class"
error InvalidSpecifyPath "specify path must be a simple reference to a module port signal"
error SpecifyPathMultiDim "specify path references can only have a single select dimension"
error InvalidSpecifyType "specify path terminal '{}' has non-integral type {}"
error InvalidSpecifySource "specify path source '{}' must be an 'input' or 'inout' net or modport port"
error InvalidSpecifyDest "specify path destination '{}' must be an 'output' or 'inout' net, variable, or modport port"
error ParallelPathWidth "parallel specify path source type {} has different width than destination type {}"
error UnknownSystemTimingCheck "unknown system timing check '{}'"
error TimingCheckEventNotAllowed "timing check event expression not allowed for this argument"
error InvalidTimingCheckNotifierArg "invalid timing check argument; expecting a notifier variable name"
error TimingCheckEventEdgeRequired "'{}' event argument requires an edge trigger"
error NoChangeEdgeRequired "trigger for first argument to '$nochange' must be posedge or negedge only"
error PathPulseInvalidPathName "PATHPULSE$ path '{}' must be of the form 'source$dest'"
error VirtualInterfaceIfaceMember "virtual interfaces cannot be used as the type for interface items"
error DefparamBadHierarchy "defparam in a hierarchy in or under a generate block, array of instances, or bind instantiation cannot change a parameter value outside that hierarchy"
error BindTargetPrimitive "cannot bind a primitive"
error BindUnderBind "cannot bind an instance underneath the scope of another bind instance"
error DuplicateBind "bind directive targeting the same scope instantiated more than once"
error UdpWrongInputCount "incorrect number of input fields in table row; have {} but expect {}"
error UdpDupDiffOutput "primitive table row duplicates a set of inputs with a different specified output value"
error UdpAllX "primitive table row has all 'x' inputs so its output must also be 'x'"
error SubroutinePortInitializer "subroutine port declaration cannot contain an initializer"
error CheckerPortDirectionType "checker ports with an explicit direction declared must also declare a type"
error CheckerOutputBadType "checker output ports cannot be of type 'untyped', 'sequence', or 'property'"
error CheckerClassBadInstantiation "class '{}' cannot be instantiated this way -- are you missing a 'new' expression?"
error CheckerFuncBadInstantiation "invalid syntax for calling '{}'"
error NotAChecker "invalid reference to '{}' -- this looks like a module/interface/program/checker instantiation but '{}' is none of those things"
error CheckerParameterAssign "checkers cannot have parameter assignments"
error CheckerInCheckerProc "checkers cannot be instantiated inside the procedures of other checkers"
error InvalidStmtInChecker "statement is not allowed in '{}' procedure inside a checker"
error AlwaysInChecker "always procedures are not allowed in checkers (use always_comb, always_latch, or always_ff)"
error CheckerTimingControl "procedural timing control statements in checkers can only use event controls"
error CheckerBlockingAssign "blocking assignments in checkers can only occur in always_comb and always_latch procedures"
error CheckerFuncArg "cannot call a function with output, inout, or ref arguments in a checker variable assignment"
error NetAliasWidthMismatch "all aliased nets must have the same width; expression has width of {}, previously seen width was {}"
error NetAliasHierarchical "cannot use hierarchical references in net alias statements"
error NetAliasNotANet "'{}' is not a net and so cannot be used in a net alias statement"
error NetAliasCommonNetType "all nets in a net alias statement must have a common nettype -- '{}' has nettype '{}' but previously saw '{}'"
error MissingExternWildcardPorts "could not find extern declaration for '{}' from which to take port list"
error ExternIfaceArrayMethod "cannot connect extern subroutine to interface array"
error ConfigInstanceWrongTop "config instance path must start with one of the cells named in the 'design' statement"
error ConfigDupTop "config design specifies more than one top cell named '{}'"
error ConfigOverrideTop "config rule can't override a top cell with a different target"
error ConfigInstanceUnderOtherConfig "config instance rule applies to an instance that is within a hierarchy specified by another config"
error ConfigParamsForPrimitive "cannot provide parameter assignments for primitive instance"
error TypeRestrictionMismatch "type restriction '{}' does not match assigned type {}"
error MultipleDefaultConstructorArg "'default' cannot be used more than once in constructor argument list"
error DefaultSuperArgLocalReference "cannot use 'default' here because the superclass constructor argument '{}' refers to local member '{}'"
error OverridingInitial "'{}' is marked 'initial' but overrides a base class member"
error OverridingFinal "'{}' overrides a base class member that is marked 'final'"
error PackageImportSelf "package cannot import itself"
error PackageExportSelf "package cannot export itself"
error UnknownCovergroupBase "'{}' is not a covergroup in '{}'"
error RealCoverpointBins "coverpoints of real expressions must specify at least one explicit bin"
error RealCoverpointDefaultArray "default bin for coverpoint of real expression cannot be an array"
error RealCoverpointWithExpr "'with' expressions are not allowed for bins of a real type"
error RealCoverpointTransBins "coverpoints of real expressions cannot specify transition bins"
error RealCoverpointWildcardBins "coverpoints of real expressions cannot be marked 'wildcard'"
error RealCoverpointImplicit "cannot declare implicit coverpoint for real value '{}'"
error BiDiSwitchNetTypes "bidirectional switch cannot connect nets of two different user-defined net types or a user-defined net type and a built-in net type ('{}' and '{}')"
error FatalTask "$fatal encountered{}"
error ErrorTask "$error encountered{}"
error StaticAssert "static assertion failed{}"
warning warning-task WarningTask "$warning encountered{}"
note InfoTask "$info encountered{}"
note NoteComparisonReduces "comparison reduces to ({} {} {})"
note NoteCommonAncestor "common ancestor here violates that constraint"
warning udp-coverage UdpCoverage "primitive does not specify outputs for all edges of all inputs"
note NoteUdpCoverage "missed desired output for rows:\n{}"
warning explicit-static StaticInitializerMustBeExplicit "initializing a static variable in a procedural context requires an explicit 'static' keyword"
warning case-gen-dup CaseGenerateDup "more than one case generate block matches the value {}"
warning case-gen-none CaseGenerateNoBlock "no case generate expression matches the value {}"
warning unconnected-port UnconnectedNamedPort "port '{}' has no connection"
warning unconnected-unnamed-port UnconnectedUnnamedPort "instance does not provide a connection for an unnamed port"
warning implicit-net-port ImplicitNetPortNoDefault "implicit net port disallowed because `default_nettype is set to 'none'"
warning dup-attr DuplicateAttribute "duplicate attribute definition '{}'; taking last value"
warning empty-member EmptyMember "extra ';' has no effect"
warning ineffective-sign SignednessNoEffect "'{}' on I/O declaration has no effect on type {}"
warning constraint-missing NoConstraintBody "no body for implicit constraint block '{}'"
warning dpi-spec DPISpecDisallowed "'DPI' specified subroutines are not supported and will be treated as 'DPI-C'. Note that this may require changes to the application's C code."
warning udp-port-empty EmptyUdpPort "primitive port connection is empty"
warning net-inconsistent NetInconsistent "net type '{}' of connection is inconsistent with net type '{}' of port"
warning net-inconsistent NetRangeInconsistent "net type '{}' of connection bits [{}:{}] is inconsistent with net type '{}' of corresponding port range"
warning dup-timing-path DupTimingPath "duplicate timing path"
warning invalid-pulsestyle InvalidPulseStyle "invalid use of {} for '{}' since it has previously appeared in a path declaration"
warning implicit-port-type-mismatch ImplicitNamedPortTypeMismatch "implicit named port '{}' of type {} connects to value of inequivalent type {}"
warning unknown-library WarnUnknownLibrary "unknown library '{}'"
warning dup-config-rule DupConfigRule "duplicate config rule for {} -- ignoring this one"
warning unused-config-cell UnusedConfigCell "unused cell config rule"
warning unused-config-instance UnusedConfigInstance "unused config rule -- instance path does not exist in the design"
warning static-init-order StaticInitOrder "initializer for static variable '{}' refers to another static variable '{}' which will be initialized in an indeterminate order"
warning static-init-value StaticInitValue "initializer for static variable '{}' refers to '{}' which will not have a value at initialization time"

subsystem Expressions
error BadUnaryExpression "invalid operand type {} to unary expression"
error BadBinaryExpression "invalid operands to binary expression ({} and {})"
error BadConditionalExpression "invalid operands to conditional expression ({} and {})"
error BadIndexExpression "value of type {} cannot be indexed"
error BadSliceType "value of type {} cannot be sliced"
error BadConcatExpression "invalid operand type {} in concatenation"
error BadReplicationExpression "invalid operands to replication expression ({} and {})"
error BadValueRange "invalid bounds in value range ({} and {})"
error BadCastType "invalid casting type {}"
error BadSetMembershipType "invalid type {} for {} expression"
error ConcatWithStringInt "cannot mix strings and integers in a concatenation (use a cast if this is desired)"
error RangeWidthOverflow "select range overflows the bounds of a signed 32-bit integer"
error RangeSelectAssociative "cannot take a slice of an associative array"
error CannotIndexScalar "scalar type cannot be indexed"
error ExprMustBeIntegral "expression type {} is not integral"
error IndexValueInvalid "cannot refer to element {} of {}"
error SelectEndianMismatch "endianness of selection must match declared range (type is {})"
error SelectEndianDynamic "range of selection [{}:{}] into {} is reversed"
error BadAssignment "value of type {} cannot be assigned to type {}"
error BadConversion "value of type {} cannot be converted to type {}"
error BadIntegerCast "cannot change width or signedness of non-integral expression (type is {})"
error NoImplicitConversion "no implicit conversion from {} to {}; explicit conversion exists, are you missing a cast?"
error TooManyArguments "too many arguments for '{}'; expected {} but {} were provided"
error TooFewArguments "too few arguments for '{}'; expected {} but {} were provided"
error ExpressionNotAssignable "expression is not assignable"
error ReplicationZeroOutsideConcat "replication constant can only be zero inside of a concatenation"
error InvalidMemberAccess "invalid member access for type {}"
error ExpressionNotCallable "expression is not callable"
error UnexpectedWithClause "unexpected 'with' clause"
error WithClauseNotAllowed "cannot use 'with' expression with '{}'"
error IteratorArgsWithoutWithClause "cannot provide arguments to '{}' without corresponding 'with' clause"
error ExpectedIteratorName "expected name of iterator for use with 'with' expression"
error UnexpectedConstraintBlock "unexpected constraint block"
error MissingConstraintBlock "missing inline constraint block for 'randomize' call"
error ExpectedIterationExpression "expected a single array iteration expression"
error NotBooleanConvertible "value of type {} is not convertible to a boolean predicate"
error EnumValueSizeMismatch "expression width of {} does not exactly match declared enum type width of {}"
error NoCommonComparisonType "{} expression of type {} is not comparable to common type {}"
error AssignmentPatternNoContext "assignment pattern target type cannot be deduced in this context"
error BadAssignmentPatternType "invalid target type {} for assignment pattern"
error WrongNumberAssignmentPatterns "assignment pattern for {} requires {} elements but {} were provided"
error AssignmentPatternKeyExpr "expression is not a valid assignment pattern member name or type"
error AssignmentPatternKeyDupDefault "assignment pattern has multiple default keys"
error AssignmentPatternKeyDupValue "assignment pattern has multiple keys for index {}"
error AssignmentPatternKeyDupName "assignment pattern has multiple keys for member '{}'"
error AssignmentPatternNoMember "member '{}' is not covered by any assignment pattern key"
error AssignmentPatternMissingElements "not all elements of array are covered by an assignment pattern key"
error AssignmentPatternDynamicDefault "assignment pattern for dynamic array or queue cannot have default key"
error AssignmentPatternDynamicType "assignment patterns for dynamic arrays, associative arrays, and queues cannot have type keys"
error AssignmentPatternAssociativeType "assignment pattern for associative array must specify key:value pairs"
error EmptyArgNotAllowed "empty argument not allowed"
error NamedArgNotAllowed "named argument not allowed"
error DuplicateArgAssignment "duplicate assignment for argument '{}'"
error MixingOrderedAndNamedArgs "ordered arguments are not allowed after named arguments"
error ArgDoesNotExist "argument '{}' does not exist in '{}'"
error ArgCannotBeEmpty "argument '{}' cannot be empty because it does not have a default value"
error CheckerArgCannotBeEmpty "checker port connection for '{}' cannot be empty"
error UnconnectedArg "argument '{}' is missing a value"
error MissingReturnValue "must provide a return value for non-void function"
error MissingReturnValueProd "must provide a return value for non-void production"
error AssignmentNotAllowed "assignment expressions are not allowed in this context"
error IncDecNotAllowed "increment and decrement expressions are not allowed in this context"
error AssignmentRequiresParens "assignment expressions must be parenthesized"
error PortConnArrayMismatch "cannot connect {} to each port of type {} in '{}'"
error AutoFromStaticInit "cannot refer to automatic variable '{}' from static initializer"
error NonblockingAssignmentToAuto "nonblocking assignment to automatic variable '{}' is not allowed"
error AutoFromNonProcedural "cannot refer to automatic variable '{}' from non-procedural context"
error AutoFromNonBlockingTiming "cannot refer to automatic variable '{}' from intra-assignment event control of nonblocking assignment"
error CantModifyConst "constant '{}' is not modifiable"
error AssignmentToConstVar "cannot assign to read-only variable '{}'"
error BlockingAssignToFreeVar "continuous assignments and blocking procedural assignments to free checker variables are not allowed"
error EmptyConcatNotAllowed "concatenation cannot be empty"
error TimingControlNotAllowed "timing control is not allowed in this context"
error NewArrayTarget "new array expression can only be used in an assignment to a dynamic array type"
error NewClassTarget "new class expression can only be used in an assignment to a class type"
error UnpackedConcatSize "array concatenation for {} requires {} elements but {} were provided"
error UnpackedConcatAssociative "array concatenations cannot be used for associative arrays"
error InvalidClassAccess "cannot access '{}' in {} with '.'"
error CopyClassTarget "expected {} to be a class type for shallow copy expression"
error BadStreamSlice "streaming operator slice size cannot be a dynamically sized type identifier"
error BadStreamExprType "stream expression type {} is not a bit-stream type"
error BadStreamTargetType "target type {} of streaming concatenation is not a bit-stream type"
error BadStreamSourceType "source type {} of streaming concatenation is not a bit-stream type"
error BadStreamContext "streaming operator can only be used in an assignment or bit-stream cast argument"
error BadStreamSize "streaming operator target size {} does not fit source size {}"
error BadStreamCast "streaming concatenation cannot be converted to type {}"
error BadStreamWithType "stream expression type before 'with' must be a one-dimensional unpacked array"
error BadStreamWithOrder "'with' expression cannot appear after unconstrained dynamically sized stream expression"
error NewVirtualClass "cannot create instance of virtual class '{}'"
error NewInterfaceClass "cannot create instance of interface class '{}'"
error AssignToCHandle "cannot assign to a chandle outside of a procedural context"
error AssignToNet "cannot assign to a net within a procedural context"
error SelectOfVectoredNet "bit-selects and part-selects of vectored nets are disallowed"
error ExpectedNetDelay "invalid net delay; expected min:typ:max or single expression"
error InvalidRefArg "invalid expression for pass by reference; only variables, class properties, and members of unpacked structs and arrays are allowed"
error RefTypeMismatch "argument of type {} connects to 'ref' port of inequivalent type {}"
error ConstVarToRef "cannot bind const variable to 'ref' argument"
error AutoVarToRefStatic "cannot pass automatic variables or elements of dynamically sized arrays to 'ref static' argument"
error MissingInvocationParens "parentheses are required when invoking function '{}'"
error ChainedMethodParens "parentheses are required when chaining method calls"
error NameListWithScopeRandomize "lookup restriction list not allowed for std::randomize call"
error UnboundedNotAllowed "unbounded literal '$' not allowed here"
error DynamicNotProcedural "cannot refer to an element or member of a dynamic type outside of a procedural context"
error DynamicFromChecker "cannot refer to an element or member of a dynamic type from within a checker"
error CheckerForkJoinRef "checkers cannot refer to members of fork-join blocks from their enclosing scopes"
error SpecparamInConstant "specparams cannot be used in general constant expressions"
error TaskFromFunction "cannot invoke a task from within a function"
error TaskFromFinal "cannot invoke a task from within a final block"
error NonProceduralFuncArg "cannot call a function with output, inout, or ref arguments outside of a procedural context"
error EventExpressionFuncArg "cannot call a function with output, inout, or ref arguments from an event expression"
error AssertionFuncArg "cannot call a function with output, inout, or ref arguments from a concurrent assertion expression"
error CHandleInAssertion "cannot reference variable of type 'chandle' in concurrent assertion expressions"
error ClassMemberInAssertion "cannot reference non-static class members in concurrent assertion expressions"
error InvalidArgumentExpr "sequence and property expressions are not valid in this context"
error UnexpectedClockingExpr "unexpected expression after clocking event"
error ClockVarAssignConcat "clocking signal '{}' cannot be part of a concatenation or assignment pattern lvalue"
error CycleDelayNonClock "intra-assignment cycle delays can only be used with clocking signals"
error ClockVarBadTiming "clocking signals cannot be driven with a timing control other than a cycle delay"
error ClockVarOutputRead "cannot read from output clocking signal '{}'"
error AssertionArgNeedsRegExpr "cannot pass a sequence or property expression to an argument of type {}"
error AssertionArgTypeMismatch "argument of type {} is not cast compatible with formal port type {}"
error AssertionArgTypeSequence "expected a sequence expression for port of type 'sequence'"
error RecursiveSequence "sequence '{}' is recursive"
error RecursiveLet "let declaration '{}' is recursive"
error EventExprAssertionArg "argument of type 'event' can only be used in places where event expressions are allowed"
error AssertionDelayFormalType "a typed formal argument used in a cycle delay or sequence repetition must be of type 'shortint', 'int', or 'longint', not {}"
error AssertionOutputLocalVar "an argument bound to an 'inout' or 'output' local variable formal argument must itself be a reference to a local variable"
error LocalVarOutputEmptyMatch "sequence '{}' cannot have an 'inout' or 'output' local variable formal argument because it admits an empty match"
error AssertionPortTypedLValue "typed formal argument '{}' cannot be modified because it is not marked 'local'"
error RecursivePropTimeAdvance "recursive property instance must occur after a positive advance in time to avoid infinite recursion"
error RecursivePropNegation "recursive property instance cannot have the operators 'not', 's_nexttime', 's_eventually', 's_always', 's_until', or 's_until_with' applied to it"
error RecursivePropArgExpr "cannot refer to argument '{}' in an argument to a recursive property unless it is the entire argument expression or bound to a local variable formal argument"
error LetHierarchical "hierarchical references to let declarations are not allowed"
error TaggedUnionTarget "tagged union expression can only be used in an assignment to a tagged union type"
error TaggedUnionMissingInit "missing initializer expression for non-void tagged union member '{}'"
error CoverOptionImmutable "the '{}' option cannot be modified outside of the covergroup definition"
error ValueRangeUnbounded "value range cannot be unbounded on both sides"
error CoverageExprVar "coverage expressions can only reference constants and non-ref formal arguments"
error CoverageSampleFormal "sample formal argument '{}' can only be referenced in a coverpoint, cross, or conditional guard expression"
error InvalidBinsTarget "binsof target '{}' is not a coverpoint or coverpoint bin"
error InvalidBinsMatches "'matches' expression is invalid here"
error InOutVarPortConn "'{}' cannot be connected to 'inout' port (only nets are allowed)"
error InOutUWireConn "'uwire' net '{}' cannot be connected to 'inout' port"
error InterconnectReference "cannot reference interconnect net '{}' outside of a net port connection"
error GateUDNTConn "gate cannot connect to net '{}' with user-defined net type"
error UserDefPartialDriver "net '{}' with user-defined nettype cannot be partially driven"
error LocalVarEventExpr "local variable '{}' cannot be referenced in an event expression"
error RefArgForkJoin "cannot refer to 'ref' argument '{}' inside fork-join_any or fork-join_none block"
error PatternTaggedType "expected pattern type {} is not a tagged union"
error PatternStructType "structure pattern type {} is not a struct"
error PatternStructTooFew "structure pattern has too few members for target type {}"
error PatternStructTooMany "structure pattern has too many members for target type {}"
error SpecifyPathBadReference "cannot refer to '{}' in state-dependent path condition expression; only locally defined nets and variables are allowed"
error PathPulseInExpr "PATHPULSE$ specparams cannot be used in expressions"
error InterfacePortInvalidExpression "invalid expression for interface port connection"
error AssignmentPatternLValueDynamic "lvalue assignment patterns cannot be assigned from dynamic arrays"
error SelectAfterRangeSelect "cannot chain select expressions after a range select"
error ClassPrivateMembersBitstream "cannot use class type {} as a bit-stream because it has local or protected members that are not accessible in this context"
error CannotCompareTwoInstances "cannot compare two instances to each other; at least one side must be a virtual interface"
error InvalidSuperNewDefault "cannot use 'default' in super.new since the containing constructor does not use 'default' in its argument list"
error InvalidExtendsDefault "constructor must use 'default' in its argument list since 'extends' specifies 'default' for its argument list"
error NonblockingDynamicAssign "nonblocking assignments to elements of dynamically sized arrays are not allowed"
error VoidAssignment "expression of type 'void' cannot be used in assignments"
error VirtualIfaceDefparam "interface instance cannot be assigned to a virtual interface because it is the target of a defparam or bind directive"
error VirtualIfaceConfigRule "interface instance cannot be assigned to a virtual interface because it is the target of config rule"
error MultipleDefaultDistWeight "'default' cannot be used more than once in dist item list"
error DistRealRangeWeight "'dist' for a range of real values must include an explicit weight and must use the ':/' operator"
warning ignored-slice IgnoredSlice "slice size ignored for left-to-right streaming operator"
warning unsized-concat UnsizedInConcat "unsized integer in concat; using a width of {}"
warning width-expand WidthExpand "implicit conversion expands from {} to {} bits"
warning width-trunc WidthTruncate "implicit conversion truncates from {} to {} bits"
warning port-width-expand PortWidthExpand "implicit conversion of port connection expands from {} to {} bits"
warning port-width-trunc PortWidthTruncate "implicit conversion of port connection truncates from {} to {} bits"
warning implicit-conv ImplicitConvert "implicit conversion from {} to {}"
warning packed-array-conv PackedArrayConv "implicit conversion from {} to {}"
warning nonblocking-final NonblockingInFinal "nonblocking assignments in final blocks have no effect"
warning reversed-range ReversedValueRange "reversed range will behave as if it were empty"
warning index-oob IndexOOB "cannot refer to element {} of {}"
warning range-oob RangeOOB "cannot select range of [{}:{}] from {}"
warning range-width-oob RangeWidthOOB "cannot select range of {} elements from {}"
warning specify-param SpecifyBlockParam "parameters cannot be used in specify blocks"
warning negative-timing-limit NegativeTimingLimit "timing check limit value '{}' should not be negative"
warning sign-conversion SignConversion "implicit conversion changes signedness from {} to {}"
warning float-bool-conv FloatBoolConv "implicit conversion from floating point type {} to boolean value"
warning int-bool-conv IntBoolConv "implicit conversion from {} to boolean value"
warning float-int-conv FloatIntConv "implicit conversion from {} to {}"
warning int-float-conv IntFloatConv "implicit conversion from {} to {}"
warning float-widen FloatWiden "implicit conversion widens from {} to {}"
warning float-narrow FloatNarrow "implicit conversion narrows from {} to {}"
warning useless-cast UselessCast "useless cast from {} to the same type"
warning unsigned-arith-shift UnsignedArithShift "arithmetic right shift of unsigned type {} will always shift in zeros (use logical shift if that is intended)"
warning specify-condition-expr SpecifyPathConditionExpr "expression is not valid in a state-dependent path condition"
warning arith-op-mismatch ArithOpMismatch "arithmetic between operands of different types ({} and {})"
warning bitwise-op-mismatch BitwiseOpMismatch "bitwise operation between operands of different types ({} and {})"
warning comparison-mismatch ComparisonMismatch "comparison between operands of different types ({} and {})"
warning sign-compare SignCompare "comparison of differently signed types ({} and {})"
warning case-type CaseTypeMismatch "comparison of different types in '{}' statement ({} and {})"
warning case-outside-range CaseOutsideRange "'{}' item with {} bits can never match the {} bit case expression"
warning bitwise-rel-precedence BitwiseRelPrecedence "'{}' has lower precedence than '{}'; '{}' will be evaluated first"
note NotePrecedenceBitwiseFirst "place parentheses around the '{}' expression to evaluate it first"
note NotePrecedenceSilence "place parentheses around the '{}' expression to silence this warning"
warning bitwise-op-parentheses BitwiseOpParentheses "'{}' within '{}'"
warning logical-op-parentheses LogicalOpParentheses "'{}' within '{}'"
warning arith-in-shift ArithInShift "'{}' has lower precedence than '{}'; '{}' will be evaluated first"
warning logical-not-parentheses LogicalNotParentheses "logical not is only applied to the left hand side of this {}"
note NoteLogicalNotFix "add parentheses after the '!' to evaluate the {} first"
note NoteLogicalNotSilence "add parentheses around left hand side expression to silence this warning"
warning conditional-precedence ConditionalPrecedence "operator '?:' has lower precedence than '{}'; '{}' will be evaluated first"
note NoteConditionalPrecedenceFix "place parentheses around the '?:' expression to evaluate it first"
warning consecutive-comparison ConsecutiveComparison "comparisons like 'x < y < z' don't have their mathematical meaning"

subsystem Statements
error ReturnNotInSubroutine "return statement is only valid inside task and function blocks"
error ReturnInParallel "return statement is not allowed in fork-join block"
error DelayNotNumeric "delay expression type {} is not numeric"
error InvalidEventExpression "invalid type {} for event expression"
error CoverStmtNoFail "cover statement cannot have a fail statement"
error RestrictStmtNoFail "restrict statement cannot have an action block"
error StatementNotInLoop "statement can only be used inside a loop"
error ExprNotStatement "expression is not allowed as a statement"
error ExprNotConstraint "expression is not allowed in a constraint"
error VoidCastFuncCall "void casting is only allowed for function calls"
error NotAnArray "name must refer to an iterable array"
error TooManyForeachVars "too many loop variables for array of type {}"
error ForeachDynamicDimAfterSkipped "foreach index '{}' can't iterate over a dynamically sized dimension since a previous dimension was omitted"
error LoopVarShadowsArray "loop variable '{}' shadows foreach array target"
error ForeachWildcardIndex "foreach loops cannot be used with associative arrays that have a wildcard index"
error CaseInsideKeyword "set membership cannot be used with {} statements"
error InvalidDisableTarget "disable statement must specify a task or block name"
error NotAnEvent "expression is not a triggerable event"
error RepeatNotNumeric "repeat type {} is not numeric"
error RepeatControlNotEvent "repeat control must be an event to count"
error UnexpectedPortDecl "unexpected port declaration"
error InvalidConstraintExpr "{} is not a valid type for a constraint expression"
error NonIntegralConstraintLiteral "literals in constraints must be integers"
error UnknownConstraintLiteral "'x' and 'z' values are not allowed in constraints"
error RandCInDist "'randc' variables cannot be used in 'dist' constraints"
error RandCInUnique "'randc' variables cannot be used in 'unique' constraints"
error RandCInSolveBefore "'randc' variables cannot be used in 'solve before' constraints"
error RandCInSoft "'randc' variables cannot be used in 'soft' constraints"
error RandNeededInDist "'dist' expression must reference at least one 'rand' variable"
error BadUniquenessType "{} is not a valid type for a uniqueness constraint"
error InvalidUniquenessExpr "uniqueness constraints can only contain references to random variables"
error InequivalentUniquenessTypes "type of '{}' is {} which is not equivalent to the type of other variables in the constraint (previously saw {})"
error TaskInConstraint "cannot invoke tasks in constraints"
error OutRefFuncConstraint "cannot call a function with ref or output arguments in constraints"
error BadDisableSoft "target of 'disable soft' constraint must be a 'rand' variable"
error BadSolveBefore "target of 'solve before' constraint must be a random variable"
error BadProceduralAssign "lvalue of procedural assign/deassign must be a variable or concatenation of variables -- bit-selects, part-selects, and references to nets are disallowed"
error BadForceNetType "force and release cannot be applied to nets with a user-defined nettype"
error TimingInFuncNotAllowed "statements that pass time are not allowed in this context"
error AssertionExprType "expression of type {} is not valid in concurrent assertions; type must be cast compatible with an integral type"
error SeqRangeMinMax "sequence range minimum '{}' is greater than specified maximum '{}'"
error ClockingBlockEventEdge "cannot specify an edge when waiting on a clocking item"
error ClockingBlockEventIff "cannot specify an 'iff' condition when waiting on a clocking item"
error NoDefaultClocking "cycle delay cannot be used because no default clocking has been specified"
error WriteToInputClockVar "cannot write to input clocking signal '{}'"
error ClockVarSyncDrive "clocking signal '{}' can only be written via a synchronous drive (with the '<=' operator)"
error InvalidDeferredAssertAction "deferred assertion action must be a subroutine call"
error DeferredAssertNonVoid "cannot invoke non-void function '{}' from a deferred assertion action"
error DeferredAssertOutArg "cannot invoke a subroutine with 'output' or 'inout' arguments from a deferred assertion action"
error DeferredAssertAutoRefArg "cannot pass automatic variable '{}' to 'ref' argument '{}' in a deferred assertion action"
error InvalidMatchItem "sequence match item must be a local variable assignment, increment/decrement operator, or subroutine call"
error LocalVarMatchItem "sequence match items can only modify local variables"
error InvalidSignalEventInSeq "invalid use of event expression in sequence"
error InvalidSyntaxInEventExpr "expression is not valid in an event expression"
error InvalidCommaInPropExpr "invalid comma-separated list in property expression"
error PropExprInSequence "cannot use property expression within a sequence"
error PropertyLhsInvalid "left-hand side of operator must be a sequence expression"
error ThroughoutLhsInvalid "left-hand side of 'throughout' operator must be a simple expression"
error PropInstanceRepetition "property instances cannot specify a repetition"
error SeqInstanceRepetition "sequences cannot specify a non-consecutive or go-to repetition"
error MatchItemsAdmitEmpty "sequences with match items cannot admit an empty match"
error SubroutineMatchNonVoid "cannot invoke non-void function '{}' from a sequence match item"
error SubroutineMatchOutArg "cannot invoke a subroutine with 'output' or 'inout' arguments from a sequence match item"
error SubroutineMatchAutoRefArg "cannot pass automatic variable '{}' to 'ref' argument '{}' in a subroutine invoked from a sequence match item"
error NestedDisableIff "cannot instantiate '{}' here because it has a disable iff condition"
error GlobalSampledValueNested "global clocking future sampled value functions may not be nested"
error DisableIffLocalVar "local variables cannot be used in disable iff conditions"
error DisableIffMatched "'matched' method cannot be used in disable iff conditions"
error PropAbortLocalVar "local variables cannot be used in property abort conditions"
error PropAbortMatched "'matched' method cannot be used in property abort conditions"
error RecursivePropDisableIff "recursive properties cannot have disable iff conditions"
error ConcurrentAssertActionBlock "cannot have '{}' statement in the action block of another concurrent assertion"
error InvalidForStepExpression "for loop step expression must be an assignment, increment/decrement operator, or a subroutine call"
error InvalidBlockEventTarget "block event must specify a task, function, or named block"
error ForkJoinAlwaysComb "fork-join is not allowed in {} procedure"
error BlockingInAlwaysFF "always_ff procedures cannot have blocking timing controls"
error AlwaysFFEventControl "always_ff procedure must have one and only one event control"
error SeqEmptyMatch "sequence must not admit an empty match"
error SeqOnlyEmpty "sequence admits only empty matches"
error ExplicitClockInClockingBlock "sequences and properties declared within clocking blocks cannot specify an explicit clock event"
error ConcurrentAssertNotInProc "concurrent assertion statements may only appear in always and initial blocks"
error CheckerNotInProc "procedural checkers may only appear in always and initial blocks"
error CheckerInForkJoin "checkers cannot be instantiated inside fork-join blocks"
warning case-default CaseDefault "'{}' missing 'default' label"
warning case-redundant-default CaseRedundantDefault "'{}' marked '{}' has 'default' label"
warning case-wildcard-2state CaseWildcard2State "'{}' has two-state type so no wildcard matching will be performed"
warning seq-no-match SeqNoMatch "sequence can never be matched"
warning event-const EventExpressionConstant "edge expression is constant"
warning empty-stmt EmptyStatement "extra ';' has no effect"
warning pointless-void-cast PointlessVoidCast "cast to void for void-returning function '{}' has no effect"
warning unused-result UnusedResult "ignoring return value of '{}', cast to void to suppress"
warning bad-procedural-force BadProceduralForce "lvalue of force/release must be a net, a variable, a constant select of a net, or a concatenation of these"
warning multibit-edge MultiBitEdge "edge of expression of type {} will only trigger on changes to the first bit"
note NoteWhileExpanding "while expanding {} '{}'"
note NoteAlwaysFalse "expression is always false"

subsystem Types
error InvalidEnumBase "invalid enum base type {} (must be a single dimensional integer type)"
error EnumValueOverflow "incrementing previous value {} would overflow enum base type {}"
error EnumIncrementUnknown "cannot increment previous value {} because it has unknown bits"
error EnumValueDuplicate "redefinition of enum value {}"
error EnumValueUnknownBits "value of {} is invalid for enum with base type {}"
error EnumValueOutOfRange "enum value {} is out of range for base type {}"
error EnumRangeMultiDimensional "enum range cannot be multidimensional"
error ForwardTypedefDoesNotMatch "forward typedef basic type '{}' does not match declaration"
error ForwardTypedefVisibility "declared visibility of forward typedef does not match the visibility of the actual type declaration"
error PackedMemberNotIntegral "packed members must be of integral type (not {})"
error PackedMemberHasInitializer "packed members can not have initializers"
error PackedDimsOnPredefinedType "packed dimensions not allowed on predefined integer type '{}'"
error PackedUnionWidthMismatch "all members of a packed union must have the same width; '{}' has width of {}, previously seen width was {}"
error InvalidArraySize "{} is not a valid size for an array"
error CannotDeclareType "cannot declare a type here"
error InvalidAssociativeIndexType "index type cannot be or contain floating point types"
error PackedArrayNotIntegral "packed array elements must be of integral type (not {})"
error PackedTypeTooLarge "packed type of {} bits is too large; maximum width is {} bits"
error ArrayDimTooLarge "array dimension of {} is too large; maximum number of elements is {}"
error ObjectTooLarge "object size exceeds implementation limit of 2^31 bytes"
error InvalidUnionMember "untagged unions cannot have members of type {}"
error VirtualInterfaceUnionMember "virtual interfaces cannot be used as members of unions"
error InvalidArrayElemType "cannot declare an array with elements of type {}"
error InvalidCoverageExpr "{} is not a valid type for a coverage expression"
error TypeRefHierarchical "'type' expression cannot contain hierarchical references"
error ClassInheritanceCycle "circular dependency in inheritance chain for class '{}'"
error NTResolveSingleArg "resolution function for nettype '{}' should have a single input argument of type {}"
error NTResolveReturn "resolution function for nettype '{}' should have a return type of {}"
error NTResolveTask "resolution function for nettype '{}' cannot be a task"
error NTResolveClass "resolution function for nettype '{}' cannot be a non-static class method"
error NTResolveUserDef "resolution function for nettype '{}' must be a normal user-defined function"
error MultiplePackedOpenArrays "cannot declare more than one packed dimension when using an open array"
error RecursiveClassSpecialization "potentially infinitely recursive specialization of class '{}'"
error TypeRefVoid "cannot create a type reference to the 'void' type"
warning enum-range EnumRangeLiteral "enum element ranges must be integer literals"

subsystem Lookup
error Redefinition "redefinition of '{}'"
error RedefinitionDifferentType "redefinition of '{}' with a different type: {} vs {}"
error AmbiguousWildcardImport "multiple imports found for identifier '{}'"
note NoteImportedFrom "imported from here"
error ImportNameCollision "import of '{}' collides with an existing declaration"
error UndeclaredIdentifier "use of undeclared identifier '{}'"
error TypoIdentifier "use of undeclared identifier '{}'; did you mean '{}'?"
error UnknownClassOrPackage "unknown class or package '{}'"
error UsedBeforeDeclared "identifier '{}' used before its declaration"
error NotAType "'{}' is not a type"
error NotAValue "'{}' cannot be used in an expression"
error NotAHierarchicalScope "cannot use dot operator on '{}'; it is not a variable or hierarchical scope"
error CheckerHierarchical "cannot access checker members hierarchically"
error HierarchicalRefUnknownModule "hierarchical reference to unknown module instance '{}' is not allowed"
error NotAModport "'{}' is not a modport of '{}'"
error NotAnInterface "'{}' is not an interface instance or array of instances"
error NotAnInterfaceOrPort "'{}' is not an interface instance or interface port name"
error NotAGenvar "'{}' is not a genvar"
error NotAClass "'{}' is not a class type"
error NotAGenericClass "'{}' is not a generic class type"
error NotASubroutine "'{}' is not a task or function"
error DotOnType "cannot use dot operator on a type name"
error UnknownMember "no member named '{}' in {}"
error UnknownPackageMember "no member named '{}' in package '{}'"
error UnknownClassMember "no member named '{}' in class '{}'"
error UnknownCovergroupMember "no member named '{}' in covergroup '{}'"
error RecursiveDefinition "'{}' recursively depends on its own definition"
error UnknownSystemMethod "unknown built-in method '{}' on type {}"
error ScopeNotIndexable "hierarchical scope '{}' is not indexable"
error InvalidScopeIndexExpression "invalid hierarchical index expression"
error ScopeIndexOutOfRange "hierarchical index {} is out of scope's declared range"
error CouldNotResolveHierarchicalPath "could not resolve hierarchical path name '{}'"
error DotIntoInstArray "object is an array so expecting an index here, not a dot"
error UnresolvedForwardTypedef "forward typedef '{}' does not resolve to a data type"
error DefinitionUsedAsType "'{}' is {} definition but is used like a type"
error DefinitionUsedAsValue "'{}' is {} definition but is used as a value"
error UndeclaredButFoundPackage "'{}' is a package, did you mean to use '::' here?"
error AutoVariableHierarchical "cannot refer to automatic variable via hierarchical reference"
error TypeHierarchical "cannot refer to type names via hierarchical reference"
error NonStaticClassProperty "invalid use of non-static class property '{}'"
error NonStaticClassMethod "call to non-static member function without object handle"
error NestedNonStaticClassProperty "invalid use of non-static class property '{}' from nested class '{}'"
error NestedNonStaticClassMethod "call to non-static member function without object handle from nested class '{}'"
error NoDefaultSpecialization "generic class '{}' does not have a default specialization because some parameters have no default value"
error GenericClassScopeResolution "cannot refer to generic class with '::' without providing parameter assignments"
error InvalidThisHandle "invalid use of 'this' outside of a non-static method"
error LocalMemberAccess "'{}' is a local member of '{}'"
error ProtectedMemberAccess "'{}' is a protected member of '{}'"
error InvalidConstructorAccess "cannot create instance of class '{}' because its constructor is declared '{}'"
error SuperOutsideClass "cannot use 'super' handle outside of class scope"
error SuperNoBase "class '{}' does not have a super class"
error IfaceExtendTypeParam "cannot extend or implement a type parameter"
error IfaceExtendIncomplete "cannot extend or implement an incomplete forward typedef"
error ScopeIncompleteTypedef "cannot resolve scoped class member via incomplete forward typedef"
error InvalidModportAccess "cannot access '{}' via modport '{}.{}'"
error LocalNotAllowed "'local' qualifier not allowed here"
error UnexpectedSelection "unexpected selection expression"
error BadInstanceArrayRange "cannot select range of [{}:{}] from instance array of [{}:{}]"
error InstanceArrayEndianMismatch "endianness of selection [{}:{}] must match declared array range of [{}:{}]"
error IfaceImportExportTarget "could not find target for {} of '{}'"
error UnexpectedNameToken "unexpected use of '{}' keyword"
error IllegalReferenceToProgramItem "cannot reference program item from outside of a program"
error HierarchicalFromPackage "cannot use hierarchical reference from within a package"
error CompilationUnitFromPackage "cannot reference compilation unit item from within a package"
error InvalidHierarchicalIfacePortConn "cannot connect interface port hierarchically through a generate block or instance array"
warning unknown-sys-name UnknownSystemName "unknown system name '{}'"
warning dup-import DuplicateImport "duplicate import declaration is redundant"
warning duplicate-definition DuplicateDefinition "duplicate definition of '{}'"

subsystem SysFuncs
error BadSystemSubroutineArg "invalid argument type {} to system {}"
error UnknownFormatSpecifier "unknown format specifier '%{}'"
error FormatSpecifierWidthNotAllowed "field width not allowed on '%{}' format specifiers"
error FormatSpecifierInvalidWidth "field width is invalid"
error FormatSpecifierNotFloat "floating point format specifier options used on non-floating point field"
error FormatUnspecifiedType "cannot format values of type {} without a specification string"
error FormatMismatchedType "value of type {} is invalid for '%{}' format specifier"
error FormatNoArgument "no argument provided for '%{}' format specifier"
error FormatEmptyArg "empty argument not allowed -- expecting value for '%{}' format specifier"
error FormatTooManyArgs "too many arguments provided for format string"
error InvalidStringArg "value of type {} is not a valid string argument"
error ArrayMethodIntegral "array method '{}' can only be called on unpacked arrays of integral values"
error ArrayMethodComparable "array method '{}' can only be called on unpacked arrays of comparable values"
error SysFuncNotConst "'{}' is not allowed in a constant context"
error QueryOnDynamicType "array query function '{}' cannot be called on a dynamically sized type identifier"
error DimensionIndexInvalid "{} is not a valid dimension for {}"
error DynamicDimensionIndex "cannot refer to a dynamically sized dimension with index {} greater than 1"
error QueryOnAssociativeNonIntegral "'{}' cannot be called with an associative array with non-integral index type"
error QueryOnAssociativeWildcard "'{}' cannot be called with an associative array with non-integral (or wildcard) index type"
error AssociativeWildcardNotAllowed "'{}' cannot be called with an associative array with wildcard index type"
error ExpectedModuleName "expected module name"
error ExpectedModOrVarName "expected module or variable name"
error ExpectedScopeOrAssert "expected scope or assertion name"
error ExpectedScopeName "expected scope name"
error ExpectedClassPropertyName "expected class property name"
error ExpectedVariableName "expected variable name"
error ExpectedModuleInstance "expected module instance"
error ArrayLocatorWithClause "'{}' requires 'with' expression for searching"
error AutoVarTraced "automatic variable '{}' cannot be traced"
error NoGlobalClocking "no global clocking is available in this scope"
error GlobalClockEventExpr "$global_clock system function can only be called as an event expression"
error InferredValDefArg "the {} system function can only be used as the entire default value expression for a sequence or property formal argument"
error PastNumTicksInvalid "'number_of_ticks' argument must be greater than or equal to 1"
error SeqMethodInputLocalVar "cannot call '{}' on a sequence that has 'input' or 'inout' local variable formal arguments"
error SampledValueLocalVar "local variables cannot be used in sampled value system functions"
error SampledValueMatched "'matched' method cannot be used in sampled value system functions"
error GlobalSampledValueAssertionExpr "global clocking future sampled value functions may be invoked only in sequence and property expressions"
error SequenceMethodLocalVar "local variables can only be passed to sequences on which '{}' is called if they are the entire argument and not a sub-expression"
error SequenceMatchedOutsideAssertion "'matched' method can only be called from a sequence expression"
error PlaRangeInAscendingOrder "must specify dimensions in ascending order for PLA input type {}"
error SysFuncHierarchicalNotAllowed "hierarchical references are not allowed in calls to '{}'"
error TypeIsNotAClass "{} is not a class type"
error ExpectedNetRef "expected a reference to a net"
warning format-real FormatRealInt "real value provided for integer format specifier '%{}'"
warning finish-num BadFinishNum "finish argument must have value of 0, 1, or 2"
warning missing-format MissingFormatSpecifier "missing format specifier"
warning format-multibit-strength FormatMultibitStrength "formatting strength values for multi-bit nets is non-standard"
warning nonstandard-sys-func NonstandardSysFunc "system function '{}' is non-standard"
warning isunbounded-param-arg IsUnboundedParamArg "argument to $isunbounded must be the name of a parameter"

subsystem ConstEval
note NoteInCallTo "in call to '{}'"
note NoteSkippingFrames "(skipping {} calls in backtrace; use --constexpr-backtrace-limit=0 to see all)"
error ConstEvalNonConstVariable "reference to non-constant variable '{}' is not allowed in a constant expression"
error ConstEvalBitstreamCastSize "bit-stream casting argument size {} cannot fit casting type {} in a constant expression"
error ConstEvalReplicationCountInvalid "string replication count {} is invalid in a constant expression"
error ConstEvalHierarchicalName "reference to '{}' by hierarchical name is not allowed in a constant expression"
error ConstEvalFunctionIdentifiersMustBeLocal "all identifiers that are not parameters or enums must be declared locally to a constant function"
error ConstEvalIdUsedInCEBeforeDecl "identifier '{}' is declared after the invocation of the current constant function"
error ConstEvalNoCaseItemsMatched "no items in {} case statement matched value {} in constant function"
error ConstEvalCaseItemsNotUnique "more than one match found in unique case statement for value {} in constant function"
error ConstEvalTimedStmtNotConst "constant expressions cannot schedule events"
error ConstEvalAssertionFailed "assertion failed in constant function"
error ConstEvalParallelBlockNotConst "parallel blocks are not allowed in constant functions"
error ConstEvalExceededMaxCallDepth "constant evaluation exceeded maximum depth of {} calls"
error ConstEvalExceededMaxSteps "constant evaluation hit maximum step limit; possible infinite loop?"
error ConstEvalTaskNotConstant "cannot invoke a task in a constant expression"
error ConstEvalVoidNotConstant "cannot call a void function in a constant expression"
error ConstEvalDPINotConstant "cannot call DPI import function in a constant expression"
error ConstEvalMethodNotConstant "cannot call class method in a constant expression"
error ConstEvalSubroutineNotConstant "'{}' is not callable in a constant expression"
error ConstEvalFunctionArgDirection "cannot call a function with output, inout, or ref arguments in a constant expression"
error ConstEvalFunctionInsideGenerate "cannot call a function declared inside a generate block in a constant expression"
error ConstEvalDynamicToFixedSize "cannot assign {} of length {} to {}"
error ConstEvalAssociativeIndexInvalid "index {} has x or z bits which is invalid"
error ConstEvalDisableTarget "disable statement in a constant function cannot target a block that is not currently executing"
error ConstEvalClassType "class types are not allowed in constant expressions"
error ConstEvalCovergroupType "covergroup types are not allowed in constant expressions"
error ConstEvalProceduralAssign "procedural assign and deassign statements are not allowed in constant functions"
error ConstEvalTaggedUnion "cannot access '{}' because it is not the currently active member of the tagged union"
error ConstEvalRandValue "random value statements are not allowed in constant functions"
error ConstEvalParamCycle "cyclic dependency found when determining value of '{}'"
error ConstEvalNoIfItemsMatched "no items in {} if statement matched value {} in constant function"
error ConstEvalIfItemsNotUnique "more than one match found in unique if statement for value {} in constant function"
error ConstEvalCheckers "checkers are not allowed in constant functions"
warning elem-not-found ConstEvalAssociativeElementNotFound "element {} does not exist in associative array"
warning static-skipped ConstEvalStaticSkipped "static variable initialization is skipped in constant function calls"
warning dynarray-index ConstEvalDynamicArrayIndex "invalid index {} for {} of length {}"
warning dynarray-range ConstEvalDynamicArrayRange "invalid range [{}:{}] for {} of length {}"
warning queue-range ConstEvalQueueRange "reversed selection range [{}:{}] for queue will always yield an empty result"
warning empty-queue ConstEvalEmptyQueue "pop from empty queue"
warning task-ignored ConstSysTaskIgnored "system task '{}' is ignored in constant expression"
warning constant-conversion ConstantConversion "implicit conversion from {} to {} changes value from {} to {}"

subsystem Compilation
fatal MaxInstanceDepthExceeded "{} instantiation exceeded maximum depth of {}"
fatal InfinitelyRecursiveHierarchy "infinitely recursive instantiation of {}"
error InvalidTopModule "'{}' is not a valid top-level module"
error TopModuleIfacePort "top-level module '{}' has unconnected interface port '{}'"
error TopModuleRefPort "top-level module '{}' has unconnected 'ref' port '{}'"
error TopModuleUnnamedRefPort "top-level module '{}' has unconnected unnamed 'ref' port"
error NoDeclInClass "out-of-block definition of '{}' does not match any declaration in '{}'"
error InvalidParamOverrideOpt "'{}' is not a valid form of parameter override"
error DPIExportKindMismatch "mismatch between 'function' and 'task' declared in DPI export"
error DPIExportDifferentScope "exported subroutines must be defined in the same scope as the export directive"
error DPIExportDuplicate "duplicate export of '{}'"
error DPIExportImportedFunc "cannot export an imported DPI subroutine"
error InvalidDPICIdentifier "'{}' is not a valid C identifier for DPI subroutine"
error DPISignatureMismatch "more than one DPI subroutine with C identifier '{}' declared with mismatching type signatures"
error DPIExportDuplicateCId "more than one DPI export with the same C identifier '{}' in the same scope"
error DefParamTargetChange "defparam target resolves differently post-elaboration; see '{}' vs '{}'"
error DefParamCycle "cyclic dependency found when resolving defparam"
error MultipleDefaultClocking "scope already has a default clocking block"
error MultipleGlobalClocking "scope already has a global clocking block"
error MultipleDefaultDisable "scope already has a default disable declaration"
error MissingExternImpl "no implementation found for extern {} {}.{}"
error MissingExportImpl "no implementation for modport export '{}' provided by '{}'"
error InvalidBindTarget "'{}' is not a valid bind target; only modules and interfaces are allowed"
error WrongBindTargetDef "bind target '{}' is not an instance of '{}'"
error BindTypeParamNotFound "bind type parameter '{}' resolves to {} which cannot be found in source scope"
error BindTypeParamMismatch "bind type parameter '{}' resolves to {} in source scope but {} in target scope"
error MissingTimeScale "design element does not have a time scale defined but others in the design do"
error MissingExternModuleImpl "missing implementation for extern {} '{}'"
error ExternDeclMismatchImpl "extern {} '{}' does not match implementation"
error ExternDeclMismatchPrev "extern {} '{}' does not match previous extern definition"
error UnknownLibrary "unknown library '{}'"
error NestedConfigMultipleTops "non-top config '{}' has more than one top cell specified"
error ConfigParamsIgnored "parameter overrides provided by config rule are ignored because the target instance is using a different nested config '{}'"
error MultipleTopDupName "more than one top module specified with the name '{}'"
error NetAliasSelf "cannot alias a net to itself"
error MultipleNetAlias "cannot specify an alias between the same bits of the same nets more than once"
error VirtualIfaceHierRef "interface containing hierarchical reference to object outside its body cannot be used in a virtual interface"
error VirtualIfaceIfacePort "interface with ports that reference other interfaces cannot be used in a virtual interface"
note NoteAliasedTo "which is aliased to"
note NoteAliasDeclaration "previous alias declaration here"
note NoteHierarchicalRef "hierarchical reference here"
warning missing-top NoTopModules "no top-level modules found in design"
warning duplicate-defparam DuplicateDefparam "parameter already has a defparam override applied; ignoring this one"

subsystem Analysis
error AssertionNoClock "{} has no explicit clocking event and one cannot be inferred from context"
error InvalidMulticlockedSeqOp "multiclocked sequence operands cannot be combined with any sequence operators other than ##1 and ##0"
error MulticlockedSeqEmptyMatch "sequence admits an empty match; this is not allowed when concatenating differently clocked sequences"
error MulticlockedInClockingBlock "cannot instantiate '{}' in clocking block because it is multiclocked"
error DifferentClockInClockingBlock "cannot instantiate '{}' in clocking block because its clock differs from that of the containing clocking block"
error NoUniqueClock "assertion expression does not have a unique leading clock"
error NoInferredClock "no inferred clock in this context"
error SampledValueFuncClock "could not determine clocking event for '{}'"
error SeqMethodEndClock "the ending clock of the sequence to which '{}' is applied differs from the clock of the calling context"
error GFSVMatchItems "cannot call '{}' in an assertion that has sequence match items"
error NTResolveArgModify "resolution function for nettype '{}' cannot modify its input argument '{}'"
error MixedVarAssigns "cannot mix continuous and procedural assignments to variable '{}'"
error MultipleContAssigns "cannot have multiple continuous assignments to variable '{}'"
error MultipleAlwaysAssigns "variable '{}' driven by {} procedure cannot be written to by any other process"
error MultipleUWireDrivers "'uwire' net '{}' cannot have multiple drivers"
error MultipleUDNTDrivers "net '{}' with user-defined nettype '{}' cannot have multiple drivers because it does not specify a resolution function"
error InputPortAssign "cannot assign to input port '{}'"
error ClockVarTargetAssign "cannot write to '{}' with a continuous assignment since it's associated with an output clocking signal"
error LocalFormalVarMultiAssign "cannot bind local variable '{}' to more than one output or inout local variable formal argument"
note NoteRequiredHere "required here"
note NoteClockHere "clock here"
warning unused-def UnusedDefinition "{} definition is unused"
warning unused-net UnusedNet "unused net '{}'"
warning unused-implicit-net UnusedImplicitNet "implicit net '{}' is unused elsewhere"
warning unused-variable UnusedVariable "unused variable '{}'"
warning undriven-net UndrivenNet "net '{}' is never driven"
warning unassigned-variable UnassignedVariable "variable '{}' is never assigned a value"
warning unused-but-set-net UnusedButSetNet "net '{}' is driven but its value is never used"
warning unused-but-set-variable UnusedButSetVariable "variable '{}' is assigned but its value is never used"
warning unused-but-set-port UnusedButSetPort "'inout' port '{}' is driven but its value is never used"
warning unused-port UnusedPort "unused port signal '{}'"
warning undriven-port UndrivenPort "undriven output port '{}'"
warning unused-argument UnusedArgument "unused formal argument '{}'"
warning unused-parameter UnusedParameter "unused parameter '{}'"
warning unused-type-parameter UnusedTypeParameter "unused type parameter '{}'"
warning unused-typedef UnusedTypedef "unused typedef '{}'"
warning unused-genvar UnusedGenvar "unused genvar '{}'"
warning unused-assertion-decl UnusedAssertionDecl "unused {} '{}'"
warning unused-import UnusedImport "unused import '{}'"
warning unused-wildcard-import UnusedWildcardImport "unused wildcard import"
warning inferred-latch InferredLatch "latch inferred for '{}' because it is not assigned on all control paths"
warning inferred-comb InferredComb "combinational logic inferred for '{}' because it is assigned on all control paths"
warning incomplete-return IncompleteReturn "non-void function '{}' does not return a value on all control paths"
warning missing-return MissingReturn "non-void function '{}' does not return a value"
warning case-enum CaseEnum "enumeration {} not handled in case statement"
warning case-enum-explicit CaseEnumExplicit "enumeration {} not explicitly handled in case statement"
warning case-dup CaseDup "'{}' statement contains duplicate items for value '{}' (the second one will never be matched)"
warning case-overlap CaseOverlap "'{}' statement item overlaps previous item"
warning case-unreachable CaseUnreachable "'{}' statement item will never match because it is completely handled by previous items"
warning case-not-wildcard CaseNotWildcard "'case' statement item has unknown bits which don't work as wildcards; should this be a 'casex' or 'casez' statement?"
warning casez-with-x CaseZWithX "'casez' statement item has 'x' bits which don't work as wildcards; should this be a 'casex' statement?"
warning case-too-complex CaseComplex "'{}' statement is too complex to analyze; assuming it does not have full coverage (increase --max-case-analysis-steps to try harder)"
warning case-incomplete CaseIncomplete "'{}' statement does not handle all possible inputs (for example, {})"
warning port-coercion InputPortCoercion "input net port '{}' coerced to 'inout'"
warning port-coercion OutputPortCoercion "output net port '{}' coerced to 'inout'"

subsystem Meta
fatal TooManyErrors "too many errors emitted, stopping now [--error-limit=]"
warning unknown-warning-option UnknownWarningOption "unknown warning option '{}'"

// Following are groups of diagnostics, to make it easy to control them from the command line.
// The default group is the set of warnings that are enabled by default.

group default = { real-underflow real-overflow vector-overflow int-overflow unconnected-port
                  unconnected-unnamed-port redef-macro unknown-pragma unknown-warning-option
                  extra-pragma-args expected-diag-arg unknown-diag-arg pragma-diag-level explicit-static
                  static-skipped elem-not-found dynarray-index dynarray-range queue-range empty-queue
                  unknown-escape-code missing-top warning-task unsized-concat finish-num implicit-conv
                  nonblocking-final constraint-missing missing-format dpi-spec enum-range udp-port-empty
                  lifetime-prototype net-inconsistent port-coercion empty-body reversed-range
                  invalid-source-encoding index-oob range-oob range-width-oob expected-protect-arg
                  unknown-protect-keyword expected-protect-keyword nested-protect-begin extra-protect-end
                  protect-arglist unknown-protect-encoding unknown-protect-option invalid-pragma-number
                  invalid-pragma-viewport duplicate-definition protect-encoding-bytes invalid-encoding-byte
                  raw-protect-eof protected-envelope specify-param dup-timing-path invalid-pulsestyle
                  negative-timing-limit bad-procedural-force duplicate-defparam implicit-port-type-mismatch
                  split-distweight-op dpi-pure-task multibit-edge unknown-sys-name unknown-library
                  dup-config-rule unused-config-cell unused-config-instance specify-condition-expr
                  seq-no-match case-too-complex }

group extra = { empty-member empty-stmt dup-import pointless-void-cast case-gen-none case-gen-dup
                unused-result format-real ignored-slice task-ignored width-trunc dup-attr event-const
                ineffective-sign port-width-trunc constant-conversion float-bool-conv int-bool-conv
                useless-cast unsigned-arith-shift static-init-order static-init-value float-int-conv
                float-narrow bitwise-rel-precedence arith-in-shift logical-not-parentheses
                consecutive-comparison case-outside-range case-enum case-dup case-overlap
                case-unreachable incomplete-return missing-return case-redundant-default case-incomplete
                case-wildcard-2state inferred-latch inferred-comb }

group pedantic = { empty-pattern implicit-net-port nonstandard-escape-code nonstandard-generate format-multibit-strength
                   nonstandard-sys-func nonstandard-foreach nonstandard-dist isunbounded-param-arg udp-coverage }

group conversion = { width-trunc width-expand port-width-trunc port-width-expand implicit-conv
                     constant-conversion sign-conversion float-bool-conv int-bool-conv float-int-conv
                     int-float-conv float-widen float-narrow packed-array-conv case-type }

group unused = { unused-def unused-net unused-implicit-net unused-variable undriven-net unassigned-variable
                 unused-but-set-net unused-but-set-variable unused-port undriven-port unused-argument
                 unused-parameter unused-type-parameter unused-typedef unused-genvar unused-assertion-decl
                 unused-but-set-port unused-config-cell unused-config-instance unused-import unused-wildcard-import }

group parentheses = { bitwise-rel-precedence arith-in-shift logical-not-parentheses bitwise-op-parentheses
                      logical-op-parentheses conditional-precedence consecutive-comparison }
