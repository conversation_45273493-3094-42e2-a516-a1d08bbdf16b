Expression final=false
empty

DataType base=Expression final=false
empty

TimingControl final=false
empty

SequenceExpr final=false
empty

PropertyExpr final=false
empty

// ----- ATTRIBUTES -----

EqualsValueClause
token equals
Expression expr

AttributeSpec
token name
EqualsValueClause? value

AttributeInstance
token openParen
token openStar
separated_list<AttributeSpec> specs
token closeStar
token closeParen

NamedLabel
token name
token colon

Statement final=false
NamedLabel? label
list<AttributeInstance> attributes

Member final=false
list<AttributeInstance> attributes

// ----- ARGUMENTS -----

Argument final=false
empty

EmptyArgument base=Argument
token placeholder

OrderedArgument base=Argument
PropertyExpr expr

NamedArgument base=Argument
token dot
token name
token openParen
PropertyExpr? expr
token closeParen

ArgumentList
token openParen
separated_list<Argument> parameters
token closeParen

ParamAssignment final=false
empty

OrderedParamAssignment base=ParamAssignment
Expression expr

NamedParamAssignment base=ParamAssignment
token dot
token name
token openParen
Expression? expr
token closeParen

ParameterValueAssignment
token hash
token openParen
separated_list<ParamAssignment> parameters
token closeParen

// ----- PATTERNS -----

Pattern final=false
empty

ParenthesizedPattern base=Pattern
token openParen
Pattern pattern
token closeParen

VariablePattern base=Pattern
token dot
token variableName

WildcardPattern base=Pattern
token dot
token star

ExpressionPattern base=Pattern
Expression expr

TaggedPattern base=Pattern
token tagged
token memberName
Pattern? pattern

StructurePatternMember final=false
empty

OrderedStructurePatternMember base=StructurePatternMember
Pattern pattern

NamedStructurePatternMember base=StructurePatternMember
token name
token colon
Pattern pattern

StructurePattern base=Pattern
token openBrace
separated_list<StructurePatternMember> members
token closeBrace

MatchesClause
token matchesKeyword
Pattern pattern

ConditionalPattern
Expression expr
MatchesClause? matchesClause

ConditionalPredicate
separated_list<ConditionalPattern> conditions

AssignmentPattern final=false
empty

SimpleAssignmentPattern base=AssignmentPattern
token openBrace
separated_list<Expression> items
token closeBrace

AssignmentPatternItem
Expression key
token colon
Expression expr

StructuredAssignmentPattern base=AssignmentPattern
token openBrace
separated_list<AssignmentPatternItem> items
token closeBrace

ReplicatedAssignmentPattern base=AssignmentPattern
token openBrace
Expression countExpr
token innerOpenBrace
separated_list<Expression> items
token innerCloseBrace
token closeBrace

// ----- EXPRESSIONS -----

BadExpression base=Expression
Expression expr

PrimaryExpression base=Expression final=false
empty

PrefixUnaryExpression base=Expression multiKind=true
token operatorToken
list<AttributeInstance> attributes
Expression operand

kindmap<PrefixUnaryExpression>
UnaryPlusExpression UnaryMinusExpression UnaryBitwiseAndExpression
UnaryBitwiseNandExpression UnaryBitwiseOrExpression UnaryBitwiseNorExpression
UnaryBitwiseXorExpression UnaryBitwiseXnorExpression UnaryPreincrementExpression
UnaryPredecrementExpression UnaryLogicalNotExpression UnaryBitwiseNotExpression

PostfixUnaryExpression base=Expression multiKind=true
Expression operand
list<AttributeInstance> attributes
token operatorToken

kindmap<PostfixUnaryExpression>
PostincrementExpression PostdecrementExpression

BinaryExpression base=Expression multiKind=true
Expression left
token operatorToken
list<AttributeInstance> attributes
Expression right

kindmap<BinaryExpression>
AddExpression SubtractExpression MultiplyExpression DivideExpression PowerExpression
ModExpression EqualityExpression InequalityExpression CaseEqualityExpression
CaseInequalityExpression WildcardEqualityExpression WildcardInequalityExpression
LessThanExpression LessThanEqualExpression GreaterThanExpression GreaterThanEqualExpression
LogicalAndExpression LogicalOrExpression BinaryAndExpression BinaryOrExpression
BinaryXorExpression BinaryXnorExpression LogicalImplicationExpression
LogicalEquivalenceExpression LogicalShiftLeftExpression LogicalShiftRightExpression
ArithmeticShiftLeftExpression ArithmeticShiftRightExpression
AssignmentExpression AddAssignmentExpression SubtractAssignmentExpression
MultiplyAssignmentExpression DivideAssignmentExpression ModAssignmentExpression
AndAssignmentExpression OrAssignmentExpression XorAssignmentExpression
LogicalLeftShiftAssignmentExpression LogicalRightShiftAssignmentExpression
ArithmeticLeftShiftAssignmentExpression ArithmeticRightShiftAssignmentExpression
NonblockingAssignmentExpression

MinTypMaxExpression base=Expression
Expression min
token colon1
Expression typ
token colon2
Expression max

TaggedUnionExpression base=Expression
token tagged
token member
Expression? expr

ValueRangeExpression base=Expression
token openBracket
Expression left
token op
Expression right
token closeBracket

RangeList
token openBrace
separated_list<Expression> valueRanges
token closeBrace

InsideExpression base=Expression
Expression expr
token inside
RangeList ranges

ConditionalExpression base=Expression
ConditionalPredicate predicate
token question
list<AttributeInstance> attributes
Expression left
token colon
Expression right

AssignmentPatternExpression base=PrimaryExpression
DataType? type
AssignmentPattern pattern

// ----- SELECTORS -----

Selector final=false
empty

BitSelect base=Selector
Expression expr

RangeSelect base=Selector multiKind=true
Expression left
token range
Expression right

kindmap<RangeSelect>
SimpleRangeSelect AscendingRangeSelect DescendingRangeSelect

ElementSelect
token openBracket
Selector? selector
token closeBracket

// ----- NAMES -----

Name base=Expression final=false
empty

IdentifierName base=Name
token identifier

SystemName base=Name
token systemIdentifier

IdentifierSelectName base=Name
token identifier
list<ElementSelect> selectors

EmptyIdentifierName base=Name
token placeholder

KeywordName base=Name multiKind=true
token keyword

kindmap<KeywordName>
LocalScope UnitScope RootScope ThisHandle SuperHandle ArrayUniqueMethod
ArrayAndMethod ArrayOrMethod ArrayXorMethod ConstructorName

ClassName base=Name
token identifier
ParameterValueAssignment parameters

ScopedName base=Name
Name left
token separator
Name right

// ----- PRIMARY EXPRESSIONS -----

LiteralExpression base=PrimaryExpression multiKind=true
token literal

kindmap<LiteralExpression>
NullLiteralExpression StringLiteralExpression IntegerLiteralExpression
UnbasedUnsizedLiteralExpression RealLiteralExpression
TimeLiteralExpression WildcardLiteralExpression
DefaultPatternKeyExpression

IntegerVectorExpression base=PrimaryExpression
token size
token base
token value

EmptyQueueExpression base=PrimaryExpression
token openBrace
token closeBrace

ConcatenationExpression base=PrimaryExpression
token openBrace
separated_list<Expression> expressions
token closeBrace

MultipleConcatenationExpression base=PrimaryExpression
token openBrace
Expression expression
ConcatenationExpression concatenation
token closeBrace

StreamExpressionWithRange
token withKeyword
ElementSelect range

StreamExpression
Expression expression
StreamExpressionWithRange? withRange

StreamingConcatenationExpression base=PrimaryExpression
token openBrace
token operatorToken
Expression? sliceSize
token innerOpenBrace
separated_list<StreamExpression> expressions
token innerCloseBrace
token closeBrace

ParenthesizedExpression base=PrimaryExpression
token openParen
Expression expression
token closeParen

NewArrayExpression base=Expression
Name newKeyword
token openBracket
Expression sizeExpr
token closeBracket
ParenthesizedExpression? initializer

NewClassExpression base=Expression
Name scopedNew
ArgumentList? argList

CopyClassExpression base=Expression
Name scopedNew
Expression expr

SuperNewDefaultedArgsExpression base=Expression
Name scopedNew
token openParen
token defaultKeyword
token closeParen

// ----- POSTFIX EXPRESSIONS -----

ElementSelectExpression base=Expression
Expression left
ElementSelect select

MemberAccessExpression base=Expression
Expression left
token dot
token name

InvocationExpression base=Expression
Expression left
list<AttributeInstance> attributes
ArgumentList? arguments

CastExpression base=Expression
Expression left
token apostrophe
ParenthesizedExpression right

SignedCastExpression base=Expression
token signing
token apostrophe
ParenthesizedExpression inner

// ----- TIMING CONTROL -----

Delay base=TimingControl multiKind=true
token hash
Expression delayValue

kindmap<Delay>
DelayControl CycleDelay

Delay3 base=TimingControl
token hash
token openParen
Expression delay1
token comma1
Expression? delay2
token comma2
Expression? delay3
token closeParen

OneStepDelay base=TimingControl
token hash
token oneStep

EventControl base=TimingControl
token at
Expression eventName

IffEventClause
token iff
Expression expr

EventExpression base=SequenceExpr final=false
empty

SignalEventExpression base=EventExpression
token edge
Expression expr
IffEventClause? iffClause

BinaryEventExpression base=EventExpression
EventExpression left
token operatorToken
EventExpression right

ParenthesizedEventExpression base=EventExpression
token openParen
EventExpression expr
token closeParen

ImplicitEventControl base=TimingControl
token at
token openParen
token star
token closeParen

EventControlWithExpression base=TimingControl
token at
EventExpression expr

RepeatedEventControl base=TimingControl
token repeat
token openParen
Expression expr
token closeParen
TimingControl? eventControl

TimingControlExpression base=Expression
TimingControl timing
Expression expr

// ----- DECLARATIONS -----

DimensionSpecifier final=false
empty

RangeDimensionSpecifier base=DimensionSpecifier
Selector selector

WildcardDimensionSpecifier base=DimensionSpecifier
token star

ColonExpressionClause
token colon
Expression expr

QueueDimensionSpecifier base=DimensionSpecifier
token dollar
ColonExpressionClause? maxSizeClause

VariableDimension
token openBracket
DimensionSpecifier? specifier
token closeBracket

Declarator
token name
list<VariableDimension> dimensions
EqualsValueClause? initializer

DataDeclaration base=Member
tokenlist modifiers
DataType type
separated_list<Declarator> declarators
token semi

ForwardTypeRestriction
token keyword1
token keyword2

TypedefDeclaration base=Member
token typedefKeyword
DataType type
token name
list<VariableDimension> dimensions
token semi

ForwardTypedefDeclaration base=Member
token typedefKeyword
ForwardTypeRestriction? typeRestriction
token name
token semi

NetStrength final=false
empty

ChargeStrength base=NetStrength
token openParen
token strength
token closeParen

DriveStrength base=NetStrength
token openParen
token strength0
token comma
token strength1
token closeParen

PullStrength base=NetStrength
token openParen
token strength
token closeParen

NetDeclaration base=Member
token netType
NetStrength? strength
token expansionHint
DataType type
TimingControl? delay
separated_list<Declarator> declarators
token semi

UserDefinedNetDeclaration base=Member
token netType
TimingControl delay
separated_list<Declarator> declarators
token semi

WithFunctionClause
token with
Name name

NetTypeDeclaration base=Member
token keyword
DataType type
token name
WithFunctionClause? withFunction
token semi

PackageImportItem
token package
token doubleColon
token item

PackageImportDeclaration base=Member
token keyword
separated_list<PackageImportItem> items
token semi

PackageExportDeclaration base=Member
token keyword
separated_list<PackageImportItem> items
token semi

PackageExportAllDeclaration base=Member
token keyword
token star1
token doubleColon
token star2
token semi

ParameterDeclarationBase final=false
token keyword

ParameterDeclaration base=ParameterDeclarationBase
DataType type
separated_list<Declarator> declarators

EqualsTypeClause
token equals
DataType type

TypeAssignment
token name
EqualsTypeClause? assignment

TypeParameterDeclaration base=ParameterDeclarationBase
token typeKeyword
ForwardTypeRestriction? typeRestriction
separated_list<TypeAssignment> declarators

ParameterDeclarationStatement base=Member
ParameterDeclarationBase parameter
token semi

PortHeader final=false
empty

PortDeclaration base=Member
PortHeader header
separated_list<Declarator> declarators
token semi

GenvarDeclaration base=Member
token keyword
separated_list<IdentifierName> identifiers
token semi

FunctionPortBase final=false
empty

DefaultFunctionPort base=FunctionPortBase
token keyword

FunctionPort base=FunctionPortBase
list<AttributeInstance> attributes
token constKeyword
token direction
token staticKeyword
token varKeyword
DataType? dataType
Declarator declarator

FunctionPortList
token openParen
separated_list<FunctionPortBase> ports
token closeParen

// ----- TYPES -----

IntegerType base=DataType multiKind=true
token keyword
token signing
list<VariableDimension> dimensions

kindmap<IntegerType>
BitType LogicType RegType ByteType ShortIntType IntType LongIntType
IntegerType TimeType

KeywordType base=DataType multiKind=true
token keyword

kindmap<KeywordType>
ShortRealType RealType RealTimeType StringType CHandleType EventType
VoidType Untyped PropertyType SequenceType

NamedType base=DataType
Name name

StructUnionMember
list<AttributeInstance> attributes
token randomQualifier
DataType type
separated_list<Declarator> declarators
token semi

StructUnionType base=DataType multiKind=true
token keyword
token taggedOrSoft
token packed
token signing
token openBrace
list<StructUnionMember> members
token closeBrace
list<VariableDimension> dimensions

kindmap<StructUnionType>
StructType UnionType

EnumType base=DataType
token keyword
DataType? baseType
token openBrace
separated_list<Declarator> members
token closeBrace
list<VariableDimension> dimensions

TypeReference base=DataType
token typeKeyword
token openParen
Expression expr
token closeParen

DotMemberClause
token dot
token member

VirtualInterfaceType base=DataType
token virtualKeyword
token interfaceKeyword
token name
ParameterValueAssignment? parameters
DotMemberClause? modport

ImplicitType base=DataType
token signing
list<VariableDimension> dimensions
token placeholder

// ----- ASSERTIONS -----

DeferredAssertion
token hash
token zero
token finalKeyword

ElseClause
token elseKeyword
SyntaxNode clause

ActionBlock
Statement? statement
ElseClause? elseClause

ImmediateAssertionStatement base=Statement multiKind=true
token keyword
DeferredAssertion? delay
ParenthesizedExpression expr
ActionBlock action

kindmap<ImmediateAssertionStatement>
ImmediateAssertStatement ImmediateAssumeStatement ImmediateCoverStatement

DisableIff
token disable
token iff
token openParen
Expression expr
token closeParen

PropertySpec
TimingControl? clocking
DisableIff? disable
PropertyExpr expr

ConcurrentAssertionStatement base=Statement multiKind=true
token keyword
token propertyOrSequence
token openParen
PropertySpec propertySpec
token closeParen
ActionBlock action

kindmap<ConcurrentAssertionStatement>
AssertPropertyStatement AssumePropertyStatement CoverSequenceStatement
CoverPropertyStatement RestrictPropertyStatement ExpectPropertyStatement

ConcurrentAssertionMember base=Member
ConcurrentAssertionStatement statement

ImmediateAssertionMember base=Member
ImmediateAssertionStatement statement

// ----- STATEMENTS -----

EmptyStatement base=Statement
token semicolon

ConditionalStatement base=Statement
token uniqueOrPriority
token ifKeyword
token openParen
ConditionalPredicate predicate
token closeParen
Statement statement
ElseClause? elseClause

CaseItem final=false
empty

DefaultCaseItem base=CaseItem
token defaultKeyword
token colon
SyntaxNode clause

PatternCaseItem base=CaseItem
Pattern pattern
token tripleAnd
Expression? expr
token colon
Statement statement

StandardCaseItem base=CaseItem
separated_list<Expression> expressions
token colon
SyntaxNode clause

CaseStatement base=Statement
token uniqueOrPriority
token caseKeyword
token openParen
Expression expr
token closeParen
token matchesOrInside
list<CaseItem> items
token endcase

ForeverStatement base=Statement
token foreverKeyword
Statement statement

LoopStatement base=Statement
token repeatOrWhile
token openParen
Expression expr
token closeParen
Statement statement

DoWhileStatement base=Statement
token doKeyword
Statement statement
token whileKeyword
token openParen
Expression expr
token closeParen
token semi

ForVariableDeclaration
token varKeyword
DataType? type
Declarator declarator

ForLoopStatement base=Statement
token forKeyword
token openParen
separated_list<SyntaxNode> initializers
token semi1
Expression? stopExpr
token semi2
separated_list<Expression> steps
token closeParen
Statement statement

ForeachLoopList
token openParen
Name arrayName
token openBracket
separated_list<Name> loopVariables
token closeBracket
token closeParen

ForeachLoopStatement base=Statement
token keyword
ForeachLoopList loopList
Statement statement

ReturnStatement base=Statement
token returnKeyword
Expression? returnValue
token semi

JumpStatement base=Statement
token breakOrContinue
token semi

TimingControlStatement base=Statement
TimingControl timingControl
Statement statement

ExpressionStatement base=Statement
Expression expr
token semi

VoidCastedCallStatement base=Statement
token voidKeyword
token apostrophe
token openParen
Expression expr
token closeParen
token semi

ProceduralAssignStatement base=Statement multiKind=true
token keyword
Expression expr
token semi

kindmap<ProceduralAssignStatement>
ProceduralAssignStatement ProceduralForceStatement

ProceduralDeassignStatement base=Statement multiKind=true
token keyword
Expression variable
token semi

kindmap<ProceduralDeassignStatement>
ProceduralDeassignStatement ProceduralReleaseStatement

DisableStatement base=Statement
token disable
Name name
token semi

DisableForkStatement base=Statement
token disable
token fork
token semi

NamedBlockClause
token colon
token name

BlockStatement base=Statement multiKind=true
token begin
NamedBlockClause? blockName
list<SyntaxNode> items
token end
NamedBlockClause? endBlockName

kindmap<BlockStatement>
SequentialBlockStatement ParallelBlockStatement

WaitStatement base=Statement
token wait
token openParen
Expression expr
token closeParen
Statement statement

WaitForkStatement base=Statement
token wait
token fork
token semi

WaitOrderStatement base=Statement
token wait_order
token openParen
separated_list<Name> names
token closeParen
ActionBlock action

RandCaseItem
Expression expr
token colon
Statement statement

RandCaseStatement base=Statement
token randCase
list<RandCaseItem> items
token endCase

RsProd final=false
empty

RsProdItem base=RsProd
token name
ArgumentList? argList

RsCodeBlock base=RsProd
token openBrace
list<SyntaxNode> items
token closeBrace

RsElseClause
token keyword
RsProdItem item

RsIfElse base=RsProd
token keyword
token openParen
Expression condition
token closeParen
RsProdItem ifItem
RsElseClause? elseClause

RsRepeat base=RsProd
token keyword
token openParen
Expression expr
token closeParen
RsProdItem item

RsCaseItem final=false
empty

DefaultRsCaseItem base=RsCaseItem
token defaultKeyword
token colon
RsProdItem item
token semi

StandardRsCaseItem base=RsCaseItem
separated_list<Expression> expressions
token colon
RsProdItem item
token semi

RsCase base=RsProd
token keyword
token openParen
Expression expr
token closeParen
list<RsCaseItem> items
token endcase

RandJoinClause
token rand
token join
ParenthesizedExpression? expr

RsWeightClause
token colonEqual
Expression weight
RsProd? codeBlock

RsRule
RandJoinClause? randJoin
list<RsProd> prods
RsWeightClause? weightClause

Production
DataType? dataType
token name
FunctionPortList? portList
token colon
separated_list<RsRule> rules
token semi

RandSequenceStatement base=Statement
token randsequence
token openParen
token firstProduction
token closeParen
list<Production> productions
token endsequence

EventTriggerStatement base=Statement multiKind=true
token trigger
TimingControl? timing
Name name
token semi

kindmap<EventTriggerStatement>
BlockingEventTriggerStatement NonblockingEventTriggerStatement

// ----- MODULES -----

PortList final=false
empty

NonAnsiPort final=false
empty

PortExpression final=false
empty

PortReference base=PortExpression
token name
ElementSelect? select

PortConcatenation base=PortExpression
token openBrace
separated_list<PortReference> references
token closeBrace

EmptyNonAnsiPort base=NonAnsiPort
token placeholder

ImplicitNonAnsiPort base=NonAnsiPort
PortExpression expr

ExplicitNonAnsiPort base=NonAnsiPort
token dot
token name
token openParen
PortExpression? expr
token closeParen

NonAnsiPortList base=PortList
token openParen
separated_list<NonAnsiPort> ports
token closeParen

InterfacePortHeader base=PortHeader
token nameOrKeyword
DotMemberClause? modport

VariablePortHeader base=PortHeader
token constKeyword
token direction
token varKeyword
DataType dataType

NetPortHeader base=PortHeader
token direction
token netType
DataType dataType

ImplicitAnsiPort base=Member
PortHeader header
Declarator declarator

ExplicitAnsiPort base=Member
token direction
token dot
token name
token openParen
Expression? expr
token closeParen

AnsiPortList base=PortList
token openParen
separated_list<Member> ports
token closeParen

WildcardPortList base=PortList
token openParen
token dot
token star
token closeParen

ParameterPortList
token hash
token openParen
separated_list<ParameterDeclarationBase> declarations
token closeParen

ModuleHeader multiKind=true
token moduleKeyword
token lifetime
token name
list<PackageImportDeclaration> imports
ParameterPortList? parameters
PortList? ports
token semi

kindmap<ModuleHeader>
ModuleHeader ProgramHeader InterfaceHeader PackageHeader

ModuleDeclaration base=Member multiKind=true
ModuleHeader header
list<Member> members
token endmodule
NamedBlockClause? blockName

kindmap<ModuleDeclaration>
ModuleDeclaration InterfaceDeclaration ProgramDeclaration PackageDeclaration

AnonymousProgram base=Member
token keyword
token semi
list<Member> members
token endkeyword

// ----- MEMBERS -----

EmptyMember base=Member
tokenlist qualifiers
token semi

ProceduralBlock base=Member multiKind=true
token keyword
Statement statement

kindmap<ProceduralBlock>
InitialBlock FinalBlock AlwaysBlock AlwaysCombBlock AlwaysFFBlock AlwaysLatchBlock

GenerateRegion base=Member
token keyword
list<Member> members
token endgenerate

LoopGenerate base=Member
token keyword
token openParen
token genvar
token identifier
token equals
Expression initialExpr
token semi1
Expression stopExpr
token semi2
Expression iterationExpr
token closeParen
Member block

IfGenerate base=Member
token keyword
token openParen
Expression condition
token closeParen
Member block
ElseClause? elseClause

CaseGenerate base=Member
token keyword
token openParen
Expression condition
token closeParen
list<CaseItem> items
token endCase

GenerateBlock base=Member
NamedLabel? label
token begin
NamedBlockClause? beginName
list<Member> members
token end
NamedBlockClause? endName

DividerClause
token divide
token value

TimeUnitsDeclaration base=Member
token keyword
token time
DividerClause? divider
token semi

PortConnection final=false
list<AttributeInstance> attributes

EmptyPortConnection base=PortConnection
token placeholder

OrderedPortConnection base=PortConnection
PropertyExpr expr

NamedPortConnection base=PortConnection
token dot
token name
token openParen
PropertyExpr? expr
token closeParen

WildcardPortConnection base=PortConnection
token dot
token star

InstanceName
token name
list<VariableDimension> dimensions

HierarchicalInstance
InstanceName? decl
token openParen
separated_list<PortConnection> connections
token closeParen

HierarchyInstantiation base=Member
token type
ParameterValueAssignment? parameters
separated_list<HierarchicalInstance> instances
token semi

PrimitiveInstantiation base=Member
token type
NetStrength? strength
TimingControl? delay
separated_list<HierarchicalInstance> instances
token semi

CheckerInstantiation base=Member
Name type
ParameterValueAssignment? parameters
separated_list<HierarchicalInstance> instances
token semi

CheckerInstanceStatement base=Statement
CheckerInstantiation instance

BindTargetList
token colon
separated_list<Name> targets

BindDirective base=Member
token bind
Name target
BindTargetList? targetInstances
Member instantiation

ClassSpecifier
token colon
token keyword

FunctionPrototype
token keyword
list<ClassSpecifier> specifiers
token lifetime
DataType returnType
Name name
FunctionPortList? portList

FunctionDeclaration base=Member multiKind=true
FunctionPrototype prototype
token semi
list<SyntaxNode> items
token end
NamedBlockClause? endBlockName

kindmap<FunctionDeclaration>
FunctionDeclaration TaskDeclaration

EqualsAssertionArgClause
token equals
PropertyExpr expr

AssertionItemPort
list<AttributeInstance> attributes
token local
token direction
DataType type
token name
list<VariableDimension> dimensions
EqualsAssertionArgClause? defaultValue

AssertionItemPortList
token openParen
separated_list<AssertionItemPort> ports
token closeParen

LetDeclaration base=Member
token let
token identifier
AssertionItemPortList? portList
token equals
Expression expr
token semi

DefaultExtendsClauseArg
token openParen
token defaultKeyword
token closeParen

ExtendsClause
token keyword
Name baseName
ArgumentList? arguments
DefaultExtendsClauseArg? defaultedArg

ImplementsClause
token keyword
separated_list<Name> interfaces

ClassDeclaration base=Member
token virtualOrInterface
token classKeyword
ClassSpecifier? finalSpecifier
token name
ParameterPortList? parameters
ExtendsClause? extendsClause
ImplementsClause? implementsClause
token semi
list<Member> items
token endClass
NamedBlockClause? endBlockName

ClassPropertyDeclaration base=Member
tokenlist qualifiers
Member declaration

ClassMethodDeclaration base=Member
tokenlist qualifiers
FunctionDeclaration declaration

ClassMethodPrototype base=Member
tokenlist qualifiers
FunctionPrototype prototype
token semi

ContinuousAssign base=Member
token assign
DriveStrength? strength
TimingControl? delay
separated_list<Expression> assignments
token semi

DefParamAssignment
Name name
EqualsValueClause setter

DefParam base=Member
token defparam
separated_list<DefParamAssignment> assignments
token semi

ModportClockingPort base=Member
token clocking
token name

ModportPort final=false
empty

ModportNamedPort base=ModportPort
token name

ModportExplicitPort base=ModportPort
token dot
token name
token openParen
Expression? expr
token closeParen

ModportSimplePortList base=Member
token direction
separated_list<ModportPort> ports

ModportSubroutinePort base=ModportPort
FunctionPrototype prototype

ModportSubroutinePortList base=Member
token importExport
separated_list<ModportPort> ports

ModportItem
token name
AnsiPortList ports

ModportDeclaration base=Member
token keyword
separated_list<ModportItem> items
token semi

ClockingSkew
token edge
TimingControl? delay

ClockingDirection
token input
ClockingSkew? inputSkew
token output
ClockingSkew? outputSkew

DefaultSkewItem base=Member
token keyword
ClockingDirection direction
token semi

ClockingItem base=Member
ClockingDirection direction
separated_list<AttributeSpec> decls
token semi

ClockingDeclaration base=Member
token globalOrDefault
token clocking
token blockName
token at
EventExpression event
token semi
list<Member> items
token endClocking
NamedBlockClause? endBlockName

DefaultClockingReference base=Member
token defaultKeyword
token clocking
token name
token semi

DefaultDisableDeclaration base=Member
token defaultKeyword
token disableKeyword
token iffKeyword
Expression expr
token semi

DPIImport base=Member
token keyword
token specString
token property
token c_identifier
token equals
FunctionPrototype method
token semi

DPIExport base=Member
token keyword
token specString
token c_identifier
token equals
token functionOrTask
token name
token semi

ElabSystemTask base=Member
token name
ArgumentList? arguments
token semi

UdpPortDecl final=false
list<AttributeInstance> attributes

UdpOutputPortDecl base=UdpPortDecl
token keyword
token reg
token name
EqualsValueClause? initializer

UdpInputPortDecl base=UdpPortDecl
token keyword
separated_list<IdentifierName> names

UdpPortList final=false
empty

AnsiUdpPortList base=UdpPortList
token openParen
separated_list<UdpPortDecl> ports
token closeParen
token semi

NonAnsiUdpPortList base=UdpPortList
token openParen
separated_list<IdentifierName> ports
token closeParen
token semi

WildcardUdpPortList base=UdpPortList
token openParen
token dot
token star
token closeParen
token semi

UdpInitialStmt
token initial
token name
token equals
Expression value
token semi

UdpFieldBase final=false
empty

UdpEdgeField base=UdpFieldBase
token openParen
token first
token second
token closeParen

UdpSimpleField base=UdpFieldBase
token field

UdpEntry
list<UdpFieldBase> inputs
token colon1
UdpFieldBase? current
token colon2
UdpFieldBase? next
token semi

UdpBody
separated_list<UdpPortDecl> portDecls
UdpInitialStmt? initialStmt
token table
list<UdpEntry> entries
token endtable

UdpDeclaration base=Member
token primitive
token name
UdpPortList portList
UdpBody body
token endprimitive
NamedBlockClause? endBlockName

SpecparamDeclarator
token name
token equals
token openParen
Expression value1
token comma
Expression? value2
token closeParen

SpecparamDeclaration base=Member
token keyword
ImplicitType type
separated_list<SpecparamDeclarator> declarators
token semi

PathSuffix final=false
empty

SimplePathSuffix base=PathSuffix
separated_list<Name> outputs

EdgeSensitivePathSuffix base=PathSuffix
token openParen
separated_list<Name> outputs
token polarityOperator
token colon
Expression expr
token closeParen

PathDescription
token openParen
token edgeIdentifier
separated_list<Name> inputs
token polarityOperator
token pathOperator
PathSuffix suffix
token closeParen

PathDeclaration base=Member
PathDescription desc
token equals
token openParen
separated_list<Expression> delays
token closeParen
token semi

ConditionalPathDeclaration base=Member
token keyword
token openParen
Expression predicate
token closeParen
PathDeclaration path

IfNonePathDeclaration base=Member
token keyword
PathDeclaration path

PulseStyleDeclaration base=Member
token keyword
separated_list<Name> inputs
token semi

TimingCheckArg final=false
empty

EmptyTimingCheckArg base=TimingCheckArg
token placeholder

EdgeDescriptor
token t1
token t2

EdgeControlSpecifier
token openBracket
separated_list<EdgeDescriptor> descriptors
token closeBracket

TimingCheckEventCondition
token tripleAnd
Expression expr

TimingCheckEventArg base=TimingCheckArg
token edge
EdgeControlSpecifier? controlSpecifier
Expression terminal
TimingCheckEventCondition? condition

ExpressionTimingCheckArg base=TimingCheckArg
Expression expr

SystemTimingCheck base=Member
token name
token openParen
separated_list<TimingCheckArg> args
token closeParen
token semi

SpecifyBlock base=Member
token specify
list<Member> items
token endspecify

NetAlias base=Member
token keyword
separated_list<Expression> nets
token semi

ExternModuleDecl base=Member
token externKeyword
list<AttributeInstance> actualAttributes
ModuleHeader header

ExternUdpDecl base=Member
token externKeyword
list<AttributeInstance> actualAttributes
token primitive
token name
UdpPortList portList

ExternInterfaceMethod base=Member
token externKeyword
token forkJoin
FunctionPrototype prototype
token semi

// ----- CONSTRAINTS -----

ConstraintItem final=false
empty

DistWeight
token op
token extraOp
Expression expr

DistItemBase final=false
empty

DistItem base=DistItemBase
Expression range
DistWeight? weight

DefaultDistItem base=DistItemBase
token defaultKeyword
DistWeight? weight

DistConstraintList
token dist
token openBrace
separated_list<DistItemBase> items
token closeBrace

ExpressionOrDist base=Expression
Expression expr
DistConstraintList distribution

ExpressionConstraint base=ConstraintItem
token soft
Expression expr
token semi

UniquenessConstraint base=ConstraintItem
token unique
RangeList ranges
token semi

ImplicationConstraint base=ConstraintItem
Expression left
token arrow
ConstraintItem constraints

ElseConstraintClause
token elseKeyword
ConstraintItem constraints

ConditionalConstraint base=ConstraintItem
token ifKeyword
token openParen
Expression condition
token closeParen
ConstraintItem constraints
ElseConstraintClause? elseClause

LoopConstraint base=ConstraintItem
token foreachKeyword
ForeachLoopList loopList
ConstraintItem constraints

DisableConstraint base=ConstraintItem
token disable
token soft
Expression name
token semi

SolveBeforeConstraint base=ConstraintItem
token solve
separated_list<Expression> beforeExpr
token before
separated_list<Expression> afterExpr
token semi

ConstraintBlock base=ConstraintItem
token openBrace
list<ConstraintItem> items
token closeBrace

ConstraintPrototype base=Member
tokenlist qualifiers
token keyword
list<ClassSpecifier> specifiers
Name name
token semi

ConstraintDeclaration base=Member
tokenlist qualifiers
token keyword
list<ClassSpecifier> specifiers
Name name
ConstraintBlock block

ParenExpressionList
token openParen
separated_list<Expression> expressions
token closeParen

ArrayOrRandomizeMethodExpression base=Expression
Expression method
token with
ParenExpressionList? args
ConstraintBlock? constraints

// ----- COVER GROUPS -----

WithFunctionSample
token with
token function
token sample
FunctionPortList? portList

BlockEventExpression final=false
empty

BinaryBlockEventExpression base=BlockEventExpression
BlockEventExpression left
token orKeyword
BlockEventExpression right

PrimaryBlockEventExpression base=BlockEventExpression
token keyword
Name name

BlockCoverageEvent
token atat
token openParen
BlockEventExpression expr
token closeParen

CovergroupDeclaration base=Member
token covergroup
token extends
token name
FunctionPortList? portList
SyntaxNode? event
token semi
list<Member> members
token endgroup
NamedBlockClause? endBlockName

CoverageOption base=Member
Expression expr
token semi

CoverageIffClause
token iff
token openParen
Expression expr
token closeParen

Coverpoint base=Member
DataType type
NamedLabel? label
token coverpoint
Expression expr
CoverageIffClause? iff
token openBrace
list<Member> members
token closeBrace
token emptySemi

CoverageBinInitializer final=false
empty

DefaultCoverageBinInitializer base=CoverageBinInitializer
token defaultKeyword
token sequenceKeyword

WithClause
token with
token openParen
Expression expr
token closeParen

ExpressionCoverageBinInitializer base=CoverageBinInitializer
Expression expr

RangeCoverageBinInitializer base=CoverageBinInitializer
RangeList ranges
WithClause? withClause

IdWithExprCoverageBinInitializer base=CoverageBinInitializer
token id
WithClause withClause

TransRepeatRange
token openBracket
token specifier
Selector? selector
token closeBracket

TransRange
separated_list<Expression> items
TransRepeatRange? repeat

TransSet
token openParen
separated_list<TransRange> ranges
token closeParen

TransListCoverageBinInitializer base=CoverageBinInitializer
separated_list<TransSet> sets

CoverageBinsArraySize
token openBracket
Expression? expr
token closeBracket

CoverageBins base=Member
token wildcard
token keyword
token name
CoverageBinsArraySize? size
token equals
CoverageBinInitializer initializer
CoverageIffClause? iff
token semi

CoverCross base=Member
NamedLabel? label
token cross
separated_list<IdentifierName> items
CoverageIffClause? iff
token openBrace
list<Member> members
token closeBrace
token emptySemi

BinsSelectExpression final=false
empty

IntersectClause
token intersect
RangeList ranges

BinsSelectConditionExpr base=BinsSelectExpression
token binsof
token openParen
Name name
token closeParen
IntersectClause? intersects

UnaryBinsSelectExpr base=BinsSelectExpression
token op
BinsSelectConditionExpr expr

BinaryBinsSelectExpr base=BinsSelectExpression
BinsSelectExpression left
token op
BinsSelectExpression right

ParenthesizedBinsSelectExpr base=BinsSelectExpression
token openParen
BinsSelectExpression expr
token closeParen

SimpleBinsSelectExpr base=BinsSelectExpression
Expression expr
MatchesClause? matchesClause

BinSelectWithFilterExpr base=BinsSelectExpression
BinsSelectExpression expr
token with
token openParen
Expression filter
token closeParen
MatchesClause? matchesClause

BinsSelection base=Member
token keyword
token name
token equals
BinsSelectExpression expr
CoverageIffClause? iff
token semi

// ----- ASSERTIONS -----

SequenceRepetition
token openBracket
token op
Selector? selector
token closeBracket

SimpleSequenceExpr base=SequenceExpr
Expression expr
SequenceRepetition? repetition

SequenceMatchList
token comma
separated_list<PropertyExpr> items

ParenthesizedSequenceExpr base=SequenceExpr
token openParen
SequenceExpr expr
SequenceMatchList? matchList
token closeParen
SequenceRepetition? repetition

BinarySequenceExpr base=SequenceExpr multiKind=true
SequenceExpr left
token op
SequenceExpr right

kindmap<BinarySequenceExpr>
AndSequenceExpr OrSequenceExpr IntersectSequenceExpr
ThroughoutSequenceExpr WithinSequenceExpr

FirstMatchSequenceExpr base=SequenceExpr
token first_match
token openParen
SequenceExpr expr
SequenceMatchList? matchList
token closeParen

ClockingSequenceExpr base=SequenceExpr
TimingControl event
SequenceExpr expr

DelayedSequenceElement
token doubleHash
Expression? delayVal
token openBracket
token op
Selector? range
token closeBracket
SequenceExpr expr

DelayedSequenceExpr base=SequenceExpr
SequenceExpr? first
list<DelayedSequenceElement> elements

SimplePropertyExpr base=PropertyExpr
SequenceExpr expr

ParenthesizedPropertyExpr base=PropertyExpr
token openParen
PropertyExpr expr
SequenceMatchList? matchList
token closeParen

StrongWeakPropertyExpr base=PropertyExpr
token keyword
token openParen
SequenceExpr expr
token closeParen

UnaryPropertyExpr base=PropertyExpr
token op
PropertyExpr expr

UnarySelectPropertyExpr base=PropertyExpr
token op
token openBracket
Selector? selector
token closeBracket
PropertyExpr expr

BinaryPropertyExpr base=PropertyExpr multiKind=true
PropertyExpr left
token op
PropertyExpr right

kindmap<BinaryPropertyExpr>
AndPropertyExpr OrPropertyExpr IffPropertyExpr UntilPropertyExpr
SUntilPropertyExpr UntilWithPropertyExpr SUntilWithPropertyExpr
ImpliesPropertyExpr ImplicationPropertyExpr FollowedByPropertyExpr

ClockingPropertyExpr base=PropertyExpr
TimingControl event
PropertyExpr? expr

AcceptOnPropertyExpr base=PropertyExpr
token keyword
token openParen
Expression condition
token closeParen
PropertyExpr expr

ElsePropertyClause
token elseKeyword
PropertyExpr expr

ConditionalPropertyExpr base=PropertyExpr
token ifKeyword
token openParen
Expression condition
token closeParen
PropertyExpr expr
ElsePropertyClause? elseClause

PropertyCaseItem final=false
empty

DefaultPropertyCaseItem base=PropertyCaseItem
token defaultKeyword
token colon
PropertyExpr expr
token semi

StandardPropertyCaseItem base=PropertyCaseItem
separated_list<Expression> expressions
token colon
PropertyExpr expr
token semi

CasePropertyExpr base=PropertyExpr
token caseKeyword
token openParen
Expression expr
token closeParen
list<PropertyCaseItem> items
token endcase

LocalVariableDeclaration base=Member
token var
DataType type
separated_list<Declarator> declarators
token semi

PropertyDeclaration base=Member
token keyword
token name
AssertionItemPortList? portList
token semi
list<LocalVariableDeclaration> variables
PropertySpec propertySpec
token optionalSemi
token end
NamedBlockClause? endBlockName

SequenceDeclaration base=Member
token keyword
token name
AssertionItemPortList? portList
token semi
list<LocalVariableDeclaration> variables
SequenceExpr seqExpr
token optionalSemi
token end
NamedBlockClause? endBlockName

CheckerDeclaration base=Member
token keyword
token name
AssertionItemPortList? portList
token semi
list<Member> members
token end
NamedBlockClause? endBlockName

CheckerDataDeclaration base=Member
token rand
DataDeclaration data

// ----- TOP LEVEL -----

CompilationUnit
list<Member> members
token endOfFile

LibraryMap
list<Member> members
token endOfFile

// ----- DIRECTIVES -----

Directive final=false
token directive

SimpleDirective base=Directive multiKind=true

kindmap<SimpleDirective>
CellDefineDirective NoUnconnectedDriveDirective EndCellDefineDirective EndKeywordsDirective
ResetAllDirective UndefineAllDirective DelayModeDistributedDirective DelayModePathDirective
DelayModeUnitDirective DelayModeZeroDirective ProtectDirective EndProtectDirective
ProtectedDirective EndProtectedDirective

IncludeDirective base=Directive
token fileName

ConditionalDirectiveExpression final=false
empty

NamedConditionalDirectiveExpression base=ConditionalDirectiveExpression
token name

UnaryConditionalDirectiveExpression base=ConditionalDirectiveExpression
token op
ConditionalDirectiveExpression operand

BinaryConditionalDirectiveExpression base=ConditionalDirectiveExpression
ConditionalDirectiveExpression left
token op
ConditionalDirectiveExpression right

ParenthesizedConditionalDirectiveExpression base=ConditionalDirectiveExpression
token openParen
ConditionalDirectiveExpression operand
token closeParen

ConditionalBranchDirective base=Directive multiKind=true
ConditionalDirectiveExpression expr
tokenlist disabledTokens

kindmap<ConditionalBranchDirective>
ElsIfDirective IfDefDirective IfNDefDirective

UnconditionalBranchDirective base=Directive multiKind=true
tokenlist disabledTokens

kindmap<UnconditionalBranchDirective>
EndIfDirective ElseDirective

MacroArgumentDefault
token equals
tokenlist tokens

MacroFormalArgument
token name
MacroArgumentDefault? defaultValue

MacroFormalArgumentList
token openParen
separated_list<MacroFormalArgument> args
token closeParen

DefineDirective base=Directive
token name
MacroFormalArgumentList? formalArguments
tokenlist body

MacroActualArgument
tokenlist tokens

MacroActualArgumentList
token openParen
separated_list<MacroActualArgument> args
token closeParen

MacroUsage base=Directive
MacroActualArgumentList? args

TimeScaleDirective base=Directive
token timeUnit
token slash
token timePrecision

DefaultNetTypeDirective base=Directive
token netType

UnconnectedDriveDirective base=Directive
token strength

DefaultDecayTimeDirective base=Directive
token time

DefaultTriregStrengthDirective base=Directive
token strength

LineDirective base=Directive
token lineNumber
token fileName
token level

UndefDirective base=Directive
token name

BeginKeywordsDirective base=Directive
token versionSpecifier

PragmaExpression final=false
empty

SimplePragmaExpression base=PragmaExpression
token value

NameValuePragmaExpression base=PragmaExpression
token name
token equals
PragmaExpression value

NumberPragmaExpression base=PragmaExpression
token size
token base
token value

ParenPragmaExpression base=PragmaExpression
token openParen
separated_list<PragmaExpression> values
token closeParen

PragmaDirective base=Directive
token name
separated_list<PragmaExpression> args

// ----- CONFIG -----

ConfigCellIdentifier
token library
token dot
token cell

ConfigRuleClause final=false
empty

ConfigLiblist base=ConfigRuleClause
token liblist
tokenlist libraries

ConfigUseClause base=ConfigRuleClause
token use
ConfigCellIdentifier? name
ParameterValueAssignment? paramAssignments
token colon
token config

ConfigRule final=false
empty

DefaultConfigRule base=ConfigRule
token defaultKeyword
ConfigLiblist liblist
token semi

ConfigInstanceIdentifier
token dot
token name

InstanceConfigRule base=ConfigRule
token instance
token topModule
list<ConfigInstanceIdentifier> instanceNames
ConfigRuleClause ruleClause
token semi

CellConfigRule base=ConfigRule
token cell
ConfigCellIdentifier name
ConfigRuleClause ruleClause
token semi

ConfigDeclaration base=Member
token config
token name
token semi1
list<ParameterDeclarationStatement> localparams
token design
list<ConfigCellIdentifier> topCells
token semi2
list<ConfigRule> rules
token endconfig
NamedBlockClause? blockName

FilePathSpec
token path

LibraryIncDirClause
token minus
token incdir
separated_list<FilePathSpec> filePaths

LibraryDeclaration base=Member
token library
token name
separated_list<FilePathSpec> filePaths
LibraryIncDirClause? incDirClause
token semi

LibraryIncludeStatement base=Member
token include
FilePathSpec filePath
token semi
