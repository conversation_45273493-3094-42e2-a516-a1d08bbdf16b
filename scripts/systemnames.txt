$bits Bits
$typename Typename
$isunbounded IsUnbounded
$low Low
$high High
$left Left
$right Right
$size Size
$increment Increment
$dimensions Dimensions
$unpacked_dimensions UnpackedDimensions

$rtoi Rtoi
$itor Itor
$realtobits RealToBits
$bitstoreal BitsToReal
$shortrealtobits ShortrealToBits
$bitstoshortreal BitsToShortreal
$signed Signed
$unsigned Unsigned

$coverage_control CoverageControl
$coverage_get_max CoverageGetMax
$coverage_get CoverageGet
$coverage_merge CoverageMerge
$coverage_save CoverageSave
$get_coverage GetCoverage
$set_coverage_db_name SetCoverageDbName
$load_coverage_db LoadCoverageDb

$clog2 Clog2
$countbits CountBits
$countones CountOnes
$onehot OneHot
$onehot0 OneHot0
$isunknown IsUnknown
$ln Ln
$log10 Log10
$exp Exp
$sqrt Sqrt
$floor Floor
$ceil Ceil
$sin Sin
$cos Cos
$tan Tan
$asin Asin
$acos Acos
$atan Atan
$sinh Sinh
$cosh Cosh
$tanh Tanh
$asinh Asinh
$acosh Acosh
$atanh Atanh
$pow Pow
$atan2 Atan2
$hypot Hypot

$value$plusargs ValuePlusArgs
$global_clock GlobalClock
$sformatf SFormatF
$psprintf PSPrintF
$inferred_clock InferredClock
$inferred_disable InferredDisable
randomize Randomize
triggered Triggered
matched Matched

$ferror FError
$fgets FGets
$fscanf FScanf
$sscanf SScanf
$fread FRead
$fopen FOpen
$fclose FClose
$fgetc FGetC
$ungetc UngetC
$ftell FTell
$fseek FSeek
$rewind Rewind
$fflush FFlush
$feof FEof

$rose Rose
$fell Fell
$stable Stable
$changed Changed
$past_gclk PastGclk
$rose_gclk RoseGclk
$fell_gclk FellGclk
$stable_gclk StableGclk
$changed_gclk ChangedGclk
$future_gclk FutureGclk
$rising_gclk RisingGclk
$falling_gclk FallingGclk
$steady_gclk SteadyGclk
$changing_gclk ChangingGclk
$sampled Sampled
$past Past

$stacktrace Stacktrace
$countdrivers CountDrivers
$getpattern GetPattern
$test$plusargs TestPlusArgs
$time Time
$stime STime
$realtime RealTime
$random Random
$urandom URandom
$urandom_range URandomRange
$reset_count ResetCount
$reset_value ResetValue
$timeunit TimeUnit
$timeprecision TimePrecision
$scale Scale
$sdf_annotate SdfAnnotate

$dist_uniform DistUniform
$dist_normal DistNormal
$dist_exponential DistExponential
$dist_poisson DistPoisson
$dist_chi_square DistChiSquare
$dist_t DistT
$dist_erlang DistErlang

$fatal Fatal
$static_assert StaticAssert
$cast Cast
$info Info
$warning Warning
$error Error
$finish Finish
$stop Stop

$assertcontrol AssertControl
$asserton AssertOn
$assertoff AssertOff
$assertkill AssertKill
$assertpasson AssertPassOn
$assertpassoff AssertPassOff
$assertfailon AssertFailOn
$assertfailoff AssertFailOff
$assertnonvacuouson AssertNonvacuousOn
$assertvacuousoff AssertVacuousOff

$display Display
$displayb DisplayB
$displayo DisplayO
$displayh DisplayH
$write Write
$writeb WriteB
$writeo WriteO
$writeh WriteH
$strobe Strobe
$strobeb StrobeB
$strobeo StrobeO
$strobeh StrobeH
$monitor Monitor
$monitorb MonitorB
$monitoro MonitorO
$monitorh MonitorH
$fdisplay FDisplay
$fdisplayb FDisplayB
$fdisplayo FDisplayO
$fdisplayh FDisplayH
$fwrite FWrite
$fwriteb FWriteB
$fwriteo FWriteO
$fwriteh FWriteH
$fstrobe FStrobe
$fstrobeb FStrobeB
$fstrobeo FStrobeO
$fstrobeh FStrobeH
$fmonitor FMonitor
$fmonitorb FMonitorB
$fmonitoro FMonitorO
$fmonitorh FMonitorH
$swrite SWrite
$swriteb SWriteB
$swriteo SWriteO
$swriteh SWriteH

$sformat SFormat
$printtimescale PrintTimeScale
$dumpvars DumpVars
$dumpports DumpPorts
$showvars ShowVars

$readmemb ReadMemB
$readmemh ReadMemH
$writememb WriteMemB
$writememh WriteMemH
$sreadmemb SReadMemB
$sreadmemh SReadMemH
$system System
$list List
$scope Scope
$exit Exit
$timeformat TimeFormat
$monitoron MonitorOn
$monitoroff MonitorOff

$dumpfile DumpFile
$dumpon DumpOn
$dumpoff DumpOff
$dumpall DumpAll
$dumplimit DumpLimit
$dumpflush DumpFlush
$dumpportson DumpPortsOn
$dumpportsoff DumpPortsOff
$dumpportsall DumpPortsAll
$dumpportslimit DumpPortsLimit
$dumpportsflush DumpPortsFlush

$input Input
$key Key
$nokey NoKey
$log Log
$nolog NoLog
$reset Reset
$save Save
$restart Restart
$incsave IncSave
$showscopes ShowScopes

$q_initialize QInitialize
$q_add QAdd
$q_remove QRemove
$q_exam QExam
$q_full QFull

$async$and$array AsyncAndArray
$sync$and$array SyncAndArray
$async$and$plane AsyncAndPlane
$sync$and$plane SyncAndPlane
$async$nand$array AsyncNandArray
$sync$nand$array SyncNandArray
$async$nand$plane AsyncNandPlane
$sync$nand$plane SyncNandPlane
$async$or$array AsyncOrArray
$sync$or$array SyncOrArray
$async$or$plane AsyncOrPlane
$sync$or$plane SyncOrPlane
$async$nor$array AsyncNorArray
$sync$nor$array SyncNorArray
$async$nor$plane AsyncNorPlane
$sync$nor$plane SyncNorPlane

reverse Reverse
delete Delete
exists Exists
insert Insert
index Index
map Map
size ArraySize
or Or
and And
xor XOr
sum Sum
product Product
find Find
find_index FindIndex
find_first FindFirst
find_first_index FindFirstIndex
find_last FindLast
find_last_index FindLastIndex
min Min
max Max
unique Unique
unique_index UniqueIndex
sort Sort
rsort Rsort
shuffle Shuffle
num Num
first First
last Last
next Next
prev Prev
pop_front PopFront
pop_back PopBack
push_front PushFront
push_back PushBack
name Name
len Len
putc Putc
getc Getc
substr Substr
toupper ToUpper
tolower ToLower
compare Compare
icompare ICompare
atoi AToI
atohex AToHex
atooct AToOct
atobin AToBin
atoreal AToReal
itoa IToA
hextoa HexToA
octtoa OctToA
bintoa BinToA
realtoa RealToA
rand_mode RandMode
constraint_mode ConstraintMode
