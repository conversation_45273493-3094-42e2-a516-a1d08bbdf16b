Checks: >
    -*,
    clang-analyzer-*,
    bugprone-*,
    performance-*,
    modernize-*,
    portability-*,
    misc-*,
    -modernize-use-auto,
    -modernize-use-trailing-return-type,
    -modernize-raw-string-literal,
    -modernize-use-nodiscard,
    -modernize-avoid-c-arrays,
    -modernize-use-emplace,
    -modernize-return-braced-init-list,
    -modernize-loop-convert,
    -modernize-use-designated-initializers,
    -modernize-use-ranges,
    -modernize-use-integer-sign-comparison,
    -bugprone-suspicious-semicolon,
    -bugprone-branch-clone,
    -bugprone-sizeof-expression,
    -bugprone-easily-swappable-parameters,
    -bugprone-implicit-widening-of-multiplication-result,
    -bugprone-not-null-terminated-result,
    -bugprone-unchecked-optional-access,
    -bugprone-assignment-in-if-condition,
    -bugprone-switch-missing-default-case,
    -bugprone-suspicious-stringview-data-usage,
    -bugprone-return-const-ref-from-parameter,
    -clang-analyzer-cplusplus.NewDelete*,
    -clang-analyzer-cplusplus.InnerPointer,
    -clang-analyzer-core.NullDereference,
    -clang-analyzer-core.NonNullParamChecker,
    -clang-analyzer-core.UndefinedBinaryOperatorResult,
    -clang-analyzer-core.uninitialized.Assign,
    -clang-analyzer-optin.cplusplus.UninitializedObject,
    -misc-non-private-member-variables-in-classes,
    -misc-no-recursion,
    -misc-const-correctness,
    -misc-use-anonymous-namespace,
    -misc-include-cleaner,
    -misc-header-include-cycle,
    -misc-use-internal-linkage,
    -performance-no-int-to-ptr,
    -performance-enum-size,
    -portability-simd-intrinsics,
    -portability-template-virtual-member-function,
    -performance-unnecessary-value-param
