# ~~~
# SPDX-FileCopyrightText: <PERSON>
# SPDX-License-Identifier: MIT
# ~~~

# Internal file used to force a reconfiguration if the git commit changes.
set(HEAD_HASH)

file(READ "@HEAD_FILE@" HEAD_CONTENTS LIMIT 1024)

string(STRIP "${HEAD_CONTENTS}" HEAD_CONTENTS)
if(HEAD_CONTENTS MATCHES "ref")
  # named branch
  string(REPLACE "ref: " "" HEAD_REF "${HEAD_CONTENTS}")
  if(EXISTS "@GIT_DIR@/${HEAD_REF}")
    configure_file("@GIT_DIR@/${HEAD_REF}" "@GIT_DATA@/head-ref" COPYONLY)
  else()
    configure_file("@GIT_DIR@/packed-refs" "@GIT_DATA@/packed-refs" COPYONLY)
    file(READ "@GIT_DATA@/packed-refs" PACKED_REFS)
    if(${PACKED_REFS} MATCHES "([0-9a-z]*) ${HEAD_REF}")
      set(HEAD_HASH "${CMAKE_MATCH_1}")
    endif()
  endif()
else()
  # detached HEAD
  configure_file("@GIT_DIR@/HEAD" "@GIT_DATA@/head-ref" COPYONLY)
endif()

if(NOT HEAD_HASH)
  file(READ "@GIT_DATA@/head-ref" HEAD_HASH LIMIT 1024)
  string(STRIP "${HEAD_HASH}" HEAD_HASH)
endif()
