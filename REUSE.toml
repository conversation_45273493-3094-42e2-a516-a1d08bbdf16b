version = 1
SPDX-PackageName = "slang"
SPDX-PackageSupplier = "<PERSON> <<EMAIL>>"
SPDX-PackageDownloadLocation = "https://sv-lang.com/"

[[annotations]]
path = "**"
precedence = "aggregate"
SPDX-FileCopyrightText = "<PERSON>"
SPDX-License-Identifier = "MIT"

[[annotations]]
path = "external/boost_unordered.hpp"
precedence = "aggregate"
SPDX-FileCopyrightText = "Boost authors"
SPDX-License-Identifier = "BSL-1.0"

[[annotations]]
path = "external/expected.hpp"
precedence = "aggregate"
SPDX-FileCopyrightText = "<PERSON>"
SPDX-License-Identifier = "BSL-1.0"

[[annotations]]
path = "external/BS_thread_pool.hpp"
precedence = "aggregate"
SPDX-FileCopyrightText = "Barak Shoshany"
SPDX-License-Identifier = "MIT"
