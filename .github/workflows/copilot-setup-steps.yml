name: "Copilot Setup Steps"

# Allow testing of the setup steps from your repository's "Actions" tab.
on: workflow_dispatch

jobs:
  # The job MUST be called `copilot-setup-steps` or it will not be picked up by Copilot.
  copilot-setup-steps:
    runs-on: ubuntu-latest

    # Set the permissions to the lowest permissions possible needed for your steps.
    # Copilot will be given its own token for its operations.
    permissions:
      # If you want to clone the repository as part of your setup steps, for example to install dependencies, you'll need the `contents: read` permission. If you don't clone the repository in your setup steps, <PERSON><PERSON><PERSON> will do this for you automatically after the steps complete.
      contents: read

    # You can define any steps you want, and they will run before the agent starts.
    # If you do not check out your code, <PERSON><PERSON><PERSON> will do this for you.
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest pre-commit pybind11 scikit-build-core
          pre-commit install
          pre-commit run

      - name: Configure and Build
        run: |
          cmake -B build
          cmake --build build -j8
