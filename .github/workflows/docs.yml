name: docs
description: Build and deploy documentation (both C++/slang and Python/pyslang)

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Install Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'
    - name: Install dependencies
      run: |
        pip install jinja2 pygments docutils
        wget --quiet https://sourceforge.net/projects/doxygen/files/rel-1.13.2/doxygen-1.13.2.linux.bin.tar.gz
        tar -xf doxygen-1.13.2.linux.bin.tar.gz
        cp doxygen-1.13.2/bin/doxygen /usr/local/bin
    - name: Build slang and pyslang docs via CMake
      run: |
        cmake -B build -DSLANG_INCLUDE_DOCS=ON -DSLANG_INCLUDE_TESTS=OFF
        cmake --build build --target docs -j8
    - name: Compress docs artifacts
      run: |
        tar -czvf slang.tar.gz build/docs build/bin/slang
    - name: Upload artifact for preview
      uses: actions/upload-artifact@v4
      with:
        name: slang-docs
        path: slang.tar.gz
        retention-days: 4
    - name: Upload
      if: github.event_name != 'pull_request' && github.repository == 'MikePopoloski/slang'
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SSH_DEPLOY_HOST }}
        username: ${{ secrets.SSH_DEPLOY_USERNAME }}
        port: ${{ secrets.SSH_DEPLOY_PORT }}
        key: ${{ secrets.SSH_DEPLOY_KEY }}
        passphrase: ${{ secrets.SSH_DEPLOY_PASSPHRASE }}
        source: slang.tar.gz
        target: upload/
    - name: Deploy
      if: github.event_name != 'pull_request' && github.repository == 'MikePopoloski/slang'
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SSH_DEPLOY_HOST }}
        username: ${{ secrets.SSH_DEPLOY_USERNAME }}
        port: ${{ secrets.SSH_DEPLOY_PORT }}
        key: ${{ secrets.SSH_DEPLOY_KEY }}
        passphrase: ${{ secrets.SSH_DEPLOY_PASSPHRASE }}
        script: slang-website/deploy_docs.sh
