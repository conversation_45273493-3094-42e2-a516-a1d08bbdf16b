name: "Python Build"

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - master

jobs:
  build:
    name: Build with <PERSON><PERSON>
    runs-on: ${{ matrix.platform }}
    strategy:
      fail-fast: false
      matrix:
        platform: [windows-latest, macos-latest, ubuntu-latest]
        python-version: ["3.11", "pypy-3.9"]
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - uses: maxim-lobanov/setup-xcode@v1
      if: matrix.platform == 'macos-latest'
      with:
        xcode-version: 'latest'
    - uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest
    - name: Build and install
      run: pip install --verbose .
    - name: Test
      run: pytest
