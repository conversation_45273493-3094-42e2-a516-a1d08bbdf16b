name: "Python Dist"
description: "Build and upload Python distributions to PyPI"

on:
  workflow_dispatch:
  release:
    types:
      - published

jobs:
  build_sdist:
    name: Build SDist
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.x"
    - name: Generate Stubs
      run: |
        python -m venv venv/
        ./venv/bin/python -m pip install --upgrade pip
        ./venv/bin/python -m pip install build twine pybind11-stubgen
        ./venv/bin/python -m pip install .
        mkdir pyslang/pyslang/
        ./venv/bin/python -m pybind11_stubgen pyslang -o pyslang/ --root-suffix ''
        test -f pyslang/pyslang/__init__.pyi
    - name: Build SDist
      run: pipx run build --sdist
    - name: Check metadata
      run: pipx run twine check dist/*
    - uses: actions/upload-artifact@v4
      with:
        path: dist/*.tar.gz
        name: artifact-sdist

  build_wheels:
    name: Wheels on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, ubuntu-24.04-arm, macos-latest, windows-latest]
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.x"
    - uses: maxim-lobanov/setup-xcode@v1
      if: matrix.os == 'macos-latest'
      with:
        xcode-version: 'latest'
    - name: Generate Stubs
      if: matrix.os != 'windows-latest'
      run: |
        python -m venv venv/
        ./venv/bin/python -m pip install --upgrade pip
        ./venv/bin/python -m pip install build twine pybind11-stubgen
        ./venv/bin/python -m pip install .
        mkdir pyslang/pyslang/
        ./venv/bin/python -m pybind11_stubgen pyslang -o pyslang/ --root-suffix ''
        test -f pyslang/pyslang/__init__.pyi
    - name: Generate Stubs (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        python -m venv venv/
        ./venv/Scripts/python -m pip install --upgrade pip
        ./venv/Scripts/python -m pip install build twine pybind11-stubgen
        ./venv/Scripts/python -m pip install .
        mkdir pyslang/pyslang/
        ./venv/Scripts/python -m pybind11_stubgen pyslang -o pyslang/ --root-suffix ''
        test -f pyslang/pyslang/__init__.pyi
    - uses: pypa/cibuildwheel@v2.22.0
      env:
        CIBW_ARCHS_MACOS: auto universal2
        MACOSX_DEPLOYMENT_TARGET: "11.0"
    - name: Verify clean directory
      run: git diff --exit-code
      shell: bash
    - uses: actions/upload-artifact@v4
      with:
        path: wheelhouse/*.whl
        name: artifact-${{ matrix.os }}

  upload_test:
    name: Upload test
    needs: [build_wheels, build_sdist]
    runs-on: ubuntu-latest
    environment: pypi-test
    permissions:
      id-token: write
    steps:
    - uses: actions/setup-python@v5
      with:
        python-version: "3.x"
    - uses: actions/download-artifact@v4
      with:
        pattern: artifact-*
        path: dist
        merge-multiple: true
    - uses: pypa/gh-action-pypi-publish@release/v1
      with:
        repository-url: https://test.pypi.org/legacy/
        verbose: true
        skip-existing: true

  upload_official:
    name: Upload official
    needs: [build_wheels, build_sdist]
    runs-on: ubuntu-latest
    environment: pypi
    permissions:
      id-token: write
    if: github.event_name == 'workflow_dispatch'
    steps:
    - uses: actions/setup-python@v5
      with:
        python-version: "3.x"
    - uses: actions/download-artifact@v4
      with:
        pattern: artifact-*
        path: dist
        merge-multiple: true
    - uses: pypa/gh-action-pypi-publish@release/v1
      with:
        verbose: true
