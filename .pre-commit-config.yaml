# To use:
#
#     pre-commit run -a
#
# Or:
#
#     pre-commit install  # (runs every time you commit in git)
#
# To update this file:
#
#     pre-commit autoupdate
#
# See https://github.com/pre-commit/pre-commit

ci:
  autoupdate_commit_msg: "chore: update pre-commit hooks"
  autofix_commit_msg: "style: pre-commit fixes"

exclude: grammar.md

repos:
# Standard hooks
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v5.0.0
  hooks:
  - id: check-case-conflict
  - id: check-merge-conflict
  - id: check-yaml
    args: [--allow-multiple-documents]
  - id: debug-statements
  - id: end-of-file-fixer
  - id: fix-byte-order-marker
  - id: mixed-line-ending
    args: [--fix,lf]
  - id: trailing-whitespace

# Black, the python code formatter
- repo: https://github.com/psf/black
  rev: 25.1.0
  hooks:
  - id: black
    exclude: ^(docs)

# flake8 for python linting
- repo: https://github.com/pycqa/flake8
  rev: 7.3.0
  hooks:
  - id: flake8
    additional_dependencies: [flake8-bugbear]

# Sort your python imports in a standard form
- repo: https://github.com/PyCQA/isort
  rev: 6.0.1
  hooks:
  - id: isort

# CMake formatting
- repo: https://github.com/cheshirekow/cmake-format-precommit
  rev: v0.6.13
  hooks:
  - id: cmake-format
    additional_dependencies: [pyyaml]
    types: [file]
    files: (\.cmake|CMakeLists.txt)(.in)?$

# clang-format
- repo: https://github.com/pre-commit/mirrors-clang-format
  rev: v20.1.7
  hooks:
  - id: clang-format
    exclude: ^external/.*
