# SystemVerilog LSP 符号分析器架构

## 🏗️ 架构概览

SystemVerilog LSP 项目中有三个主要的符号分析组件，它们形成了一个分层的架构：

```
ComprehensiveSymbolAnalyzer (最高层)
           ↓
EnhancedSymbolExtractor (中间层)
           ↓
SymbolExtractor (基础层)
```

## 📊 组件对比分析

### 1. SymbolExtractor (基础层)

**位置**: `src/sv_lsp/analysis/symbol_extractor.py`

**设计目标**:
- 提供基本的符号提取功能
- 简单、轻量级的实现
- 向后兼容性

**核心特性**:
```python
class SymbolExtractor:
    def extract_from_file(self, file_path: str) -> List[SymbolInfo]
    def extract_from_text(self, text: str, file_path: str) -> List[SymbolInfo]
    def _map_symbol_kind(self, slang_kind) -> SymbolKind
```

**支持的符号类型**:
- ✅ 基本模块声明
- ✅ 简单变量声明
- ✅ 端口声明
- ✅ 参数声明
- ❌ 复杂类型系统
- ❌ 嵌套作用域
- ❌ 语义关系

**使用场景**:
- 快速原型开发
- 简单项目分析
- 基础LSP功能

### 2. EnhancedSymbolExtractor (中间层)

**位置**: `src/sv_lsp/analysis/enhanced_symbol_extractor.py`

**设计目标**:
- 增强的符号提取能力
- 语义模型集成
- 关系分析

**核心特性**:
```python
class EnhancedSymbolExtractor:
    def __init__(self):
        self.semantic_model = SemanticModel()
    
    def extract_from_text(self, text: str, file_path: str) -> List[SymbolInfo]
    def get_semantic_model(self) -> SemanticModel
    def find_symbol_at_position(self, file_path: str, line: int, column: int) -> Optional[SymbolInfo]
    def get_symbol_references(self, symbol: SymbolInfo) -> List[Location]
```

**支持的符号类型**:
- ✅ 模块和接口
- ✅ 变量和端口
- ✅ 参数声明
- ✅ 基本层次关系
- ✅ 位置映射
- ✅ 引用跟踪
- ❌ 完整类型系统
- ❌ 复杂语言构造

**使用场景**:
- 中等复杂度项目
- 需要语义分析的LSP功能
- 引用查找和导航

### 3. ComprehensiveSymbolAnalyzer (最高层)

**位置**: `src/sv_lsp/analysis/comprehensive_symbol_analyzer.py`

**设计目标**:
- 完整的SystemVerilog语言支持
- 所有101种SymbolKind的支持
- 高级语义分析

**核心特性**:
```python
class ComprehensiveSymbolAnalyzer:
    def __init__(self):
        self.semantic_model = SemanticModel()
        self.symbol_stack: List[SymbolInfo] = []
        self.current_scope: Optional[SymbolInfo] = None
    
    def analyze_text(self, text: str, file_path: str) -> List[SymbolInfo]
    def get_semantic_model(self) -> SemanticModel
    def get_statistics(self) -> Dict[str, Any]
```

**支持的符号类型**:
- ✅ 所有模块层次结构
- ✅ 完整类型系统
- ✅ 面向对象特性
- ✅ 验证构造
- ✅ 时序和时钟
- ✅ 生成块
- ✅ 断言和属性
- ✅ 覆盖组
- ✅ 约束块

**使用场景**:
- 大型复杂项目
- 完整的IDE功能
- 高级语言分析

## 🔄 演进关系

### 历史发展
```
SymbolExtractor (v1.0)
    ↓ 需要语义分析
EnhancedSymbolExtractor (v2.0)
    ↓ 需要完整语言支持
ComprehensiveSymbolAnalyzer (v3.0)
```

### 技术演进
1. **基础提取** → **语义分析** → **完整支持**
2. **简单映射** → **关系建立** → **层次管理**
3. **基本类型** → **增强类型** → **完整类型系统**

## 🎯 使用指南

### 选择合适的分析器

#### 使用 SymbolExtractor 当:
```python
# 简单项目，只需要基本符号信息
extractor = SymbolExtractor()
symbols = extractor.extract_from_file("simple.sv")
```

#### 使用 EnhancedSymbolExtractor 当:
```python
# 需要语义分析和引用跟踪
extractor = EnhancedSymbolExtractor()
symbols = extractor.extract_from_text(code, "file.sv")
semantic_model = extractor.get_semantic_model()
```

#### 使用 ComprehensiveSymbolAnalyzer 当:
```python
# 需要完整的SystemVerilog支持
analyzer = ComprehensiveSymbolAnalyzer()
symbols = analyzer.analyze_text(complex_code, "complex.sv")
stats = analyzer.get_statistics()
```

## 🔧 内部实现差异

### 符号提取策略

#### SymbolExtractor
```python
# 使用简单的访问者模式
def visit_func(obj):
    visitor.visit(obj)
tree.root.visit(visit_func)
```

#### EnhancedSymbolExtractor
```python
# 构建语法树表示
syntax_root = self._build_syntax_tree(tree.root)
self._extract_symbols_recursive(syntax_root, symbols)
```

#### ComprehensiveSymbolAnalyzer
```python
# 直接使用pyslang访问者，支持所有节点类型
def visit_node(node):
    symbol = self._create_symbol_from_node(node, kind_str, location)
    if symbol:
        self._add_to_semantic_model(symbol, location)
        self._manage_scope_stack(symbol)
```

### 语义模型集成

#### SymbolExtractor
- ❌ 无语义模型
- ❌ 无关系跟踪
- ❌ 无位置映射

#### EnhancedSymbolExtractor
- ✅ 基础语义模型
- ✅ 简单关系跟踪
- ✅ 位置映射

#### ComprehensiveSymbolAnalyzer
- ✅ 完整语义模型
- ✅ 层次关系管理
- ✅ 作用域栈管理
- ✅ 统计信息收集

## 🚀 性能特征

### 内存使用
```
SymbolExtractor:           低内存使用
EnhancedSymbolExtractor:   中等内存使用 (语义模型)
ComprehensiveSymbolAnalyzer: 高内存使用 (完整分析)
```

### 处理速度
```
SymbolExtractor:           最快 (简单提取)
EnhancedSymbolExtractor:   中等 (语义分析)
ComprehensiveSymbolAnalyzer: 较慢 (完整分析)
```

### 准确性
```
SymbolExtractor:           基础准确性
EnhancedSymbolExtractor:   良好准确性
ComprehensiveSymbolAnalyzer: 最高准确性
```

## 🔮 未来发展

### 统一接口
计划开发统一的分析器接口：
```python
class UnifiedSymbolAnalyzer:
    def __init__(self, mode: AnalysisMode):
        if mode == AnalysisMode.BASIC:
            self.analyzer = SymbolExtractor()
        elif mode == AnalysisMode.ENHANCED:
            self.analyzer = EnhancedSymbolExtractor()
        elif mode == AnalysisMode.COMPREHENSIVE:
            self.analyzer = ComprehensiveSymbolAnalyzer()
```

### 自适应分析
根据文件大小和复杂度自动选择合适的分析器：
```python
def auto_select_analyzer(file_size: int, complexity: float) -> SymbolAnalyzer:
    if file_size < 1000 and complexity < 0.3:
        return SymbolExtractor()
    elif file_size < 10000 and complexity < 0.7:
        return EnhancedSymbolExtractor()
    else:
        return ComprehensiveSymbolAnalyzer()
```

## 📝 总结

三个分析器形成了一个完整的生态系统：

- **SymbolExtractor**: 快速、简单、轻量级
- **EnhancedSymbolExtractor**: 平衡、语义感知、关系分析
- **ComprehensiveSymbolAnalyzer**: 完整、准确、功能丰富

选择合适的分析器取决于项目需求、性能要求和功能复杂度。对于大多数现代SystemVerilog项目，推荐使用 `ComprehensiveSymbolAnalyzer` 以获得最佳的语言支持体验。
