# SystemVerilog LSP Development Guide

This document provides guidance for developing the SystemVerilog Language Server Protocol implementation.

## Quick Start

1. **<PERSON><PERSON> and setup the project:**
   ```bash
   cd sv-lsp
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   python scripts/dev_setup.py
   ```

2. **Verify installation:**
   ```bash
   pytest tests/
   ```

## Project Structure

```
sv-lsp/
├── src/sv_lsp/           # Main source code
│   ├── core/             # Core functionality
│   ├── lsp/              # LSP protocol implementation
│   ├── analysis/         # Code analysis components
│   └── utils/            # Utility functions
├── tests/                # Test suite
├── examples/             # Example SystemVerilog files
├── scripts/              # Development scripts
└── docs/                 # Documentation
```

## Development Workflow

### 1. Setting Up Development Environment

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate

# Install in development mode
pip install -e .[dev]

# Install pre-commit hooks (optional)
pre-commit install
```

### 2. Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src/sv_lsp --cov-report=html

# Run specific test file
pytest tests/test_types.py

# Run with verbose output
pytest -v
```

### 3. Code Quality

```bash
# Format code
black src tests
isort src tests

# Check types
mypy src

# Lint code
flake8 src tests
```

### 4. Development Tasks

Use the Makefile for common tasks:

```bash
make help           # Show available targets
make test           # Run tests
make format         # Format code
make type-check     # Run type checking
make lint           # Run linting
make clean          # Clean build artifacts
```

## Implementation Phases

### Phase 1: Basic Infrastructure ✅
- [x] Project structure
- [x] Core type definitions
- [x] Basic utilities
- [x] Test framework setup

### Phase 2: pyslang Integration (Next)
- [ ] pyslang wrapper
- [ ] Basic syntax tree parsing
- [ ] Symbol extraction

### Phase 3: Symbol Management
- [ ] Symbol table construction
- [ ] Symbol indexing
- [ ] Symbol relationships

### Phase 4: Position Mapping
- [ ] Position to symbol mapping
- [ ] Reverse symbol lookup
- [ ] Incremental updates

### Phase 5: LSP Protocol
- [ ] JSON-RPC communication
- [ ] LSP message handling
- [ ] Basic LSP lifecycle

### Phase 6: Navigation Features
- [ ] Go to definition
- [ ] Find references
- [ ] Hover information

## Key Components to Implement

### 1. PyslangWrapper (`src/sv_lsp/utils/pyslang_wrapper.py`)
Wrapper around pyslang for easier integration:
- Parse SystemVerilog files
- Extract symbol information
- Handle compilation errors

### 2. SymbolManager (`src/sv_lsp/core/symbol_manager.py`)
Manages symbol tables:
- Build symbol index from AST
- Maintain symbol relationships
- Handle symbol queries

### 3. PositionMapper (`src/sv_lsp/core/position_mapper.py`)
Maps positions to symbols:
- Position-based symbol lookup
- Range-based queries
- Incremental updates

### 4. LSP Server (`src/sv_lsp/lsp/server.py`)
Main LSP server implementation:
- Handle LSP protocol
- Route requests to handlers
- Manage client communication

## Testing Strategy

### Unit Tests
- Test individual components in isolation
- Mock external dependencies
- Focus on core logic

### Integration Tests
- Test component interactions
- Use real SystemVerilog files
- Verify end-to-end functionality

### Performance Tests
- Test with large files
- Measure symbol lookup performance
- Profile memory usage

## Debugging

### Enable Debug Logging
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Test with Sample Files
Use the example files in `examples/` for testing:
```bash
# Test symbol extraction
python -c "
from sv_lsp.utils.pyslang_wrapper import PyslangWrapper
wrapper = PyslangWrapper()
symbols = wrapper.extract_symbols('examples/simple_module.sv')
print(symbols)
"
```

### LSP Client Testing
Test the LSP server with a real client:
1. Start the server: `sv-lsp`
2. Connect with VS Code or vim
3. Test navigation features

## Contributing

1. Create a feature branch
2. Implement changes with tests
3. Run the full test suite
4. Format and lint code
5. Submit a pull request

## Common Issues

### pyslang Installation
If pyslang installation fails:
```bash
# Try installing from conda-forge
conda install -c conda-forge pyslang

# Or build from source
git clone https://github.com/MikePopoloski/slang.git
cd slang
pip install .
```

### Import Errors
If you get import errors:
```bash
# Reinstall in development mode
pip install -e .

# Check PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
```

### Test Failures
If tests fail:
```bash
# Run with more verbose output
pytest -vvv --tb=long

# Run a specific test
pytest tests/test_types.py::TestLocation::test_location_creation -v
```

## Resources

- [LSP Specification](https://microsoft.github.io/language-server-protocol/)
- [pyslang Documentation](https://sv-lang.com/pyslang/)
- [pygls Framework](https://github.com/openlawlibrary/pygls)
- [SystemVerilog IEEE Standard](https://ieeexplore.ieee.org/document/8299595)
