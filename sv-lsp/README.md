# SystemVerilog Language Server Protocol (sv-lsp)

A Language Server Protocol (LSP) implementation for SystemVerilog based on [pyslang](https://github.com/MikePopoloski/slang).

## Features

- **Go to Definition**: Jump to symbol definitions (variables, functions, modules, etc.)
- **Find References**: Find all references to a symbol
- **Hover Information**: Display symbol information on hover
- **Symbol Navigation**: Navigate through SystemVerilog code with IDE support

## Architecture

This LSP server is built on top of pyslang, which provides:
- Complete SystemVerilog parsing and semantic analysis
- Accurate symbol table construction
- Comprehensive AST representation
- IEEE 1800-2017 compliance

### Core Components

- **LSP Protocol Handler**: Manages JSON-RPC communication with editors
- **Symbol Manager**: Builds and maintains symbol tables using pyslang
- **Position Mapper**: Maps source positions to symbols and vice versa
- **Workspace Manager**: Handles multi-file projects and dependencies

## Installation

### From PyPI (when available)

```bash
pip install sv-lsp
```

### From Source

```bash
git clone https://github.com/your-org/sv-lsp.git
cd sv-lsp
pip install -e .[dev]
```

## Usage

### Command Line

Start the LSP server:

```bash
sv-lsp
```

The server communicates via stdin/stdout using the LSP protocol.

### VS Code Integration

1. Install the SystemVerilog extension that supports this LSP server
2. Configure the extension to use `sv-lsp` as the language server

### Vim/Neovim Integration

Configure your LSP client to use `sv-lsp` for SystemVerilog files:

```lua
-- For nvim-lspconfig
require'lspconfig'.sv_lsp.setup{
  cmd = {"sv-lsp"},
  filetypes = {"systemverilog", "verilog"},
}
```

## Configuration

The LSP server can be configured through workspace settings or command-line arguments:

```json
{
  "sv-lsp": {
    "includePaths": ["./include", "./src"],
    "defines": ["DEBUG=1"],
    "maxErrors": 100,
    "enableDiagnostics": true
  }
}
```

## Development

### Setup Development Environment

```bash
git clone https://github.com/your-org/sv-lsp.git
cd sv-lsp
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -e .[dev]
```

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black src tests
isort src tests
```

### Type Checking

```bash
mypy src
```

## Project Structure

```
sv-lsp/
├── src/
│   └── sv_lsp/
│       ├── __init__.py
│       ├── main.py              # Entry point
│       ├── lsp/
│       │   ├── __init__.py
│       │   ├── protocol.py      # LSP protocol implementation
│       │   ├── server.py        # LSP server main logic
│       │   └── handlers.py      # Request handlers
│       ├── core/
│       │   ├── __init__.py
│       │   ├── symbol_manager.py   # Symbol table management
│       │   ├── position_mapper.py  # Position mapping
│       │   ├── workspace.py        # Workspace management
│       │   └── cache.py            # Caching system
│       ├── analysis/
│       │   ├── __init__.py
│       │   ├── symbol_extractor.py # Symbol extraction
│       │   ├── reference_finder.py # Reference finding
│       │   └── type_resolver.py    # Type resolution
│       └── utils/
│           ├── __init__.py
│           ├── pyslang_wrapper.py  # pyslang integration
│           └── helpers.py          # Utility functions
├── tests/
├── examples/
├── docs/
├── pyproject.toml
└── README.md
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [pyslang](https://github.com/MikePopoloski/slang) - The SystemVerilog compiler library
- [pygls](https://github.com/openlawlibrary/pygls) - Python LSP framework
- [Language Server Protocol](https://microsoft.github.io/language-server-protocol/) - The LSP specification
