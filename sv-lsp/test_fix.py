#!/usr/bin/env python3
"""
Simple test to verify the fixes work.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from sv_lsp.analysis.symbol_extractor import SymbolExtractor
    from sv_lsp.core.types import SymbolKind, Location
    
    print("✅ Imports successful")
    
    # Test symbol extractor
    extractor = SymbolExtractor()
    
    # Test with simple SystemVerilog code
    test_code = """
module test_module(
    input logic clk,
    input logic reset,
    output logic [7:0] data
);
    logic [7:0] counter;
    
    always_ff @(posedge clk) begin
        if (reset)
            counter <= 0;
        else
            counter <= counter + 1;
    end
    
    assign data = counter;
endmodule
"""
    
    print("🔍 Testing symbol extraction...")
    symbols = extractor.extract_from_text(test_code, "test.sv")
    
    print(f"✅ Extracted {len(symbols)} symbols:")
    for symbol in symbols:
        print(f"  - {symbol.name} ({symbol.kind.value}) at line {symbol.definition_location.line}")
    
    print("✅ Test completed successfully!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
