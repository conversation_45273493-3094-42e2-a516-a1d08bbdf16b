"""
Utility helper functions for the SystemVerilog LSP server.

This module provides common utility functions used throughout the LSP server
implementation, including URI/path conversions and position calculations.
"""

import os
import re
from pathlib import Path
from typing import Optional, Tuple
from urllib.parse import unquote, urlparse
from urllib.request import pathname2url

from ..core.types import Location, Range


def uri_to_path(uri: str) -> str:
    """
    Convert a file URI to a local file path.
    
    Args:
        uri: File URI (e.g., "file:///path/to/file.sv")
        
    Returns:
        Local file path
    """
    parsed = urlparse(uri)
    if parsed.scheme != "file":
        raise ValueError(f"Expected file URI, got: {uri}")
    
    path = unquote(parsed.path)
    
    # Handle Windows paths
    if os.name == "nt" and path.startswith("/"):
        path = path[1:]
    
    return path


def path_to_uri(path: str) -> str:
    """
    Convert a local file path to a file URI.
    
    Args:
        path: Local file path
        
    Returns:
        File URI
    """
    # Convert to absolute path
    abs_path = Path(path).resolve()
    
    # Convert to URI
    if os.name == "nt":
        # Windows: handle drive letters
        uri_path = pathname2url(str(abs_path))
        return f"file:///{uri_path}"
    else:
        # Unix-like systems
        uri_path = pathname2url(str(abs_path))
        return f"file://{uri_path}"


def position_to_offset(text: str, line: int, column: int) -> int:
    """
    Convert line/column position to byte offset in text.
    
    Args:
        text: Source text
        line: Line number (0-based)
        column: Column number (0-based)
        
    Returns:
        Byte offset in text
    """
    lines = text.split('\n')
    
    if line >= len(lines):
        return len(text)
    
    offset = sum(len(lines[i]) + 1 for i in range(line))  # +1 for newline
    offset += min(column, len(lines[line]))
    
    return offset


def offset_to_position(text: str, offset: int) -> Tuple[int, int]:
    """
    Convert byte offset to line/column position in text.
    
    Args:
        text: Source text
        offset: Byte offset
        
    Returns:
        Tuple of (line, column) both 0-based
    """
    if offset >= len(text):
        lines = text.split('\n')
        return len(lines) - 1, len(lines[-1])
    
    lines = text[:offset].split('\n')
    line = len(lines) - 1
    column = len(lines[-1])
    
    return line, column


def range_contains_position(range_obj: Range, position: Location) -> bool:
    """
    Check if a range contains a position.
    
    Args:
        range_obj: Range to check
        position: Position to test
        
    Returns:
        True if position is within range
    """
    return range_obj.contains_position(position)


def normalize_path(path: str) -> str:
    """
    Normalize a file path for consistent comparison.
    
    Args:
        path: File path to normalize
        
    Returns:
        Normalized path
    """
    return str(Path(path).resolve())


def is_systemverilog_file(path: str) -> bool:
    """
    Check if a file is a SystemVerilog file based on extension.
    
    Args:
        path: File path to check
        
    Returns:
        True if file appears to be SystemVerilog
    """
    sv_extensions = {'.sv', '.svh', '.v', '.vh', '.svi'}
    return Path(path).suffix.lower() in sv_extensions


def extract_identifier_at_position(text: str, line: int, column: int) -> Optional[str]:
    """
    Extract the identifier at the given position in text.
    
    Args:
        text: Source text
        line: Line number (0-based)
        column: Column number (0-based)
        
    Returns:
        Identifier at position, or None if no identifier found
    """
    lines = text.split('\n')
    
    if line >= len(lines):
        return None
    
    line_text = lines[line]
    if column >= len(line_text):
        return None
    
    # SystemVerilog identifier pattern
    identifier_pattern = r'[a-zA-Z_][a-zA-Z0-9_$]*'
    
    # Find all identifiers in the line
    for match in re.finditer(identifier_pattern, line_text):
        start, end = match.span()
        if start <= column < end:
            return match.group()
    
    return None


def get_word_range_at_position(text: str, line: int, column: int) -> Optional[Range]:
    """
    Get the range of the word at the given position.
    
    Args:
        text: Source text
        line: Line number (0-based)
        column: Column number (0-based)
        
    Returns:
        Range of the word, or None if no word found
    """
    lines = text.split('\n')
    
    if line >= len(lines):
        return None
    
    line_text = lines[line]
    if column >= len(line_text):
        return None
    
    # SystemVerilog identifier pattern
    identifier_pattern = r'[a-zA-Z_][a-zA-Z0-9_$]*'
    
    # Find all identifiers in the line
    for match in re.finditer(identifier_pattern, line_text):
        start, end = match.span()
        if start <= column < end:
            # Create locations for start and end of word
            start_loc = Location(file_path="", line=line, column=start)
            end_loc = Location(file_path="", line=line, column=end)
            return Range(start=start_loc, end=end_loc)
    
    return None


def split_qualified_name(qualified_name: str) -> Tuple[Optional[str], str]:
    """
    Split a qualified name into parent and local name.
    
    Args:
        qualified_name: Qualified name (e.g., "module.signal")
        
    Returns:
        Tuple of (parent_name, local_name)
    """
    if '.' in qualified_name:
        parts = qualified_name.rsplit('.', 1)
        return parts[0], parts[1]
    else:
        return None, qualified_name


def escape_regex(text: str) -> str:
    """
    Escape special regex characters in text.
    
    Args:
        text: Text to escape
        
    Returns:
        Escaped text
    """
    return re.escape(text)
