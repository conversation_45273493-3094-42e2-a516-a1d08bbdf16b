"""
Performance monitoring and optimization utilities.

This module provides tools for measuring and optimizing the performance
of the SystemVerilog LSP server.
"""

import time
import functools
import logging
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Represents a performance metric."""
    name: str
    total_time: float = 0.0
    call_count: int = 0
    min_time: float = float('inf')
    max_time: float = 0.0

    @property
    def average_time(self) -> float:
        """Calculate average execution time."""
        return self.total_time / self.call_count if self.call_count > 0 else 0.0

    def add_measurement(self, execution_time: float) -> None:
        """Add a new measurement."""
        self.total_time += execution_time
        self.call_count += 1
        self.min_time = min(self.min_time, execution_time)
        self.max_time = max(self.max_time, execution_time)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for reporting."""
        return {
            "name": self.name,
            "total_time": self.total_time,
            "call_count": self.call_count,
            "average_time": self.average_time,
            "min_time": self.min_time if self.min_time != float('inf') else 0.0,
            "max_time": self.max_time,
        }


class PerformanceMonitor:
    """
    Monitors and tracks performance metrics.
    """

    def __init__(self):
        """Initialize the performance monitor."""
        self.metrics: Dict[str, PerformanceMetric] = {}
        self.enabled = True

    def enable(self) -> None:
        """Enable performance monitoring."""
        self.enabled = True

    def disable(self) -> None:
        """Disable performance monitoring."""
        self.enabled = False

    def measure(self, name: str, execution_time: float) -> None:
        """
        Record a performance measurement.

        Args:
            name: Name of the operation
            execution_time: Execution time in seconds
        """
        if not self.enabled:
            return

        if name not in self.metrics:
            self.metrics[name] = PerformanceMetric(name)

        self.metrics[name].add_measurement(execution_time)

    def get_metric(self, name: str) -> Optional[PerformanceMetric]:
        """
        Get a specific metric.

        Args:
            name: Name of the metric

        Returns:
            Performance metric or None if not found
        """
        return self.metrics.get(name)

    def get_all_metrics(self) -> Dict[str, PerformanceMetric]:
        """Get all metrics."""
        return self.metrics.copy()

    def get_report(self) -> Dict[str, Any]:
        """
        Generate a performance report.

        Returns:
            Dictionary containing performance statistics
        """
        report = {
            "enabled": self.enabled,
            "total_metrics": len(self.metrics),
            "metrics": {}
        }

        for name, metric in self.metrics.items():
            report["metrics"][name] = metric.to_dict()

        return report

    def clear(self) -> None:
        """Clear all metrics."""
        self.metrics.clear()

    def log_summary(self) -> None:
        """Log a summary of performance metrics."""
        if not self.metrics:
            logger.info("No performance metrics recorded")
            return

        logger.info("Performance Summary:")
        for name, metric in sorted(self.metrics.items()):
            logger.info(
                f"  {name}: {metric.call_count} calls, "
                f"avg: {metric.average_time:.4f}s, "
                f"total: {metric.total_time:.4f}s"
            )


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def timed(name: Optional[str] = None):
    """
    Decorator to measure execution time of functions.

    Args:
        name: Optional name for the metric (defaults to function name)
    """
    def decorator(func: Callable) -> Callable:
        metric_name = name or f"{func.__module__}.{func.__name__}"

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not performance_monitor.enabled:
                return func(*args, **kwargs)

            start_time = time.perf_counter()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.perf_counter()
                execution_time = end_time - start_time
                performance_monitor.measure(metric_name, execution_time)

        return wrapper
    return decorator


class Timer:
    """
    Context manager for timing code blocks.
    """

    def __init__(self, name: str, enabled: bool = True, monitor: Optional[PerformanceMonitor] = None):
        """
        Initialize timer.

        Args:
            name: Name for the timing metric
            enabled: Whether timing is enabled
            monitor: Performance monitor to use (defaults to global)
        """
        self.name = name
        self.monitor = monitor or performance_monitor
        self.enabled = enabled and self.monitor.enabled
        self.start_time = 0.0

    def __enter__(self):
        if self.enabled:
            self.start_time = time.perf_counter()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.enabled:
            end_time = time.perf_counter()
            execution_time = end_time - self.start_time
            self.monitor.measure(self.name, execution_time)


class BatchTimer:
    """
    Timer for batch operations that tracks individual and total times.
    """

    def __init__(self, base_name: str):
        """
        Initialize batch timer.

        Args:
            base_name: Base name for metrics
        """
        self.base_name = base_name
        self.batch_start = 0.0
        self.item_count = 0

    def start_batch(self) -> None:
        """Start timing a batch operation."""
        self.batch_start = time.perf_counter()
        self.item_count = 0

    def time_item(self, item_name: str = "item") -> Timer:
        """
        Get a timer for an individual item in the batch.

        Args:
            item_name: Name for the item (defaults to "item")

        Returns:
            Timer context manager
        """
        self.item_count += 1
        return Timer(f"{self.base_name}.{item_name}")

    def end_batch(self) -> None:
        """End timing the batch operation."""
        if self.batch_start > 0:
            batch_time = time.perf_counter() - self.batch_start
            performance_monitor.measure(f"{self.base_name}.batch_total", batch_time)
            performance_monitor.measure(f"{self.base_name}.batch_count", self.item_count)

            if self.item_count > 0:
                avg_time = batch_time / self.item_count
                performance_monitor.measure(f"{self.base_name}.batch_avg", avg_time)


def benchmark_function(func: Callable, *args, iterations: int = 100, **kwargs) -> Dict[str, float]:
    """
    Benchmark a function by running it multiple times.

    Args:
        func: Function to benchmark
        *args: Arguments to pass to function
        iterations: Number of iterations to run
        **kwargs: Keyword arguments to pass to function

    Returns:
        Dictionary with benchmark results
    """
    times = []

    # Warm up
    for _ in range(min(10, iterations // 10)):
        func(*args, **kwargs)

    # Actual benchmark
    for _ in range(iterations):
        start_time = time.perf_counter()
        func(*args, **kwargs)
        end_time = time.perf_counter()
        times.append(end_time - start_time)

    return {
        "iterations": iterations,
        "total_time": sum(times),
        "average_time": sum(times) / len(times),
        "min_time": min(times),
        "max_time": max(times),
        "median_time": sorted(times)[len(times) // 2],
    }
