"""
Pyslang wrapper for SystemVerilog LSP server.

This module provides a high-level wrapper around pyslang functionality
to simplify integration with the LSP server.
"""

from typing import Dict, List, Optional, Tuple
import logging
from pathlib import Path

try:
    import pyslang
    PYSLANG_AVAILABLE = True
except ImportError:
    PYSLANG_AVAILABLE = False
    pyslang = None

from ..core.types import SymbolInfo, Location, WorkspaceConfig
# SymbolAnalyzer import removed to avoid circular dependency

logger = logging.getLogger(__name__)


class PyslangWrapper:
    """
    High-level wrapper around pyslang functionality.

    This class provides:
    - Simplified parsing interface
    - Symbol extraction
    - Error handling and diagnostics
    - Configuration management
    """

    def __init__(self, config: Optional[WorkspaceConfig] = None):
        """
        Initialize the pyslang wrapper.

        Args:
            config: Workspace configuration
        """
        if not PYSLANG_AVAILABLE:
            logger.error("pyslang is not available - LSP functionality will be limited")
            raise ImportError("pyslang is required but not available")

        self.config = config or WorkspaceConfig()
        # Symbol analyzer will be created when needed to avoid circular imports

        # Cache for parsed files
        self.syntax_trees: Dict[str, object] = {}
        self.compilations: Dict[str, object] = {}

    def parse_file(self, file_path: str, force_reparse: bool = False) -> Tuple[bool, List[str]]:
        """
        Parse a SystemVerilog file.

        Args:
            file_path: Path to the file to parse
            force_reparse: Force reparsing even if cached

        Returns:
            Tuple of (success, error_messages)
        """
        try:
            # Check if we need to reparse
            if not force_reparse and file_path in self.syntax_trees:
                file_mtime = Path(file_path).stat().st_mtime
                # TODO: Check if file has been modified since last parse
                # For now, always reparse

            # Parse the file
            tree = pyslang.SyntaxTree.fromFile(file_path)

            # Check for parse errors
            errors = []
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        errors.append(str(diag))
                    else:
                        logger.debug(f"Parse warning in {file_path}: {diag}")

            # Cache the syntax tree
            self.syntax_trees[file_path] = tree

            if errors:
                logger.warning(f"Parse errors in {file_path}: {errors}")
                return False, errors
            else:
                logger.debug(f"Successfully parsed {file_path}")
                return True, []

        except Exception as e:
            error_msg = f"Failed to parse {file_path}: {e}"
            logger.error(error_msg)
            return False, [error_msg]

    def parse_text(self, text: str, file_path: str = "<text>") -> Tuple[bool, List[str]]:
        """
        Parse SystemVerilog text.

        Args:
            text: SystemVerilog source code
            file_path: Virtual file path for the text

        Returns:
            Tuple of (success, error_messages)
        """
        try:
            # Parse the text
            tree = pyslang.SyntaxTree.fromText(text, file_path)

            # Check for parse errors
            errors = []
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        errors.append(str(diag))
                    else:
                        logger.debug(f"Parse warning in {file_path}: {diag}")

            # Cache the syntax tree
            self.syntax_trees[file_path] = tree

            if errors:
                logger.warning(f"Parse errors in {file_path}: {errors}")
                return False, errors
            else:
                logger.debug(f"Successfully parsed text as {file_path}")
                return True, []

        except Exception as e:
            error_msg = f"Failed to parse text: {e}"
            logger.error(error_msg)
            return False, [error_msg]

    def create_compilation(self, file_paths: List[str]) -> Tuple[bool, List[str]]:
        """
        Create a compilation unit from multiple files.

        Args:
            file_paths: List of file paths to include in compilation

        Returns:
            Tuple of (success, error_messages)
        """
        try:
            compilation = pyslang.Compilation()

            # Add compilation options
            options = compilation.options

            # Set include paths
            for include_path in self.config.include_paths:
                options.includeDirs.append(include_path)

            # Set defines
            for name, value in self.config.defines.items():
                options.defines[name] = value

            # Add syntax trees
            errors = []
            for file_path in file_paths:
                if file_path not in self.syntax_trees:
                    success, parse_errors = self.parse_file(file_path)
                    if not success:
                        errors.extend(parse_errors)
                        continue

                compilation.addSyntaxTree(self.syntax_trees[file_path])

            # Get compilation diagnostics
            diagnostics = compilation.getAllDiagnostics()
            for diag in diagnostics:
                if diag.isError():
                    errors.append(str(diag))
                else:
                    logger.debug(f"Compilation warning: {diag}")

            # Cache the compilation
            compilation_key = ":".join(sorted(file_paths))
            self.compilations[compilation_key] = compilation

            if errors:
                logger.warning(f"Compilation errors: {errors}")
                return False, errors
            else:
                logger.info(f"Successfully compiled {len(file_paths)} files")
                return True, []

        except Exception as e:
            error_msg = f"Failed to create compilation: {e}"
            logger.error(error_msg)
            return False, [error_msg]

    def extract_symbols(self, file_path: str) -> List[SymbolInfo]:
        """
        Extract symbols from a parsed file.

        Args:
            file_path: Path to the file

        Returns:
            List of extracted symbols
        """
        return self.symbol_extractor.extract_from_file(file_path)

    def extract_symbols_from_text(self, text: str, file_path: str = "<text>") -> List[SymbolInfo]:
        """
        Extract symbols from text.

        Args:
            text: SystemVerilog source code
            file_path: Virtual file path

        Returns:
            List of extracted symbols
        """
        return self.symbol_extractor.extract_from_text(text, file_path)

    def get_syntax_tree(self, file_path: str) -> Optional[object]:
        """
        Get the cached syntax tree for a file.

        Args:
            file_path: Path to the file

        Returns:
            Syntax tree or None if not cached
        """
        return self.syntax_trees.get(file_path)

    def get_compilation(self, file_paths: List[str]) -> Optional[object]:
        """
        Get the cached compilation for a set of files.

        Args:
            file_paths: List of file paths

        Returns:
            Compilation or None if not cached
        """
        compilation_key = ":".join(sorted(file_paths))
        return self.compilations.get(compilation_key)

    def clear_cache(self) -> None:
        """Clear all cached parse results."""
        self.syntax_trees.clear()
        self.compilations.clear()
        logger.info("Pyslang wrapper cache cleared")

    def remove_file_from_cache(self, file_path: str) -> None:
        """
        Remove a file from the cache.

        Args:
            file_path: Path to the file to remove
        """
        if file_path in self.syntax_trees:
            del self.syntax_trees[file_path]

        # Remove compilations that include this file
        keys_to_remove = []
        for key in self.compilations.keys():
            if file_path in key.split(":"):
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self.compilations[key]

        logger.debug(f"Removed {file_path} from pyslang cache")

    def get_version_info(self) -> Dict[str, str]:
        """
        Get pyslang version information.

        Returns:
            Dictionary with version info
        """
        if not PYSLANG_AVAILABLE:
            return {"error": "pyslang not available"}

        try:
            # Try to get version info from pyslang
            # This may not be available in all versions
            version_info = {
                "pyslang_available": "true",
                "module_path": str(pyslang.__file__) if hasattr(pyslang, '__file__') else "unknown"
            }

            if hasattr(pyslang, '__version__'):
                version_info["version"] = pyslang.__version__

            return version_info

        except Exception as e:
            return {"error": f"Failed to get version info: {e}"}

    def validate_syntax(self, text: str, file_path: str = "<text>") -> Tuple[bool, List[str]]:
        """
        Validate SystemVerilog syntax without full compilation.

        Args:
            text: SystemVerilog source code
            file_path: Virtual file path

        Returns:
            Tuple of (is_valid, error_messages)
        """
        success, errors = self.parse_text(text, file_path)
        return success, errors
