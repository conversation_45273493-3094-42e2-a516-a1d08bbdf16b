#!/usr/bin/env python3
"""
Main entry point for the SystemVerilog Language Server.

This module provides the command-line interface and starts the LSP server.
"""

import argparse
import asyncio
import logging
import sys
from typing import Optional

from .lsp.server import SVLanguageServer


def setup_logging(level: str = "INFO") -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stderr),  # LSP uses stdout for communication
        ],
    )


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="SystemVerilog Language Server Protocol implementation"
    )

    parser.add_argument(
        "--version",
        action="version",
        version=f"sv-lsp {__import__('sv_lsp').__version__}",
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Set the logging level (default: INFO)",
    )

    parser.add_argument(
        "--tcp",
        type=int,
        metavar="PORT",
        help="Run server on TCP port instead of stdio",
    )

    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="Host to bind TCP server to (default: 127.0.0.1)",
    )

    parser.add_argument(
        "--max-workers",
        type=int,
        default=4,
        help="Maximum number of worker threads (default: 4)",
    )

    return parser.parse_args()


async def main() -> None:
    """Main entry point."""
    args = parse_args()
    setup_logging(args.log_level)

    logger = logging.getLogger(__name__)
    logger.info("Starting SystemVerilog Language Server")

    try:
        # Create and configure the language server
        server = SVLanguageServer(max_workers=args.max_workers)

        if args.tcp:
            # Run server on TCP
            logger.info(f"Starting TCP server on {args.host}:{args.tcp}")
            await server.start_tcp(args.host, args.tcp)
        else:
            # Run server on stdio (default for LSP)
            logger.info("Starting stdio server")
            await server.start_io()

    except KeyboardInterrupt:
        logger.info("Server interrupted by user")
    except Exception as e:
        logger.error(f"Server error: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("Server shutdown complete")


def cli_main() -> None:
    """CLI entry point that handles asyncio setup."""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        pass


if __name__ == "__main__":
    cli_main()
