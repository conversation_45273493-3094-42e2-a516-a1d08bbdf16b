"""
LSP request handlers.

This module provides handlers for different LSP requests and notifications.
"""

from typing import Any, Dict, List, Optional
import logging

from ..core.types import Location, SymbolInfo

logger = logging.getLogger(__name__)


class LSPHandlers:
    """
    Handles LSP requests and notifications.

    This class provides handlers for:
    - Text document operations
    - Navigation requests (go-to-definition, find-references)
    - Symbol queries
    - Workspace operations
    """

    def __init__(self, symbol_manager=None, position_mapper=None, workspace_manager=None):
        """Initialize the LSP handlers."""
        from ..core.symbol_manager import SymbolManager
        from ..core.position_mapper import PositionMapper
        from ..core.workspace import WorkspaceManager
        from ..utils.helpers import uri_to_path, path_to_uri

        self.symbol_manager = symbol_manager or SymbolManager()
        self.position_mapper = position_mapper or PositionMapper()
        self.workspace_manager = workspace_manager or WorkspaceManager()
        self.uri_to_path = uri_to_path
        self.path_to_uri = path_to_uri

    def handle_text_document_did_open(self, params: Dict[str, Any]) -> None:
        """
        Handle textDocument/didOpen notification.

        Args:
            params: Notification parameters
        """
        text_document = params.get("textDocument", {})
        uri = text_document.get("uri", "")
        text = text_document.get("text", "")

        logger.info(f"Document opened: {uri}")

        try:
            file_path = self.uri_to_path(uri)

            # Add file to workspace
            self.workspace_manager.add_file(file_path)

            # Parse document and update symbol table
            success = self.symbol_manager.add_file(file_path)
            if success:
                # Update position mapper
                file_index = self.symbol_manager.file_indices.get(file_path)
                if file_index:
                    self.position_mapper.add_file_index(file_index)

        except Exception as e:
            logger.error(f"Error handling document open for {uri}: {e}")

    def handle_text_document_did_change(self, params: Dict[str, Any]) -> None:
        """
        Handle textDocument/didChange notification.

        Args:
            params: Notification parameters
        """
        text_document = params.get("textDocument", {})
        uri = text_document.get("uri", "")
        changes = params.get("contentChanges", [])

        logger.debug(f"Document changed: {uri}")

        # TODO: Update document content and reparse
        # For full document sync, we get the entire new content
        if changes and "text" in changes[0]:
            new_text = changes[0]["text"]
            # file_path = uri_to_path(uri)
            # self.symbol_manager.add_file(file_path, force_rebuild=True)

    def handle_text_document_did_save(self, params: Dict[str, Any]) -> None:
        """
        Handle textDocument/didSave notification.

        Args:
            params: Notification parameters
        """
        text_document = params.get("textDocument", {})
        uri = text_document.get("uri", "")

        logger.debug(f"Document saved: {uri}")

        # TODO: Trigger reanalysis if needed

    def handle_text_document_did_close(self, params: Dict[str, Any]) -> None:
        """
        Handle textDocument/didClose notification.

        Args:
            params: Notification parameters
        """
        text_document = params.get("textDocument", {})
        uri = text_document.get("uri", "")

        logger.debug(f"Document closed: {uri}")

        # TODO: Clean up resources for this document

    def handle_goto_definition(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Handle textDocument/definition request.

        Args:
            params: Request parameters

        Returns:
            Definition location or None
        """
        text_document = params.get("textDocument", {})
        position = params.get("position", {})

        uri = text_document.get("uri", "")
        line = position.get("line", 0)
        character = position.get("character", 0)

        logger.debug(f"Go to definition: {uri}:{line}:{character}")

        try:
            file_path = self.uri_to_path(uri)

            # Find symbol at position
            symbol = self.position_mapper.find_symbol_at_position(file_path, line, character)
            if symbol:
                # Return the definition location
                def_location = symbol.definition_location
                return {
                    "uri": self.path_to_uri(def_location.file_path),
                    "range": {
                        "start": {"line": def_location.line, "character": def_location.column},
                        "end": {"line": def_location.line, "character": def_location.column}
                    }
                }

        except Exception as e:
            logger.error(f"Error in go to definition for {uri}:{line}:{character}: {e}")

        return None

    def handle_find_references(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Handle textDocument/references request.

        Args:
            params: Request parameters

        Returns:
            List of reference locations
        """
        text_document = params.get("textDocument", {})
        position = params.get("position", {})
        context = params.get("context", {})

        uri = text_document.get("uri", "")
        line = position.get("line", 0)
        character = position.get("character", 0)
        include_declaration = context.get("includeDeclaration", True)

        logger.debug(f"Find references: {uri}:{line}:{character}")

        try:
            file_path = self.uri_to_path(uri)

            # Find symbol at position
            symbol = self.position_mapper.find_symbol_at_position(file_path, line, character)
            if symbol:
                references = []

                # Include definition if requested
                if include_declaration:
                    def_location = symbol.definition_location
                    references.append({
                        "uri": self.path_to_uri(def_location.file_path),
                        "range": {
                            "start": {"line": def_location.line, "character": def_location.column},
                            "end": {"line": def_location.line, "character": def_location.column}
                        }
                    })

                # Add all reference locations
                for ref_loc in symbol.references:
                    references.append({
                        "uri": self.path_to_uri(ref_loc.file_path),
                        "range": {
                            "start": {"line": ref_loc.line, "character": ref_loc.column},
                            "end": {"line": ref_loc.line, "character": ref_loc.column}
                        }
                    })

                return references

        except Exception as e:
            logger.error(f"Error in find references for {uri}:{line}:{character}: {e}")

        return []

    def handle_hover(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Handle textDocument/hover request.

        Args:
            params: Request parameters

        Returns:
            Hover information or None
        """
        text_document = params.get("textDocument", {})
        position = params.get("position", {})

        uri = text_document.get("uri", "")
        line = position.get("line", 0)
        character = position.get("character", 0)

        logger.debug(f"Hover: {uri}:{line}:{character}")

        try:
            file_path = self.uri_to_path(uri)

            # Find symbol at position
            symbol = self.position_mapper.find_symbol_at_position(file_path, line, character)
            if symbol:
                content = []

                # Add type information
                if symbol.type_info:
                    content.append(f"```systemverilog\n{symbol.type_info} {symbol.name}\n```")
                else:
                    content.append(f"```systemverilog\n{symbol.kind.value} {symbol.name}\n```")

                # Add qualified name
                if symbol.qualified_name != symbol.name:
                    content.append(f"**Qualified name:** `{symbol.qualified_name}`")

                # Add documentation
                if symbol.documentation:
                    content.append(symbol.documentation)

                # Add location info
                content.append(f"**Defined in:** {symbol.definition_location.file_path}:{symbol.definition_location.line + 1}")

                if content:
                    return {
                        "contents": {
                            "kind": "markdown",
                            "value": "\n\n".join(content)
                        }
                    }

        except Exception as e:
            logger.error(f"Error in hover for {uri}:{line}:{character}: {e}")

        return None

    def handle_document_symbol(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Handle textDocument/documentSymbol request.

        Args:
            params: Request parameters

        Returns:
            List of document symbols
        """
        text_document = params.get("textDocument", {})
        uri = text_document.get("uri", "")

        logger.debug(f"Document symbols: {uri}")

        try:
            file_path = self.uri_to_path(uri)

            # Get all symbols in the document
            symbols = self.symbol_manager.get_file_symbols(file_path)

            # Convert to LSP document symbols
            document_symbols = []
            for symbol in symbols:
                # Only include top-level symbols (children will be nested)
                if symbol.parent is None:
                    document_symbols.append(symbol.to_lsp_document_symbol())

            return document_symbols

        except Exception as e:
            logger.error(f"Error getting document symbols for {uri}: {e}")

        return []

    def handle_workspace_symbol(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Handle workspace/symbol request.

        Args:
            params: Request parameters

        Returns:
            List of workspace symbols
        """
        query = params.get("query", "")

        logger.debug(f"Workspace symbols: {query}")

        try:
            # Search for symbols matching the query
            if query:
                symbols = self.symbol_manager.search_symbols(query, limit=100)
            else:
                # Return all symbols if no query
                symbols = list(self.symbol_manager.global_symbols.values())[:100]

            # Convert to LSP symbol information
            return [symbol.to_lsp_symbol_information() for symbol in symbols]

        except Exception as e:
            logger.error(f"Error searching workspace symbols for '{query}': {e}")

        return []

    def handle_workspace_did_change_configuration(self, params: Dict[str, Any]) -> None:
        """
        Handle workspace/didChangeConfiguration notification.

        Args:
            params: Notification parameters
        """
        settings = params.get("settings", {})

        logger.info("Workspace configuration changed")

        # TODO: Update configuration
        # sv_lsp_settings = settings.get("sv-lsp", {})
        # self.update_configuration(sv_lsp_settings)

    def handle_workspace_did_change_watched_files(self, params: Dict[str, Any]) -> None:
        """
        Handle workspace/didChangeWatchedFiles notification.

        Args:
            params: Notification parameters
        """
        changes = params.get("changes", [])

        for change in changes:
            uri = change.get("uri", "")
            change_type = change.get("type", 0)  # 1=Created, 2=Changed, 3=Deleted

            logger.debug(f"File change: {uri} (type: {change_type})")

            # TODO: Handle file changes
            # file_path = uri_to_path(uri)
            # if change_type == 3:  # Deleted
            #     self.symbol_manager.remove_file(file_path)
            # else:  # Created or Changed
            #     self.symbol_manager.add_file(file_path, force_rebuild=True)
