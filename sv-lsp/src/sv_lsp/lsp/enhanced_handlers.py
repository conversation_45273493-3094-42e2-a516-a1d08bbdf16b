"""
Enhanced LSP Handlers with Semantic Model Integration.

This module provides enhanced LSP handlers that leverage the semantic model
for more accurate and feature-rich language server capabilities.
"""

from typing import Dict, List, Any, Optional
import logging

from ..core.types import SymbolInfo, Location
from ..core.semantic_model import SemanticModel
from ..analysis.symbol_analyzer import <PERSON>ymbolAnalyzer
from .handlers import LSPHandlers

logger = logging.getLogger(__name__)


class EnhancedLSPHandlers(LSPHandlers):
    """
    Enhanced LSP handlers with semantic model integration.

    This class extends the basic LSP handlers to provide:
    - More accurate symbol resolution using semantic model
    - Enhanced reference finding with relationship tracking
    - Improved hover information with hierarchy context
    - Better workspace symbol search with semantic ranking
    """

    def __init__(self, symbol_manager=None, position_mapper=None, workspace_manager=None):
        """Initialize enhanced LSP handlers."""
        super().__init__(symbol_manager, position_mapper, workspace_manager)
        self.symbol_analyzer = SymbolAnalyzer()
        self.semantic_model: Optional[SemanticModel] = None

    def handle_text_document_did_open(self, params: Dict[str, Any]) -> None:
        """
        Enhanced document open handling with semantic analysis.

        Args:
            params: LSP textDocument/didOpen parameters
        """
        try:
            # Call parent implementation first
            super().handle_text_document_did_open(params)

            # Extract file information
            text_document = params.get("textDocument", {})
            uri = text_document.get("uri", "")
            text = text_document.get("text", "")

            if not uri.startswith("file://"):
                return

            file_path = uri[7:]  # Remove "file://" prefix

            # Perform symbol analysis
            symbols = self.symbol_analyzer.analyze_text(text, file_path)
            self.semantic_model = self.symbol_analyzer.get_semantic_model()

            logger.info(f"Symbol analysis completed for {file_path}: {len(symbols)} symbols")

        except Exception as e:
            logger.error(f"Error in document open handling: {e}")

    def handle_goto_definition(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Enhanced go-to-definition with semantic model.

        Args:
            params: LSP textDocument/definition parameters

        Returns:
            Enhanced definition location with semantic context
        """
        try:
            # Extract position information
            text_document = params.get("textDocument", {})
            position = params.get("position", {})

            uri = text_document.get("uri", "")
            if not uri.startswith("file://"):
                return None

            file_path = uri[7:]
            line = position.get("line", 0)
            character = position.get("character", 0)

            # Use semantic model if available
            if self.semantic_model:
                symbol = self.semantic_model.find_symbol_at_position(file_path, line, character)
                if symbol:
                    definition_location = self.semantic_model.get_symbol_definition(symbol)
                    if definition_location:
                        return {
                            "uri": f"file://{definition_location.file_path}",
                            "range": {
                                "start": {"line": definition_location.line, "character": definition_location.column},
                                "end": {"line": definition_location.line, "character": definition_location.column + len(symbol.name)}
                            }
                        }

            # Fallback to parent implementation
            return super().handle_goto_definition(params)

        except Exception as e:
            logger.error(f"Error in enhanced definition handling: {e}")
            return super().handle_goto_definition(params)

    def handle_find_references(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Enhanced find references with semantic model.

        Args:
            params: LSP textDocument/references parameters

        Returns:
            Enhanced list of reference locations with semantic context
        """
        try:
            # Extract position information
            text_document = params.get("textDocument", {})
            position = params.get("position", {})
            context = params.get("context", {})

            uri = text_document.get("uri", "")
            if not uri.startswith("file://"):
                return []

            file_path = uri[7:]
            line = position.get("line", 0)
            character = position.get("character", 0)
            include_declaration = context.get("includeDeclaration", True)

            # Use semantic model if available
            if self.semantic_model:
                symbol = self.semantic_model.find_symbol_at_position(file_path, line, character)
                if symbol:
                    references = []

                    # Get all symbol locations
                    symbol_locations = self.semantic_model.get_symbol_locations(symbol)

                    for symbol_location in symbol_locations:
                        # Include based on context
                        if (symbol_location.kind == "reference" or
                            (include_declaration and symbol_location.kind in ["definition", "declaration"])):

                            location = symbol_location.location
                            references.append({
                                "uri": f"file://{location.file_path}",
                                "range": {
                                    "start": {"line": location.line, "character": location.column},
                                    "end": {"line": location.line, "character": location.column + len(symbol.name)}
                                }
                            })

                    return references

            # Fallback to parent implementation
            return super().handle_find_references(params)

        except Exception as e:
            logger.error(f"Error in enhanced references handling: {e}")
            return super().handle_find_references(params)

    def handle_hover(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Enhanced hover information with semantic context.

        Args:
            params: LSP textDocument/hover parameters

        Returns:
            Enhanced hover information with hierarchy and relationships
        """
        try:
            # Extract position information
            text_document = params.get("textDocument", {})
            position = params.get("position", {})

            uri = text_document.get("uri", "")
            if not uri.startswith("file://"):
                return None

            file_path = uri[7:]
            line = position.get("line", 0)
            character = position.get("character", 0)

            # Use semantic model if available
            if self.semantic_model:
                symbol = self.semantic_model.find_symbol_at_position(file_path, line, character)
                if symbol:
                    # Build enhanced hover content
                    content = self._build_enhanced_hover_content(symbol)

                    return {
                        "contents": {
                            "kind": "markdown",
                            "value": content
                        },
                        "range": {
                            "start": {"line": line, "character": character},
                            "end": {"line": line, "character": character + len(symbol.name)}
                        }
                    }

            # Fallback to parent implementation
            return super().handle_hover(params)

        except Exception as e:
            logger.error(f"Error in enhanced hover handling: {e}")
            return super().handle_hover(params)

    def handle_workspace_symbol(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Enhanced workspace symbol search with semantic ranking.

        Args:
            params: LSP workspace/symbol parameters

        Returns:
            Enhanced symbol list with semantic ranking
        """
        try:
            query = params.get("query", "")

            # Use semantic model if available
            if self.semantic_model:
                # Get all symbols from semantic model
                all_symbols = []
                for syntax_node, symbol in self.semantic_model.symbol_cache.items():
                    if query.lower() in symbol.name.lower():
                        all_symbols.append(symbol)

                # Rank symbols by semantic relevance
                ranked_symbols = self._rank_symbols_by_relevance(all_symbols, query)

                # Convert to LSP format
                result = []
                for symbol in ranked_symbols[:100]:  # Limit to 100 results
                    symbol_info = symbol.to_lsp_symbol_information()

                    # Add semantic context
                    hierarchy = self.semantic_model.get_symbol_hierarchy(symbol)
                    if hierarchy.get("parent"):
                        symbol_info["containerName"] = hierarchy["parent"]

                    result.append(symbol_info)

                return result

            # Fallback to parent implementation
            return super().handle_workspace_symbol(params)

        except Exception as e:
            logger.error(f"Error in enhanced workspace symbol handling: {e}")
            return super().handle_workspace_symbol(params)

    def _build_enhanced_hover_content(self, symbol: SymbolInfo) -> str:
        """
        Build enhanced hover content with semantic information.

        Args:
            symbol: Symbol to build hover content for

        Returns:
            Markdown formatted hover content
        """
        try:
            content = []

            # Basic symbol information
            content.append(f"**{symbol.kind.value}** `{symbol.name}`")

            if symbol.type_info:
                content.append(f"**Type:** `{symbol.type_info}`")

            # Qualified name
            content.append(f"**Qualified Name:** `{symbol.qualified_name}`")

            # Hierarchy information
            if self.semantic_model:
                hierarchy = self.semantic_model.get_symbol_hierarchy(symbol)

                if hierarchy.get("parent"):
                    content.append(f"**Parent:** `{hierarchy['parent']}`")

                children = hierarchy.get("children", [])
                if children:
                    content.append(f"**Children:** {len(children)} symbols")

                # Reference count
                references = hierarchy.get("references", [])
                if references:
                    content.append(f"**References:** {len(references)} locations")

            # Location information
            location = symbol.definition_location
            content.append(f"**Defined at:** `{location.file_path}:{location.line + 1}:{location.column + 1}`")

            # Documentation
            if symbol.documentation:
                content.append("---")
                content.append(symbol.documentation)

            return "\n\n".join(content)

        except Exception as e:
            logger.debug(f"Error building enhanced hover content: {e}")
            return f"**{symbol.kind.value}** `{symbol.name}`"

    def _rank_symbols_by_relevance(self, symbols: List[SymbolInfo], query: str) -> List[SymbolInfo]:
        """
        Rank symbols by semantic relevance to query.

        Args:
            symbols: List of symbols to rank
            query: Search query

        Returns:
            Ranked list of symbols
        """
        def relevance_score(symbol: SymbolInfo) -> float:
            score = 0.0

            # Exact name match gets highest score
            if symbol.name.lower() == query.lower():
                score += 100.0

            # Name starts with query
            elif symbol.name.lower().startswith(query.lower()):
                score += 50.0

            # Name contains query
            elif query.lower() in symbol.name.lower():
                score += 25.0

            # Boost score based on symbol kind
            kind_boost = {
                "module": 10.0,
                "interface": 8.0,
                "class": 6.0,
                "function": 4.0,
                "variable": 2.0
            }
            score += kind_boost.get(symbol.kind.value, 1.0)

            # Boost score based on reference count (if available)
            if self.semantic_model:
                references = self.semantic_model.get_symbol_references(symbol)
                score += min(len(references) * 0.1, 5.0)  # Max 5 points from references

            return score

        return sorted(symbols, key=relevance_score, reverse=True)

    def get_semantic_statistics(self) -> Dict[str, Any]:
        """Get statistics about the semantic model."""
        if self.semantic_model:
            return self.semantic_model.get_statistics()
        return {"semantic_model": "not_available"}
