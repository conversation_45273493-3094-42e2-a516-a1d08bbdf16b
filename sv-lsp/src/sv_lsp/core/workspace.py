"""
Workspace manager for SystemVerilog LSP server.

This module provides functionality for managing SystemVerilog workspaces,
including file discovery, dependency tracking, and project configuration.
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Set
import logging
import fnmatch

from .types import WorkspaceConfig

logger = logging.getLogger(__name__)


class WorkspaceManager:
    """
    Manages SystemVerilog workspace and project files.
    
    This class provides:
    - File discovery and monitoring
    - Project configuration management
    - Dependency tracking
    - Include path resolution
    """
    
    def __init__(self, root_path: Optional[str] = None, config: Optional[WorkspaceConfig] = None):
        """
        Initialize the workspace manager.
        
        Args:
            root_path: Root directory of the workspace
            config: Workspace configuration
        """
        self.root_path = Path(root_path) if root_path else Path.cwd()
        self.config = config or WorkspaceConfig()
        self.files: Set[str] = set()
        self.file_dependencies: Dict[str, Set[str]] = {}
        
    def discover_files(self, recursive: bool = True) -> List[str]:
        """
        Discover SystemVerilog files in the workspace.
        
        Args:
            recursive: Whether to search recursively
            
        Returns:
            List of discovered file paths
        """
        discovered = []
        
        if recursive:
            for root, dirs, files in os.walk(self.root_path):
                # Skip excluded directories
                dirs[:] = [d for d in dirs if not self._is_excluded(os.path.join(root, d))]
                
                for file in files:
                    file_path = os.path.join(root, file)
                    if self._is_systemverilog_file(file_path) and not self._is_excluded(file_path):
                        discovered.append(file_path)
        else:
            for file_path in self.root_path.iterdir():
                if (file_path.is_file() and 
                    self._is_systemverilog_file(str(file_path)) and 
                    not self._is_excluded(str(file_path))):
                    discovered.append(str(file_path))
        
        self.files.update(discovered)
        logger.info(f"Discovered {len(discovered)} SystemVerilog files")
        return discovered
    
    def add_file(self, file_path: str) -> bool:
        """
        Add a file to the workspace.
        
        Args:
            file_path: Path to the file to add
            
        Returns:
            True if file was added successfully
        """
        if not self._is_systemverilog_file(file_path):
            logger.warning(f"Not a SystemVerilog file: {file_path}")
            return False
        
        if self._is_excluded(file_path):
            logger.warning(f"File is excluded: {file_path}")
            return False
        
        self.files.add(file_path)
        logger.debug(f"Added file to workspace: {file_path}")
        return True
    
    def remove_file(self, file_path: str) -> bool:
        """
        Remove a file from the workspace.
        
        Args:
            file_path: Path to the file to remove
            
        Returns:
            True if file was removed
        """
        if file_path in self.files:
            self.files.remove(file_path)
            # Remove dependencies
            if file_path in self.file_dependencies:
                del self.file_dependencies[file_path]
            logger.debug(f"Removed file from workspace: {file_path}")
            return True
        return False
    
    def get_files(self) -> List[str]:
        """
        Get all files in the workspace.
        
        Returns:
            List of file paths
        """
        return list(self.files)
    
    def resolve_include_path(self, include_file: str, current_file: str) -> Optional[str]:
        """
        Resolve an include file path relative to the current file and include paths.
        
        Args:
            include_file: Include file name/path
            current_file: Current file that contains the include
            
        Returns:
            Resolved absolute path or None if not found
        """
        # Try relative to current file first
        current_dir = Path(current_file).parent
        candidate = current_dir / include_file
        if candidate.exists():
            return str(candidate.resolve())
        
        # Try include paths
        for include_path in self.config.include_paths:
            candidate = Path(include_path) / include_file
            if candidate.exists():
                return str(candidate.resolve())
        
        # Try relative to workspace root
        candidate = self.root_path / include_file
        if candidate.exists():
            return str(candidate.resolve())
        
        logger.warning(f"Could not resolve include: {include_file} from {current_file}")
        return None
    
    def add_file_dependency(self, file_path: str, dependency: str) -> None:
        """
        Add a dependency relationship between files.
        
        Args:
            file_path: File that depends on another
            dependency: File that is depended upon
        """
        if file_path not in self.file_dependencies:
            self.file_dependencies[file_path] = set()
        self.file_dependencies[file_path].add(dependency)
        logger.debug(f"Added dependency: {file_path} -> {dependency}")
    
    def get_file_dependencies(self, file_path: str) -> Set[str]:
        """
        Get dependencies for a file.
        
        Args:
            file_path: File to get dependencies for
            
        Returns:
            Set of dependency file paths
        """
        return self.file_dependencies.get(file_path, set())
    
    def get_dependent_files(self, file_path: str) -> Set[str]:
        """
        Get files that depend on the given file.
        
        Args:
            file_path: File to find dependents for
            
        Returns:
            Set of dependent file paths
        """
        dependents = set()
        for file, deps in self.file_dependencies.items():
            if file_path in deps:
                dependents.add(file)
        return dependents
    
    def get_compilation_order(self) -> List[str]:
        """
        Get files in compilation order based on dependencies.
        
        Returns:
            List of files in compilation order
        """
        # Simple topological sort
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(file_path: str) -> None:
            if file_path in temp_visited:
                logger.warning(f"Circular dependency detected involving: {file_path}")
                return
            if file_path in visited:
                return
            
            temp_visited.add(file_path)
            for dep in self.get_file_dependencies(file_path):
                if dep in self.files:  # Only consider files in workspace
                    visit(dep)
            temp_visited.remove(file_path)
            visited.add(file_path)
            result.append(file_path)
        
        for file_path in self.files:
            if file_path not in visited:
                visit(file_path)
        
        return result
    
    def update_config(self, config: WorkspaceConfig) -> None:
        """
        Update workspace configuration.
        
        Args:
            config: New configuration
        """
        self.config = config
        logger.info("Workspace configuration updated")
    
    def get_statistics(self) -> Dict[str, int]:
        """
        Get workspace statistics.
        
        Returns:
            Dictionary with statistics
        """
        return {
            "total_files": len(self.files),
            "total_dependencies": sum(len(deps) for deps in self.file_dependencies.values()),
            "include_paths": len(self.config.include_paths),
        }
    
    def _is_systemverilog_file(self, file_path: str) -> bool:
        """Check if a file is a SystemVerilog file."""
        return Path(file_path).suffix.lower() in self.config.file_extensions
    
    def _is_excluded(self, file_path: str) -> bool:
        """Check if a file/directory should be excluded."""
        path_str = str(Path(file_path).resolve())
        
        for pattern in self.config.exclude_patterns:
            if fnmatch.fnmatch(path_str, pattern):
                return True
        
        return False
    
    def clear(self) -> None:
        """Clear all workspace data."""
        self.files.clear()
        self.file_dependencies.clear()
        logger.info("Workspace cleared")
