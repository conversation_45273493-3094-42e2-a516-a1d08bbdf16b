"""
Position mapper for SystemVerilog LSP server.

This module provides functionality for mapping between source code positions
and symbols, enabling features like go-to-definition and find-references.
"""

from typing import Dict, List, Optional, Tuple
import logging

from .types import Location, Range, SymbolInfo, FileIndex

logger = logging.getLogger(__name__)


class PositionMapper:
    """
    Maps source code positions to symbols and vice versa.
    
    This class provides:
    - Position-based symbol lookup
    - Symbol-to-position mapping
    - Range-based queries
    - Incremental updates
    """
    
    def __init__(self):
        """Initialize the position mapper."""
        self.file_indices: Dict[str, FileIndex] = {}
    
    def add_file_index(self, file_index: FileIndex) -> None:
        """
        Add or update a file index.
        
        Args:
            file_index: File index to add
        """
        self.file_indices[file_index.file_path] = file_index
        logger.debug(f"Added file index: {file_index.file_path}")
    
    def remove_file(self, file_path: str) -> bool:
        """
        Remove a file from the position mapper.
        
        Args:
            file_path: Path to the file to remove
            
        Returns:
            True if file was removed
        """
        if file_path in self.file_indices:
            del self.file_indices[file_path]
            logger.debug(f"Removed file: {file_path}")
            return True
        return False
    
    def find_symbol_at_position(self, file_path: str, line: int, column: int) -> Optional[SymbolInfo]:
        """
        Find the symbol at the given position.
        
        Args:
            file_path: Path to the file
            line: Line number (0-based)
            column: Column number (0-based)
            
        Returns:
            Symbol at position or None
        """
        if file_path not in self.file_indices:
            return None
        
        position = Location(file_path=file_path, line=line, column=column)
        return self.file_indices[file_path].find_symbol_at_position(position)
    
    def find_symbols_in_range(self, file_path: str, start_line: int, start_column: int,
                             end_line: int, end_column: int) -> List[SymbolInfo]:
        """
        Find all symbols within the given range.
        
        Args:
            file_path: Path to the file
            start_line: Start line (0-based)
            start_column: Start column (0-based)
            end_line: End line (0-based)
            end_column: End column (0-based)
            
        Returns:
            List of symbols in range
        """
        if file_path not in self.file_indices:
            return []
        
        start_pos = Location(file_path=file_path, line=start_line, column=start_column)
        end_pos = Location(file_path=file_path, line=end_line, column=end_column)
        query_range = Range(start=start_pos, end=end_pos)
        
        result = []
        file_index = self.file_indices[file_path]
        
        for range_info, symbol in file_index.symbol_map.items():
            # Check if symbol range overlaps with query range
            if self._ranges_overlap(range_info, query_range):
                result.append(symbol)
        
        return result
    
    def get_symbol_definition_location(self, symbol: SymbolInfo) -> Location:
        """
        Get the definition location of a symbol.
        
        Args:
            symbol: Symbol to get location for
            
        Returns:
            Definition location
        """
        return symbol.definition_location
    
    def get_symbol_references(self, symbol: SymbolInfo) -> List[Location]:
        """
        Get all reference locations for a symbol.
        
        Args:
            symbol: Symbol to get references for
            
        Returns:
            List of reference locations
        """
        return symbol.references.copy()
    
    def add_symbol_reference(self, symbol: SymbolInfo, location: Location) -> None:
        """
        Add a reference location to a symbol.
        
        Args:
            symbol: Symbol to add reference to
            location: Reference location
        """
        symbol.add_reference(location)
        logger.debug(f"Added reference for {symbol.name} at {location}")
    
    def get_file_symbols(self, file_path: str) -> List[SymbolInfo]:
        """
        Get all symbols in a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            List of symbols in file
        """
        if file_path not in self.file_indices:
            return []
        return self.file_indices[file_path].symbols
    
    def get_symbol_range(self, symbol: SymbolInfo) -> Optional[Range]:
        """
        Get the source range for a symbol.
        
        Args:
            symbol: Symbol to get range for
            
        Returns:
            Symbol range or None if not found
        """
        # Find the range in the appropriate file index
        file_path = symbol.definition_location.file_path
        if file_path not in self.file_indices:
            return None
        
        file_index = self.file_indices[file_path]
        for range_info, mapped_symbol in file_index.symbol_map.items():
            if mapped_symbol == symbol:
                return range_info
        
        return None
    
    def update_symbol_position(self, symbol: SymbolInfo, new_location: Location,
                              new_range: Optional[Range] = None) -> None:
        """
        Update the position information for a symbol.
        
        Args:
            symbol: Symbol to update
            new_location: New definition location
            new_range: New symbol range (optional)
        """
        old_file = symbol.definition_location.file_path
        new_file = new_location.file_path
        
        # Remove from old file index if file changed
        if old_file != new_file and old_file in self.file_indices:
            old_index = self.file_indices[old_file]
            if symbol in old_index.symbols:
                old_index.symbols.remove(symbol)
            
            # Remove from symbol map
            ranges_to_remove = []
            for range_info, mapped_symbol in old_index.symbol_map.items():
                if mapped_symbol == symbol:
                    ranges_to_remove.append(range_info)
            for range_info in ranges_to_remove:
                del old_index.symbol_map[range_info]
        
        # Update symbol location
        symbol.definition_location = new_location
        
        # Add to new file index
        if new_file in self.file_indices:
            new_index = self.file_indices[new_file]
            if symbol not in new_index.symbols:
                new_index.symbols.append(symbol)
            
            if new_range:
                new_index.symbol_map[new_range] = symbol
        
        logger.debug(f"Updated position for {symbol.name}: {new_location}")
    
    def get_statistics(self) -> Dict[str, int]:
        """
        Get statistics about the position mapper.
        
        Returns:
            Dictionary with statistics
        """
        total_symbols = 0
        total_ranges = 0
        
        for file_index in self.file_indices.values():
            total_symbols += len(file_index.symbols)
            total_ranges += len(file_index.symbol_map)
        
        return {
            "total_files": len(self.file_indices),
            "total_symbols": total_symbols,
            "total_ranges": total_ranges,
        }
    
    def _ranges_overlap(self, range1: Range, range2: Range) -> bool:
        """
        Check if two ranges overlap.
        
        Args:
            range1: First range
            range2: Second range
            
        Returns:
            True if ranges overlap
        """
        # Ranges must be in the same file
        if range1.start.file_path != range2.start.file_path:
            return False
        
        # Check for overlap
        # Range1 ends before range2 starts
        if (range1.end.line < range2.start.line or 
            (range1.end.line == range2.start.line and range1.end.column < range2.start.column)):
            return False
        
        # Range2 ends before range1 starts
        if (range2.end.line < range1.start.line or 
            (range2.end.line == range1.start.line and range2.end.column < range1.start.column)):
            return False
        
        return True
    
    def clear(self) -> None:
        """Clear all position mapping data."""
        self.file_indices.clear()
        logger.info("Position mapper cleared")
