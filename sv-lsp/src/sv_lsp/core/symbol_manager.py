"""
Symbol manager for SystemVerilog LSP server.

This module provides functionality for building, maintaining, and querying
symbol tables from SystemVerilog source code using pyslang.
"""

from typing import Dict, List, Optional, Set
from pathlib import Path
import logging

from .types import SymbolInfo, SymbolKind, Location, FileIndex, WorkspaceConfig

logger = logging.getLogger(__name__)


class SymbolManager:
    """
    Manages symbol tables for SystemVerilog code.
    
    This class is responsible for:
    - Building symbol tables from parsed SystemVerilog code
    - Maintaining symbol relationships and hierarchies
    - Providing efficient symbol lookup and query capabilities
    - Handling incremental updates when files change
    """
    
    def __init__(self, config: Optional[WorkspaceConfig] = None):
        """
        Initialize the symbol manager.
        
        Args:
            config: Workspace configuration
        """
        self.config = config or WorkspaceConfig()
        self.file_indices: Dict[str, FileIndex] = {}
        self.global_symbols: Dict[str, SymbolInfo] = {}
        self.name_index: Dict[str, List[SymbolInfo]] = {}
        
    def add_file(self, file_path: str, force_rebuild: bool = False) -> bool:
        """
        Add or update a file in the symbol table.
        
        Args:
            file_path: Path to the SystemVerilog file
            force_rebuild: Force rebuilding even if file hasn't changed
            
        Returns:
            True if file was successfully processed
        """
        try:
            path = Path(file_path)
            if not path.exists():
                logger.warning(f"File not found: {file_path}")
                return False
            
            # Check if file needs to be processed
            if not force_rebuild and file_path in self.file_indices:
                current_mtime = path.stat().st_mtime
                if (self.file_indices[file_path].last_modified and 
                    current_mtime <= self.file_indices[file_path].last_modified):
                    logger.debug(f"File unchanged, skipping: {file_path}")
                    return True
            
            # Create or update file index
            file_index = FileIndex(
                file_path=file_path,
                last_modified=path.stat().st_mtime
            )
            
            # TODO: Parse file with pyslang and extract symbols
            # This is a placeholder implementation
            logger.info(f"Processing file: {file_path}")
            
            self.file_indices[file_path] = file_index
            return True
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            return False
    
    def remove_file(self, file_path: str) -> bool:
        """
        Remove a file from the symbol table.
        
        Args:
            file_path: Path to the file to remove
            
        Returns:
            True if file was successfully removed
        """
        if file_path not in self.file_indices:
            return False
        
        # Remove symbols from this file
        file_index = self.file_indices[file_path]
        for symbol in file_index.symbols:
            self._remove_symbol_from_indices(symbol)
        
        # Remove file index
        del self.file_indices[file_path]
        logger.info(f"Removed file: {file_path}")
        return True
    
    def find_symbol(self, name: str, qualified: bool = False) -> Optional[SymbolInfo]:
        """
        Find a symbol by name.
        
        Args:
            name: Symbol name to search for
            qualified: Whether to search by qualified name
            
        Returns:
            Found symbol or None
        """
        if qualified:
            return self.global_symbols.get(name)
        else:
            symbols = self.name_index.get(name, [])
            return symbols[0] if symbols else None
    
    def find_symbols_by_name(self, name: str) -> List[SymbolInfo]:
        """
        Find all symbols with the given name.
        
        Args:
            name: Symbol name to search for
            
        Returns:
            List of matching symbols
        """
        return self.name_index.get(name, [])
    
    def find_symbols_by_kind(self, kind: SymbolKind) -> List[SymbolInfo]:
        """
        Find all symbols of a specific kind.
        
        Args:
            kind: Symbol kind to search for
            
        Returns:
            List of matching symbols
        """
        result = []
        for symbol in self.global_symbols.values():
            if symbol.kind == kind:
                result.append(symbol)
        return result
    
    def get_file_symbols(self, file_path: str) -> List[SymbolInfo]:
        """
        Get all symbols defined in a specific file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            List of symbols in the file
        """
        if file_path not in self.file_indices:
            return []
        return self.file_indices[file_path].symbols
    
    def get_symbol_hierarchy(self, symbol: SymbolInfo) -> List[SymbolInfo]:
        """
        Get the hierarchy path for a symbol (from root to symbol).
        
        Args:
            symbol: Symbol to get hierarchy for
            
        Returns:
            List of symbols from root to the given symbol
        """
        hierarchy = []
        current = symbol
        while current:
            hierarchy.insert(0, current)
            current = current.parent
        return hierarchy
    
    def get_child_symbols(self, symbol: SymbolInfo, recursive: bool = False) -> List[SymbolInfo]:
        """
        Get child symbols of a given symbol.
        
        Args:
            symbol: Parent symbol
            recursive: Whether to include grandchildren recursively
            
        Returns:
            List of child symbols
        """
        if not recursive:
            return symbol.children.copy()
        
        result = []
        for child in symbol.children:
            result.append(child)
            result.extend(self.get_child_symbols(child, recursive=True))
        return result
    
    def get_statistics(self) -> Dict[str, int]:
        """
        Get statistics about the symbol table.
        
        Returns:
            Dictionary with statistics
        """
        stats = {
            "total_files": len(self.file_indices),
            "total_symbols": len(self.global_symbols),
            "unique_names": len(self.name_index),
        }
        
        # Count symbols by kind
        for kind in SymbolKind:
            count = len(self.find_symbols_by_kind(kind))
            if count > 0:
                stats[f"{kind.value}_count"] = count
        
        return stats
    
    def _add_symbol_to_indices(self, symbol: SymbolInfo) -> None:
        """Add a symbol to the global indices."""
        # Add to global symbols
        self.global_symbols[symbol.qualified_name] = symbol
        
        # Add to name index
        if symbol.name not in self.name_index:
            self.name_index[symbol.name] = []
        if symbol not in self.name_index[symbol.name]:
            self.name_index[symbol.name].append(symbol)
    
    def _remove_symbol_from_indices(self, symbol: SymbolInfo) -> None:
        """Remove a symbol from the global indices."""
        # Remove from global symbols
        if symbol.qualified_name in self.global_symbols:
            del self.global_symbols[symbol.qualified_name]
        
        # Remove from name index
        if symbol.name in self.name_index:
            if symbol in self.name_index[symbol.name]:
                self.name_index[symbol.name].remove(symbol)
            if not self.name_index[symbol.name]:
                del self.name_index[symbol.name]
    
    def clear(self) -> None:
        """Clear all symbol data."""
        self.file_indices.clear()
        self.global_symbols.clear()
        self.name_index.clear()
        logger.info("Symbol manager cleared")
