"""
Symbol manager for SystemVerilog LSP server.

This module provides functionality for building, maintaining, and querying
symbol tables from SystemVerilog source code using pyslang.
"""

from typing import Dict, List, Optional, Set
from pathlib import Path
import logging

from .types import SymbolInfo, SymbolKind, Location, FileIndex, WorkspaceConfig
from ..analysis.symbol_extractor import SymbolExtractor
from ..utils.pyslang_wrapper import PyslangWrapper
from ..utils.performance import timed, Timer

logger = logging.getLogger(__name__)


class SymbolManager:
    """
    Manages symbol tables for SystemVerilog code.

    This class is responsible for:
    - Building symbol tables from parsed SystemVerilog code
    - Maintaining symbol relationships and hierarchies
    - Providing efficient symbol lookup and query capabilities
    - Handling incremental updates when files change
    """

    def __init__(self, config: Optional[WorkspaceConfig] = None):
        """
        Initialize the symbol manager.

        Args:
            config: Workspace configuration
        """
        self.config = config or WorkspaceConfig()
        self.file_indices: Dict[str, FileIndex] = {}
        self.global_symbols: Dict[str, SymbolInfo] = {}
        self.name_index: Dict[str, List[SymbolInfo]] = {}

        # Advanced indexing structures
        self.kind_index: Dict[SymbolKind, List[SymbolInfo]] = {}
        self.scope_index: Dict[str, List[SymbolInfo]] = {}  # scope_name -> symbols in scope
        self.prefix_index: Dict[str, List[SymbolInfo]] = {}  # prefix -> symbols starting with prefix

        # Relationship tracking
        self.inheritance_map: Dict[str, Set[str]] = {}  # child -> parents
        self.instantiation_map: Dict[str, Set[str]] = {}  # instance -> module/interface
        self.reference_map: Dict[str, Set[str]] = {}  # symbol -> symbols it references

        # Initialize pyslang components
        try:
            self.pyslang_wrapper = PyslangWrapper(config)
            self.symbol_extractor = SymbolExtractor()
        except ImportError:
            logger.warning("pyslang not available - symbol extraction will be limited")
            self.pyslang_wrapper = None
            self.symbol_extractor = None

    @timed("symbol_manager.add_file")
    def add_file(self, file_path: str, force_rebuild: bool = False) -> bool:
        """
        Add or update a file in the symbol table.

        Args:
            file_path: Path to the SystemVerilog file
            force_rebuild: Force rebuilding even if file hasn't changed

        Returns:
            True if file was successfully processed
        """
        try:
            path = Path(file_path)
            if not path.exists():
                logger.warning(f"File not found: {file_path}")
                return False

            # Check if file needs to be processed
            if not force_rebuild and file_path in self.file_indices:
                current_mtime = path.stat().st_mtime
                if (self.file_indices[file_path].last_modified and
                    current_mtime <= self.file_indices[file_path].last_modified):
                    logger.debug(f"File unchanged, skipping: {file_path}")
                    return True

            # Remove old symbols if file was already indexed
            if file_path in self.file_indices:
                old_symbols = self.file_indices[file_path].symbols
                for symbol in old_symbols:
                    self._remove_symbol_from_indices(symbol)

            # Create file index
            file_index = FileIndex(
                file_path=file_path,
                last_modified=path.stat().st_mtime
            )

            # Extract symbols using pyslang
            if self.symbol_extractor:
                try:
                    with Timer("symbol_manager.extract_symbols"):
                        symbols = self.symbol_extractor.extract_from_file(file_path)

                    with Timer("symbol_manager.index_symbols"):
                        for symbol in symbols:
                            file_index.add_symbol(symbol)
                            self._add_symbol_to_indices(symbol)

                    logger.info(f"Extracted {len(symbols)} symbols from {file_path}")
                except Exception as e:
                    logger.error(f"Error extracting symbols from {file_path}: {e}")
            else:
                logger.warning(f"Symbol extractor not available for {file_path}")

            self.file_indices[file_path] = file_index
            return True

        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            return False

    def remove_file(self, file_path: str) -> bool:
        """
        Remove a file from the symbol table.

        Args:
            file_path: Path to the file to remove

        Returns:
            True if file was successfully removed
        """
        if file_path not in self.file_indices:
            return False

        # Remove symbols from this file
        file_index = self.file_indices[file_path]
        for symbol in file_index.symbols:
            self._remove_symbol_from_indices(symbol)

        # Remove file index
        del self.file_indices[file_path]
        logger.info(f"Removed file: {file_path}")
        return True

    @timed("symbol_manager.find_symbol")
    def find_symbol(self, name: str, qualified: bool = False) -> Optional[SymbolInfo]:
        """
        Find a symbol by name.

        Args:
            name: Symbol name to search for
            qualified: Whether to search by qualified name

        Returns:
            Found symbol or None
        """
        if qualified:
            return self.global_symbols.get(name)
        else:
            symbols = self.name_index.get(name, [])
            return symbols[0] if symbols else None

    def find_symbols_by_name(self, name: str) -> List[SymbolInfo]:
        """
        Find all symbols with the given name.

        Args:
            name: Symbol name to search for

        Returns:
            List of matching symbols
        """
        return self.name_index.get(name, [])

    def find_symbols_by_kind(self, kind: SymbolKind) -> List[SymbolInfo]:
        """
        Find all symbols of a specific kind.

        Args:
            kind: Symbol kind to search for

        Returns:
            List of matching symbols
        """
        return self.kind_index.get(kind, []).copy()

    def find_symbols_by_prefix(self, prefix: str, limit: int = 50) -> List[SymbolInfo]:
        """
        Find symbols that start with the given prefix.

        Args:
            prefix: Prefix to search for
            limit: Maximum number of results

        Returns:
            List of matching symbols
        """
        prefix_lower = prefix.lower()
        symbols = self.prefix_index.get(prefix_lower, [])
        return symbols[:limit]

    def find_symbols_in_scope(self, scope_name: str) -> List[SymbolInfo]:
        """
        Find all symbols in a specific scope.

        Args:
            scope_name: Qualified name of the scope

        Returns:
            List of symbols in the scope
        """
        return self.scope_index.get(scope_name, []).copy()

    @timed("symbol_manager.search_symbols")
    def search_symbols(self, query: str, limit: int = 50) -> List[SymbolInfo]:
        """
        Search for symbols using a query string.

        Args:
            query: Search query
            limit: Maximum number of results

        Returns:
            List of matching symbols
        """
        query_lower = query.lower()
        results = []

        # Exact name matches first
        if query in self.name_index:
            results.extend(self.name_index[query])

        # Prefix matches
        if query_lower in self.prefix_index:
            for symbol in self.prefix_index[query_lower]:
                if symbol not in results:
                    results.append(symbol)

        # Substring matches in qualified names
        for symbol in self.global_symbols.values():
            if (query_lower in symbol.qualified_name.lower() and
                symbol not in results):
                results.append(symbol)

        return results[:limit]

    def add_inheritance_relationship(self, child_symbol: str, parent_symbol: str) -> None:
        """
        Add an inheritance relationship.

        Args:
            child_symbol: Qualified name of child symbol
            parent_symbol: Qualified name of parent symbol
        """
        if child_symbol not in self.inheritance_map:
            self.inheritance_map[child_symbol] = set()
        self.inheritance_map[child_symbol].add(parent_symbol)

    def add_instantiation_relationship(self, instance_symbol: str, module_symbol: str) -> None:
        """
        Add an instantiation relationship.

        Args:
            instance_symbol: Qualified name of instance symbol
            module_symbol: Qualified name of module/interface symbol
        """
        if instance_symbol not in self.instantiation_map:
            self.instantiation_map[instance_symbol] = set()
        self.instantiation_map[instance_symbol].add(module_symbol)

    def add_reference_relationship(self, from_symbol: str, to_symbol: str) -> None:
        """
        Add a reference relationship.

        Args:
            from_symbol: Qualified name of symbol making the reference
            to_symbol: Qualified name of symbol being referenced
        """
        if from_symbol not in self.reference_map:
            self.reference_map[from_symbol] = set()
        self.reference_map[from_symbol].add(to_symbol)

    def get_parent_symbols(self, symbol_name: str) -> List[SymbolInfo]:
        """
        Get parent symbols (inheritance).

        Args:
            symbol_name: Qualified name of symbol

        Returns:
            List of parent symbols
        """
        parents = self.inheritance_map.get(symbol_name, set())
        return [self.global_symbols[p] for p in parents if p in self.global_symbols]

    def get_child_symbols(self, symbol_name: str) -> List[SymbolInfo]:
        """
        Get child symbols (inheritance).

        Args:
            symbol_name: Qualified name of symbol

        Returns:
            List of child symbols
        """
        children = []
        for child, parents in self.inheritance_map.items():
            if symbol_name in parents and child in self.global_symbols:
                children.append(self.global_symbols[child])
        return children

    def get_instantiated_modules(self, instance_name: str) -> List[SymbolInfo]:
        """
        Get modules/interfaces instantiated by a symbol.

        Args:
            instance_name: Qualified name of instance symbol

        Returns:
            List of instantiated module/interface symbols
        """
        modules = self.instantiation_map.get(instance_name, set())
        return [self.global_symbols[m] for m in modules if m in self.global_symbols]

    def get_instances_of_module(self, module_name: str) -> List[SymbolInfo]:
        """
        Get instances of a module/interface.

        Args:
            module_name: Qualified name of module/interface symbol

        Returns:
            List of instance symbols
        """
        instances = []
        for instance, modules in self.instantiation_map.items():
            if module_name in modules and instance in self.global_symbols:
                instances.append(self.global_symbols[instance])
        return instances

    def get_referenced_symbols(self, symbol_name: str) -> List[SymbolInfo]:
        """
        Get symbols referenced by a symbol.

        Args:
            symbol_name: Qualified name of symbol

        Returns:
            List of referenced symbols
        """
        references = self.reference_map.get(symbol_name, set())
        return [self.global_symbols[r] for r in references if r in self.global_symbols]

    def get_referencing_symbols(self, symbol_name: str) -> List[SymbolInfo]:
        """
        Get symbols that reference a symbol.

        Args:
            symbol_name: Qualified name of symbol

        Returns:
            List of symbols that reference this symbol
        """
        referencing = []
        for from_symbol, references in self.reference_map.items():
            if symbol_name in references and from_symbol in self.global_symbols:
                referencing.append(self.global_symbols[from_symbol])
        return referencing

    def get_file_symbols(self, file_path: str) -> List[SymbolInfo]:
        """
        Get all symbols defined in a specific file.

        Args:
            file_path: Path to the file

        Returns:
            List of symbols in the file
        """
        if file_path not in self.file_indices:
            return []
        return self.file_indices[file_path].symbols

    def get_symbol_hierarchy(self, symbol: SymbolInfo) -> List[SymbolInfo]:
        """
        Get the hierarchy path for a symbol (from root to symbol).

        Args:
            symbol: Symbol to get hierarchy for

        Returns:
            List of symbols from root to the given symbol
        """
        hierarchy = []
        current = symbol
        while current:
            hierarchy.insert(0, current)
            current = current.parent
        return hierarchy

    def get_child_symbols(self, symbol: SymbolInfo, recursive: bool = False) -> List[SymbolInfo]:
        """
        Get child symbols of a given symbol.

        Args:
            symbol: Parent symbol
            recursive: Whether to include grandchildren recursively

        Returns:
            List of child symbols
        """
        if not recursive:
            return symbol.children.copy()

        result = []
        for child in symbol.children:
            result.append(child)
            result.extend(self.get_child_symbols(child, recursive=True))
        return result

    def get_statistics(self) -> Dict[str, int]:
        """
        Get statistics about the symbol table.

        Returns:
            Dictionary with statistics
        """
        stats = {
            "total_files": len(self.file_indices),
            "total_symbols": len(self.global_symbols),
            "unique_names": len(self.name_index),
        }

        # Count symbols by kind
        for kind in SymbolKind:
            count = len(self.find_symbols_by_kind(kind))
            if count > 0:
                stats[f"{kind.value}_count"] = count

        return stats

    def _add_symbol_to_indices(self, symbol: SymbolInfo) -> None:
        """Add a symbol to the global indices."""
        # Add to global symbols
        self.global_symbols[symbol.qualified_name] = symbol

        # Add to name index
        if symbol.name not in self.name_index:
            self.name_index[symbol.name] = []
        if symbol not in self.name_index[symbol.name]:
            self.name_index[symbol.name].append(symbol)

        # Add to kind index
        if symbol.kind not in self.kind_index:
            self.kind_index[symbol.kind] = []
        if symbol not in self.kind_index[symbol.kind]:
            self.kind_index[symbol.kind].append(symbol)

        # Add to scope index
        scope_name = symbol.parent.qualified_name if symbol.parent else "<global>"
        if scope_name not in self.scope_index:
            self.scope_index[scope_name] = []
        if symbol not in self.scope_index[scope_name]:
            self.scope_index[scope_name].append(symbol)

        # Add to prefix index (for autocomplete)
        for i in range(1, len(symbol.name) + 1):
            prefix = symbol.name[:i].lower()
            if prefix not in self.prefix_index:
                self.prefix_index[prefix] = []
            if symbol not in self.prefix_index[prefix]:
                self.prefix_index[prefix].append(symbol)

    def _remove_symbol_from_indices(self, symbol: SymbolInfo) -> None:
        """Remove a symbol from the global indices."""
        # Remove from global symbols
        if symbol.qualified_name in self.global_symbols:
            del self.global_symbols[symbol.qualified_name]

        # Remove from name index
        if symbol.name in self.name_index:
            if symbol in self.name_index[symbol.name]:
                self.name_index[symbol.name].remove(symbol)
            if not self.name_index[symbol.name]:
                del self.name_index[symbol.name]

        # Remove from kind index
        if symbol.kind in self.kind_index:
            if symbol in self.kind_index[symbol.kind]:
                self.kind_index[symbol.kind].remove(symbol)
            if not self.kind_index[symbol.kind]:
                del self.kind_index[symbol.kind]

        # Remove from scope index
        scope_name = symbol.parent.qualified_name if symbol.parent else "<global>"
        if scope_name in self.scope_index:
            if symbol in self.scope_index[scope_name]:
                self.scope_index[scope_name].remove(symbol)
            if not self.scope_index[scope_name]:
                del self.scope_index[scope_name]

        # Remove from prefix index
        for i in range(1, len(symbol.name) + 1):
            prefix = symbol.name[:i].lower()
            if prefix in self.prefix_index:
                if symbol in self.prefix_index[prefix]:
                    self.prefix_index[prefix].remove(symbol)
                if not self.prefix_index[prefix]:
                    del self.prefix_index[prefix]

    def clear(self) -> None:
        """Clear all symbol data."""
        self.file_indices.clear()
        self.global_symbols.clear()
        self.name_index.clear()
        self.kind_index.clear()
        self.scope_index.clear()
        self.prefix_index.clear()
        self.inheritance_map.clear()
        self.instantiation_map.clear()
        self.reference_map.clear()
        logger.info("Symbol manager cleared")
