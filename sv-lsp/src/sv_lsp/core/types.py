"""
Core type definitions for the SystemVerilog LSP server.

This module defines the fundamental data structures used throughout
the LSP server implementation.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Set, Union
from pathlib import Path


class SymbolKind(Enum):
    """
    Symbol kinds for SystemVerilog symbols.

    This enum matches the SymbolKind definition in slang/ast/Symbol.h
    to ensure compatibility and completeness.
    """
    # Core symbols
    UNKNOWN = "unknown"
    ROOT = "root"
    DEFINITION = "definition"
    COMPILATION_UNIT = "compilation_unit"
    DEFERRED_MEMBER = "deferred_member"
    TRANSPARENT_MEMBER = "transparent_member"
    EMPTY_MEMBER = "empty_member"

    # Type system
    PREDEFINED_INTEGER_TYPE = "predefined_integer_type"
    SCALAR_TYPE = "scalar_type"
    FLOATING_TYPE = "floating_type"
    ENUM_TYPE = "enum_type"
    ENUM_VALUE = "enum_value"
    PACKED_ARRAY_TYPE = "packed_array_type"
    FIXED_SIZE_UNPACKED_ARRAY_TYPE = "fixed_size_unpacked_array_type"
    DYNAMIC_ARRAY_TYPE = "dynamic_array_type"
    DPI_OPEN_ARRAY_TYPE = "dpi_open_array_type"
    ASSOCIATIVE_ARRAY_TYPE = "associative_array_type"
    QUEUE_TYPE = "queue_type"
    PACKED_STRUCT_TYPE = "packed_struct_type"
    UNPACKED_STRUCT_TYPE = "unpacked_struct_type"
    PACKED_UNION_TYPE = "packed_union_type"
    UNPACKED_UNION_TYPE = "unpacked_union_type"
    CLASS_TYPE = "class_type"
    COVERGROUP_TYPE = "covergroup_type"
    VOID_TYPE = "void_type"
    NULL_TYPE = "null_type"
    CHANDLE_TYPE = "chandle_type"
    STRING_TYPE = "string_type"
    EVENT_TYPE = "event_type"
    UNBOUNDED_TYPE = "unbounded_type"
    TYPE_REF_TYPE = "type_ref_type"
    UNTYPED_TYPE = "untyped_type"
    SEQUENCE_TYPE = "sequence_type"
    PROPERTY_TYPE = "property_type"
    VIRTUAL_INTERFACE_TYPE = "virtual_interface_type"
    TYPE_ALIAS = "type_alias"
    ERROR_TYPE = "error_type"
    FORWARDING_TYPEDEF = "forwarding_typedef"
    NET_TYPE = "net_type"

    # Parameters and ports
    PARAMETER = "parameter"
    TYPE_PARAMETER = "type_parameter"
    PORT = "port"
    MULTI_PORT = "multi_port"
    INTERFACE_PORT = "interface_port"
    MODPORT = "modport"
    MODPORT_PORT = "modport_port"
    MODPORT_CLOCKING = "modport_clocking"

    # Instances and modules
    INSTANCE = "instance"
    INSTANCE_BODY = "instance_body"
    INSTANCE_ARRAY = "instance_array"
    PACKAGE = "package"
    EXPLICIT_IMPORT = "explicit_import"
    WILDCARD_IMPORT = "wildcard_import"

    # Attributes and generation
    ATTRIBUTE = "attribute"
    GENVAR = "genvar"
    GENERATE_BLOCK = "generate_block"
    GENERATE_BLOCK_ARRAY = "generate_block_array"

    # Procedural blocks
    PROCEDURAL_BLOCK = "procedural_block"
    STATEMENT_BLOCK = "statement_block"

    # Variables and nets
    NET = "net"
    VARIABLE = "variable"
    FORMAL_ARGUMENT = "formal_argument"
    FIELD = "field"
    CLASS_PROPERTY = "class_property"

    # Subroutines
    SUBROUTINE = "subroutine"

    # Assignments and tasks
    CONTINUOUS_ASSIGN = "continuous_assign"
    ELAB_SYSTEM_TASK = "elab_system_task"

    # Generic and method definitions
    GENERIC_CLASS_DEF = "generic_class_def"
    METHOD_PROTOTYPE = "method_prototype"
    UNINSTANTIATED_DEF = "uninstantiated_def"

    # Iteration and patterns
    ITERATOR = "iterator"
    PATTERN_VAR = "pattern_var"

    # Constraints
    CONSTRAINT_BLOCK = "constraint_block"

    # Parameters and specifications
    DEF_PARAM = "def_param"
    SPECPARAM = "specparam"

    # Primitives
    PRIMITIVE = "primitive"
    PRIMITIVE_PORT = "primitive_port"
    PRIMITIVE_INSTANCE = "primitive_instance"

    # Specify blocks
    SPECIFY_BLOCK = "specify_block"

    # Assertions and properties
    SEQUENCE = "sequence"
    PROPERTY = "property"
    ASSERTION_PORT = "assertion_port"

    # Clocking
    CLOCKING_BLOCK = "clocking_block"
    CLOCK_VAR = "clock_var"
    LOCAL_ASSERTION_VAR = "local_assertion_var"

    # Let declarations
    LET_DECL = "let_decl"

    # Checkers
    CHECKER = "checker"
    CHECKER_INSTANCE = "checker_instance"
    CHECKER_INSTANCE_BODY = "checker_instance_body"

    # Random sequences
    RAND_SEQ_PRODUCTION = "rand_seq_production"

    # Coverage
    COVERGROUP_BODY = "covergroup_body"
    COVERPOINT = "coverpoint"
    COVER_CROSS = "cover_cross"
    COVER_CROSS_BODY = "cover_cross_body"
    COVERAGE_BIN = "coverage_bin"

    # Timing
    TIMING_PATH = "timing_path"
    PULSE_STYLE = "pulse_style"
    SYSTEM_TIMING_CHECK = "system_timing_check"

    # Miscellaneous
    ANONYMOUS_PROGRAM = "anonymous_program"
    NET_ALIAS = "net_alias"
    CONFIG_BLOCK = "config_block"

    # Legacy compatibility aliases (commonly used in LSP)
    MODULE = "module_legacy"  # Legacy module type
    INTERFACE = "interface_legacy"  # Legacy interface type
    PROGRAM = "program_legacy"  # Legacy program type
    CLASS = "class_legacy"  # Legacy class type
    FUNCTION = "function_legacy"  # Legacy function type
    TASK = "task_legacy"  # Legacy task type
    TYPEDEF = "typedef_legacy"  # Legacy typedef
    ENUM = "enum_legacy"  # Legacy enum type
    STRUCT = "struct_legacy"  # Legacy struct type
    UNION = "union_legacy"  # Legacy union type
    LOCALPARAM = "localparam_legacy"  # Legacy localparam
    ALWAYS_BLOCK = "always_block_legacy"  # Legacy always block
    INITIAL_BLOCK = "initial_block_legacy"  # Legacy initial block
    FINAL_BLOCK = "final_block_legacy"  # Legacy final block
    CLOCKING = "clocking_legacy"  # Legacy clocking
    COVERGROUP = "covergroup_legacy"  # Legacy covergroup
    ASSERTION = "assertion_legacy"  # Legacy assertion


@dataclass(frozen=True)
class Location:
    """Represents a location in source code."""
    file_path: str
    line: int  # 0-based
    column: int  # 0-based
    offset: Optional[int] = None  # Byte offset in file

    def __str__(self) -> str:
        return f"{self.file_path}:{self.line + 1}:{self.column + 1}"

    def to_lsp_position(self) -> Dict[str, int]:
        """Convert to LSP Position format."""
        return {"line": self.line, "character": self.column}

    def to_lsp_location(self) -> Dict[str, Union[str, Dict]]:
        """Convert to LSP Location format."""
        return {
            "uri": f"file://{Path(self.file_path).absolute()}",
            "range": {
                "start": self.to_lsp_position(),
                "end": self.to_lsp_position(),
            }
        }


@dataclass(frozen=True)
class Range:
    """Represents a range in source code."""
    start: Location
    end: Location

    def contains_position(self, position: Location) -> bool:
        """Check if this range contains the given position."""
        if position.file_path != self.start.file_path:
            return False

        # Check if position is within the range
        if position.line < self.start.line or position.line > self.end.line:
            return False

        if position.line == self.start.line and position.column < self.start.column:
            return False

        if position.line == self.end.line and position.column > self.end.column:
            return False

        return True

    def to_lsp_range(self) -> Dict[str, Dict[str, int]]:
        """Convert to LSP Range format."""
        return {
            "start": self.start.to_lsp_position(),
            "end": self.end.to_lsp_position(),
        }


@dataclass
class SymbolInfo:
    """Information about a SystemVerilog symbol."""
    name: str
    kind: SymbolKind
    definition_location: Location
    qualified_name: str = ""
    parent: Optional['SymbolInfo'] = None
    children: List['SymbolInfo'] = field(default_factory=list)
    references: List[Location] = field(default_factory=list)
    type_info: Optional[str] = None
    documentation: Optional[str] = None
    detail: Optional[str] = None

    def __post_init__(self):
        """Post-initialization processing."""
        if not self.qualified_name:
            if self.parent:
                self.qualified_name = f"{self.parent.qualified_name}.{self.name}"
            else:
                self.qualified_name = self.name

    def add_reference(self, location: Location) -> None:
        """Add a reference to this symbol."""
        if location not in self.references:
            self.references.append(location)

    def add_child(self, child: 'SymbolInfo') -> None:
        """Add a child symbol."""
        child.parent = self
        # Update qualified name when parent changes
        child.qualified_name = f"{self.qualified_name}.{child.name}"
        if child not in self.children:
            self.children.append(child)

    def to_lsp_symbol_information(self) -> Dict:
        """Convert to LSP SymbolInformation format."""
        return {
            "name": self.name,
            "kind": self._symbol_kind_to_lsp(),
            "location": self.definition_location.to_lsp_location(),
            "containerName": self.parent.name if self.parent else None,
        }

    def to_lsp_document_symbol(self) -> Dict:
        """Convert to LSP DocumentSymbol format."""
        return {
            "name": self.name,
            "detail": self.detail or "",
            "kind": self._symbol_kind_to_lsp(),
            "range": Range(self.definition_location, self.definition_location).to_lsp_range(),
            "selectionRange": Range(self.definition_location, self.definition_location).to_lsp_range(),
            "children": [child.to_lsp_document_symbol() for child in self.children],
        }

    def _symbol_kind_to_lsp(self) -> int:
        """Convert SymbolKind to LSP SymbolKind number."""
        # LSP SymbolKind constants
        mapping = {
            SymbolKind.MODULE: 2,      # Module
            SymbolKind.INTERFACE: 11,  # Interface
            SymbolKind.PACKAGE: 4,     # Package
            SymbolKind.CLASS: 5,       # Class
            SymbolKind.FUNCTION: 12,   # Function
            SymbolKind.TASK: 12,       # Function (closest match)
            SymbolKind.VARIABLE: 13,   # Variable
            SymbolKind.PARAMETER: 14,  # Constant
            SymbolKind.PORT: 13,       # Variable
            SymbolKind.TYPEDEF: 5,     # Class (closest match)
            SymbolKind.ENUM: 10,       # Enum
            SymbolKind.STRUCT: 23,     # Struct
            SymbolKind.UNION: 23,      # Struct (closest match)
            SymbolKind.PROPERTY: 7,    # Property
        }
        return mapping.get(self.kind, 1)  # Default to File


@dataclass
class FileIndex:
    """Index for a single file."""
    file_path: str
    symbols: List[SymbolInfo] = field(default_factory=list)
    symbol_map: Dict[Range, SymbolInfo] = field(default_factory=dict)
    last_modified: Optional[float] = None
    syntax_tree: Optional[object] = None  # pyslang.SyntaxTree
    compilation: Optional[object] = None  # pyslang.Compilation

    def add_symbol(self, symbol: SymbolInfo, range_info: Optional[Range] = None) -> None:
        """Add a symbol to this file index."""
        if symbol not in self.symbols:
            self.symbols.append(symbol)

        if range_info:
            self.symbol_map[range_info] = symbol

    def find_symbol_at_position(self, position: Location) -> Optional[SymbolInfo]:
        """Find the symbol at the given position."""
        for range_info, symbol in self.symbol_map.items():
            if range_info.contains_position(position):
                return symbol
        return None


@dataclass
class WorkspaceConfig:
    """Configuration for the workspace."""
    include_paths: List[str] = field(default_factory=list)
    defines: Dict[str, str] = field(default_factory=dict)
    max_errors: int = 100
    enable_diagnostics: bool = True
    file_extensions: Set[str] = field(default_factory=lambda: {".sv", ".svh", ".v", ".vh"})
    exclude_patterns: List[str] = field(default_factory=list)
