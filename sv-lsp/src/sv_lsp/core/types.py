"""
Core type definitions for the SystemVerilog LSP server.

This module defines the fundamental data structures used throughout
the LSP server implementation.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Set, Union
from pathlib import Path


class SymbolKind(Enum):
    """SystemVerilog symbol kinds."""
    MODULE = "module"
    INTERFACE = "interface"
    PACKAGE = "package"
    CLASS = "class"
    FUNCTION = "function"
    TASK = "task"
    VARIABLE = "variable"
    PARAMETER = "parameter"
    PORT = "port"
    TYPEDEF = "typedef"
    ENUM = "enum"
    STRUCT = "struct"
    UNION = "union"
    MODPORT = "modport"
    CLOCKING = "clocking"
    PROPERTY = "property"
    SEQUENCE = "sequence"
    COVERGROUP = "covergroup"
    ASSERTION = "assertion"
    UNKNOWN = "unknown"


@dataclass
class Location:
    """Represents a location in source code."""
    file_path: str
    line: int  # 0-based
    column: int  # 0-based
    offset: Optional[int] = None  # Byte offset in file
    
    def __str__(self) -> str:
        return f"{self.file_path}:{self.line + 1}:{self.column + 1}"
    
    def to_lsp_position(self) -> Dict[str, int]:
        """Convert to LSP Position format."""
        return {"line": self.line, "character": self.column}
    
    def to_lsp_location(self) -> Dict[str, Union[str, Dict]]:
        """Convert to LSP Location format."""
        return {
            "uri": f"file://{Path(self.file_path).absolute()}",
            "range": {
                "start": self.to_lsp_position(),
                "end": self.to_lsp_position(),
            }
        }


@dataclass
class Range:
    """Represents a range in source code."""
    start: Location
    end: Location
    
    def contains_position(self, position: Location) -> bool:
        """Check if this range contains the given position."""
        if position.file_path != self.start.file_path:
            return False
        
        # Check if position is within the range
        if position.line < self.start.line or position.line > self.end.line:
            return False
        
        if position.line == self.start.line and position.column < self.start.column:
            return False
        
        if position.line == self.end.line and position.column > self.end.column:
            return False
        
        return True
    
    def to_lsp_range(self) -> Dict[str, Dict[str, int]]:
        """Convert to LSP Range format."""
        return {
            "start": self.start.to_lsp_position(),
            "end": self.end.to_lsp_position(),
        }


@dataclass
class SymbolInfo:
    """Information about a SystemVerilog symbol."""
    name: str
    kind: SymbolKind
    definition_location: Location
    qualified_name: str = ""
    parent: Optional['SymbolInfo'] = None
    children: List['SymbolInfo'] = field(default_factory=list)
    references: List[Location] = field(default_factory=list)
    type_info: Optional[str] = None
    documentation: Optional[str] = None
    detail: Optional[str] = None
    
    def __post_init__(self):
        """Post-initialization processing."""
        if not self.qualified_name:
            if self.parent:
                self.qualified_name = f"{self.parent.qualified_name}.{self.name}"
            else:
                self.qualified_name = self.name
    
    def add_reference(self, location: Location) -> None:
        """Add a reference to this symbol."""
        if location not in self.references:
            self.references.append(location)
    
    def add_child(self, child: 'SymbolInfo') -> None:
        """Add a child symbol."""
        child.parent = self
        if child not in self.children:
            self.children.append(child)
    
    def to_lsp_symbol_information(self) -> Dict:
        """Convert to LSP SymbolInformation format."""
        return {
            "name": self.name,
            "kind": self._symbol_kind_to_lsp(),
            "location": self.definition_location.to_lsp_location(),
            "containerName": self.parent.name if self.parent else None,
        }
    
    def to_lsp_document_symbol(self) -> Dict:
        """Convert to LSP DocumentSymbol format."""
        return {
            "name": self.name,
            "detail": self.detail or "",
            "kind": self._symbol_kind_to_lsp(),
            "range": Range(self.definition_location, self.definition_location).to_lsp_range(),
            "selectionRange": Range(self.definition_location, self.definition_location).to_lsp_range(),
            "children": [child.to_lsp_document_symbol() for child in self.children],
        }
    
    def _symbol_kind_to_lsp(self) -> int:
        """Convert SymbolKind to LSP SymbolKind number."""
        # LSP SymbolKind constants
        mapping = {
            SymbolKind.MODULE: 2,      # Module
            SymbolKind.INTERFACE: 11,  # Interface
            SymbolKind.PACKAGE: 4,     # Package
            SymbolKind.CLASS: 5,       # Class
            SymbolKind.FUNCTION: 12,   # Function
            SymbolKind.TASK: 12,       # Function (closest match)
            SymbolKind.VARIABLE: 13,   # Variable
            SymbolKind.PARAMETER: 14,  # Constant
            SymbolKind.PORT: 13,       # Variable
            SymbolKind.TYPEDEF: 5,     # Class (closest match)
            SymbolKind.ENUM: 10,       # Enum
            SymbolKind.STRUCT: 23,     # Struct
            SymbolKind.UNION: 23,      # Struct (closest match)
            SymbolKind.PROPERTY: 7,    # Property
        }
        return mapping.get(self.kind, 1)  # Default to File


@dataclass
class FileIndex:
    """Index for a single file."""
    file_path: str
    symbols: List[SymbolInfo] = field(default_factory=list)
    symbol_map: Dict[Range, SymbolInfo] = field(default_factory=dict)
    last_modified: Optional[float] = None
    syntax_tree: Optional[object] = None  # pyslang.SyntaxTree
    compilation: Optional[object] = None  # pyslang.Compilation
    
    def add_symbol(self, symbol: SymbolInfo, range_info: Optional[Range] = None) -> None:
        """Add a symbol to this file index."""
        if symbol not in self.symbols:
            self.symbols.append(symbol)
        
        if range_info:
            self.symbol_map[range_info] = symbol
    
    def find_symbol_at_position(self, position: Location) -> Optional[SymbolInfo]:
        """Find the symbol at the given position."""
        for range_info, symbol in self.symbol_map.items():
            if range_info.contains_position(position):
                return symbol
        return None


@dataclass
class WorkspaceConfig:
    """Configuration for the workspace."""
    include_paths: List[str] = field(default_factory=list)
    defines: Dict[str, str] = field(default_factory=dict)
    max_errors: int = 100
    enable_diagnostics: bool = True
    file_extensions: Set[str] = field(default_factory=lambda: {".sv", ".svh", ".v", ".vh"})
    exclude_patterns: List[str] = field(default_factory=list)
