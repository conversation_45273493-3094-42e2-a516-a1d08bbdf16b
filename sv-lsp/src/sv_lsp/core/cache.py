"""
Cache manager for SystemVerilog LSP server.

This module provides caching functionality to improve performance
by storing parsed results and symbol information.
"""

import pickle
import hashlib
import time
from pathlib import Path
from typing import Any, Dict, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """
    Manages caching of parsed data and symbol information.
    
    This class provides:
    - File-based caching of parse results
    - Cache invalidation based on file modification times
    - Memory-based caching for frequently accessed data
    - Cache statistics and management
    """
    
    def __init__(self, cache_dir: Optional[str] = None, max_memory_cache_size: int = 100):
        """
        Initialize the cache manager.
        
        Args:
            cache_dir: Directory for persistent cache files
            max_memory_cache_size: Maximum number of items in memory cache
        """
        self.cache_dir = Path(cache_dir) if cache_dir else Path.cwd() / ".sv-lsp-cache"
        self.cache_dir.mkdir(exist_ok=True)
        
        self.max_memory_cache_size = max_memory_cache_size
        self.memory_cache: Dict[str, Tuple[Any, float]] = {}  # key -> (value, timestamp)
        self.access_times: Dict[str, float] = {}  # key -> last_access_time
        
    def get_file_cache_path(self, file_path: str) -> Path:
        """
        Get the cache file path for a source file.
        
        Args:
            file_path: Source file path
            
        Returns:
            Cache file path
        """
        # Create a hash of the file path for the cache filename
        file_hash = hashlib.md5(file_path.encode()).hexdigest()
        return self.cache_dir / f"{file_hash}.cache"
    
    def is_cache_valid(self, file_path: str) -> bool:
        """
        Check if the cache for a file is still valid.
        
        Args:
            file_path: Source file path
            
        Returns:
            True if cache is valid
        """
        cache_path = self.get_file_cache_path(file_path)
        
        if not cache_path.exists():
            return False
        
        try:
            source_mtime = Path(file_path).stat().st_mtime
            cache_mtime = cache_path.stat().st_mtime
            return cache_mtime >= source_mtime
        except (OSError, FileNotFoundError):
            return False
    
    def save_file_cache(self, file_path: str, data: Any) -> bool:
        """
        Save data to file cache.
        
        Args:
            file_path: Source file path
            data: Data to cache
            
        Returns:
            True if successfully saved
        """
        try:
            cache_path = self.get_file_cache_path(file_path)
            
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
            
            logger.debug(f"Saved cache for: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save cache for {file_path}: {e}")
            return False
    
    def load_file_cache(self, file_path: str) -> Optional[Any]:
        """
        Load data from file cache.
        
        Args:
            file_path: Source file path
            
        Returns:
            Cached data or None if not available
        """
        if not self.is_cache_valid(file_path):
            return None
        
        try:
            cache_path = self.get_file_cache_path(file_path)
            
            with open(cache_path, 'rb') as f:
                data = pickle.load(f)
            
            logger.debug(f"Loaded cache for: {file_path}")
            return data
            
        except Exception as e:
            logger.error(f"Failed to load cache for {file_path}: {e}")
            return None
    
    def invalidate_file_cache(self, file_path: str) -> bool:
        """
        Invalidate cache for a specific file.
        
        Args:
            file_path: Source file path
            
        Returns:
            True if cache was invalidated
        """
        try:
            cache_path = self.get_file_cache_path(file_path)
            if cache_path.exists():
                cache_path.unlink()
                logger.debug(f"Invalidated cache for: {file_path}")
                return True
        except Exception as e:
            logger.error(f"Failed to invalidate cache for {file_path}: {e}")
        
        return False
    
    def set_memory_cache(self, key: str, value: Any) -> None:
        """
        Store data in memory cache.
        
        Args:
            key: Cache key
            value: Data to cache
        """
        current_time = time.time()
        
        # Remove oldest items if cache is full
        if len(self.memory_cache) >= self.max_memory_cache_size:
            self._evict_oldest_memory_cache_item()
        
        self.memory_cache[key] = (value, current_time)
        self.access_times[key] = current_time
        logger.debug(f"Cached in memory: {key}")
    
    def get_memory_cache(self, key: str) -> Optional[Any]:
        """
        Retrieve data from memory cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached data or None if not found
        """
        if key in self.memory_cache:
            self.access_times[key] = time.time()
            value, _ = self.memory_cache[key]
            logger.debug(f"Memory cache hit: {key}")
            return value
        
        logger.debug(f"Memory cache miss: {key}")
        return None
    
    def invalidate_memory_cache(self, key: str) -> bool:
        """
        Invalidate a specific memory cache entry.
        
        Args:
            key: Cache key
            
        Returns:
            True if entry was invalidated
        """
        if key in self.memory_cache:
            del self.memory_cache[key]
            if key in self.access_times:
                del self.access_times[key]
            logger.debug(f"Invalidated memory cache: {key}")
            return True
        return False
    
    def clear_memory_cache(self) -> None:
        """Clear all memory cache entries."""
        self.memory_cache.clear()
        self.access_times.clear()
        logger.info("Memory cache cleared")
    
    def clear_file_cache(self) -> None:
        """Clear all file cache entries."""
        try:
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
            logger.info("File cache cleared")
        except Exception as e:
            logger.error(f"Failed to clear file cache: {e}")
    
    def clear_all(self) -> None:
        """Clear both memory and file caches."""
        self.clear_memory_cache()
        self.clear_file_cache()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        file_cache_count = len(list(self.cache_dir.glob("*.cache")))
        file_cache_size = sum(f.stat().st_size for f in self.cache_dir.glob("*.cache"))
        
        return {
            "memory_cache_size": len(self.memory_cache),
            "memory_cache_max_size": self.max_memory_cache_size,
            "file_cache_count": file_cache_count,
            "file_cache_size_bytes": file_cache_size,
            "cache_directory": str(self.cache_dir),
        }
    
    def _evict_oldest_memory_cache_item(self) -> None:
        """Evict the oldest item from memory cache."""
        if not self.access_times:
            return
        
        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        self.invalidate_memory_cache(oldest_key)
