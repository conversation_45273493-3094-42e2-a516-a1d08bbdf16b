"""
Symbol Kind Mapper for converting between slang and LSP symbol kinds.

This module provides utilities for mapping between slang's SymbolKind
and our LSP SymbolKind, as well as converting to LSP protocol symbol kinds.
"""

from typing import Dict, Optional
from .types import SymbolKind


class SymbolKindMapper:
    """
    Maps between different symbol kind representations.

    This class handles conversion between:
    1. Slang SymbolKind strings (from pyslang)
    2. Our internal SymbolKind enum
    3. LSP protocol SymbolKind numbers
    """

    # Mapping from slang SymbolKind strings to our SymbolKind enum
    SLANG_TO_INTERNAL: Dict[str, SymbolKind] = {
        # Core symbols
        "SymbolKind.Unknown": SymbolKind.UNKNOWN,
        "SymbolKind.Root": SymbolKind.ROOT,
        "SymbolKind.Definition": SymbolKind.DEFINITION,
        "SymbolKind.CompilationUnit": SymbolKind.COMPILATION_UNIT,
        "SymbolKind.DeferredMember": SymbolKind.DEFERRED_MEMBER,
        "SymbolKind.TransparentMember": SymbolKind.TRANSPARENT_MEMBER,
        "SymbolKind.EmptyMember": SymbolKind.EMPTY_MEMBER,

        # Type system
        "SymbolKind.PredefinedIntegerType": SymbolKind.PREDEFINED_INTEGER_TYPE,
        "SymbolKind.ScalarType": SymbolKind.SCALAR_TYPE,
        "SymbolKind.FloatingType": SymbolKind.FLOATING_TYPE,
        "SymbolKind.EnumType": SymbolKind.ENUM_TYPE,
        "SymbolKind.EnumValue": SymbolKind.ENUM_VALUE,
        "SymbolKind.PackedArrayType": SymbolKind.PACKED_ARRAY_TYPE,
        "SymbolKind.FixedSizeUnpackedArrayType": SymbolKind.FIXED_SIZE_UNPACKED_ARRAY_TYPE,
        "SymbolKind.DynamicArrayType": SymbolKind.DYNAMIC_ARRAY_TYPE,
        "SymbolKind.DPIOpenArrayType": SymbolKind.DPI_OPEN_ARRAY_TYPE,
        "SymbolKind.AssociativeArrayType": SymbolKind.ASSOCIATIVE_ARRAY_TYPE,
        "SymbolKind.QueueType": SymbolKind.QUEUE_TYPE,
        "SymbolKind.PackedStructType": SymbolKind.PACKED_STRUCT_TYPE,
        "SymbolKind.UnpackedStructType": SymbolKind.UNPACKED_STRUCT_TYPE,
        "SymbolKind.PackedUnionType": SymbolKind.PACKED_UNION_TYPE,
        "SymbolKind.UnpackedUnionType": SymbolKind.UNPACKED_UNION_TYPE,
        "SymbolKind.ClassType": SymbolKind.CLASS_TYPE,
        "SymbolKind.CovergroupType": SymbolKind.COVERGROUP_TYPE,
        "SymbolKind.VoidType": SymbolKind.VOID_TYPE,
        "SymbolKind.NullType": SymbolKind.NULL_TYPE,
        "SymbolKind.CHandleType": SymbolKind.CHANDLE_TYPE,
        "SymbolKind.StringType": SymbolKind.STRING_TYPE,
        "SymbolKind.EventType": SymbolKind.EVENT_TYPE,
        "SymbolKind.UnboundedType": SymbolKind.UNBOUNDED_TYPE,
        "SymbolKind.TypeRefType": SymbolKind.TYPE_REF_TYPE,
        "SymbolKind.UntypedType": SymbolKind.UNTYPED_TYPE,
        "SymbolKind.SequenceType": SymbolKind.SEQUENCE_TYPE,
        "SymbolKind.PropertyType": SymbolKind.PROPERTY_TYPE,
        "SymbolKind.VirtualInterfaceType": SymbolKind.VIRTUAL_INTERFACE_TYPE,
        "SymbolKind.TypeAlias": SymbolKind.TYPE_ALIAS,
        "SymbolKind.ErrorType": SymbolKind.ERROR_TYPE,
        "SymbolKind.ForwardingTypedef": SymbolKind.FORWARDING_TYPEDEF,
        "SymbolKind.NetType": SymbolKind.NET_TYPE,

        # Parameters and ports
        "SymbolKind.Parameter": SymbolKind.PARAMETER,
        "SymbolKind.TypeParameter": SymbolKind.TYPE_PARAMETER,
        "SymbolKind.Port": SymbolKind.PORT,
        "SymbolKind.MultiPort": SymbolKind.MULTI_PORT,
        "SymbolKind.InterfacePort": SymbolKind.INTERFACE_PORT,
        "SymbolKind.Modport": SymbolKind.MODPORT,
        "SymbolKind.ModportPort": SymbolKind.MODPORT_PORT,
        "SymbolKind.ModportClocking": SymbolKind.MODPORT_CLOCKING,

        # Instances and modules
        "SymbolKind.Instance": SymbolKind.INSTANCE,
        "SymbolKind.InstanceBody": SymbolKind.INSTANCE_BODY,
        "SymbolKind.InstanceArray": SymbolKind.INSTANCE_ARRAY,
        "SymbolKind.Package": SymbolKind.PACKAGE,
        "SymbolKind.ExplicitImport": SymbolKind.EXPLICIT_IMPORT,
        "SymbolKind.WildcardImport": SymbolKind.WILDCARD_IMPORT,

        # Attributes and generation
        "SymbolKind.Attribute": SymbolKind.ATTRIBUTE,
        "SymbolKind.Genvar": SymbolKind.GENVAR,
        "SymbolKind.GenerateBlock": SymbolKind.GENERATE_BLOCK,
        "SymbolKind.GenerateBlockArray": SymbolKind.GENERATE_BLOCK_ARRAY,

        # Procedural blocks
        "SymbolKind.ProceduralBlock": SymbolKind.PROCEDURAL_BLOCK,
        "SymbolKind.StatementBlock": SymbolKind.STATEMENT_BLOCK,

        # Variables and nets
        "SymbolKind.Net": SymbolKind.NET,
        "SymbolKind.Variable": SymbolKind.VARIABLE,
        "SymbolKind.FormalArgument": SymbolKind.FORMAL_ARGUMENT,
        "SymbolKind.Field": SymbolKind.FIELD,
        "SymbolKind.ClassProperty": SymbolKind.CLASS_PROPERTY,

        # Subroutines
        "SymbolKind.Subroutine": SymbolKind.SUBROUTINE,

        # Assignments and tasks
        "SymbolKind.ContinuousAssign": SymbolKind.CONTINUOUS_ASSIGN,
        "SymbolKind.ElabSystemTask": SymbolKind.ELAB_SYSTEM_TASK,

        # Generic and method definitions
        "SymbolKind.GenericClassDef": SymbolKind.GENERIC_CLASS_DEF,
        "SymbolKind.MethodPrototype": SymbolKind.METHOD_PROTOTYPE,
        "SymbolKind.UninstantiatedDef": SymbolKind.UNINSTANTIATED_DEF,

        # Iteration and patterns
        "SymbolKind.Iterator": SymbolKind.ITERATOR,
        "SymbolKind.PatternVar": SymbolKind.PATTERN_VAR,

        # Constraints
        "SymbolKind.ConstraintBlock": SymbolKind.CONSTRAINT_BLOCK,

        # Parameters and specifications
        "SymbolKind.DefParam": SymbolKind.DEF_PARAM,
        "SymbolKind.Specparam": SymbolKind.SPECPARAM,

        # Primitives
        "SymbolKind.Primitive": SymbolKind.PRIMITIVE,
        "SymbolKind.PrimitivePort": SymbolKind.PRIMITIVE_PORT,
        "SymbolKind.PrimitiveInstance": SymbolKind.PRIMITIVE_INSTANCE,

        # Specify blocks
        "SymbolKind.SpecifyBlock": SymbolKind.SPECIFY_BLOCK,

        # Assertions and properties
        "SymbolKind.Sequence": SymbolKind.SEQUENCE,
        "SymbolKind.Property": SymbolKind.PROPERTY,
        "SymbolKind.AssertionPort": SymbolKind.ASSERTION_PORT,

        # Clocking
        "SymbolKind.ClockingBlock": SymbolKind.CLOCKING_BLOCK,
        "SymbolKind.ClockVar": SymbolKind.CLOCK_VAR,
        "SymbolKind.LocalAssertionVar": SymbolKind.LOCAL_ASSERTION_VAR,

        # Let declarations
        "SymbolKind.LetDecl": SymbolKind.LET_DECL,

        # Checkers
        "SymbolKind.Checker": SymbolKind.CHECKER,
        "SymbolKind.CheckerInstance": SymbolKind.CHECKER_INSTANCE,
        "SymbolKind.CheckerInstanceBody": SymbolKind.CHECKER_INSTANCE_BODY,

        # Random sequences
        "SymbolKind.RandSeqProduction": SymbolKind.RAND_SEQ_PRODUCTION,

        # Coverage
        "SymbolKind.CovergroupBody": SymbolKind.COVERGROUP_BODY,
        "SymbolKind.Coverpoint": SymbolKind.COVERPOINT,
        "SymbolKind.CoverCross": SymbolKind.COVER_CROSS,
        "SymbolKind.CoverCrossBody": SymbolKind.COVER_CROSS_BODY,
        "SymbolKind.CoverageBin": SymbolKind.COVERAGE_BIN,

        # Timing
        "SymbolKind.TimingPath": SymbolKind.TIMING_PATH,
        "SymbolKind.PulseStyle": SymbolKind.PULSE_STYLE,
        "SymbolKind.SystemTimingCheck": SymbolKind.SYSTEM_TIMING_CHECK,

        # Miscellaneous
        "SymbolKind.AnonymousProgram": SymbolKind.ANONYMOUS_PROGRAM,
        "SymbolKind.NetAlias": SymbolKind.NET_ALIAS,
        "SymbolKind.ConfigBlock": SymbolKind.CONFIG_BLOCK,
    }

    # Mapping from our SymbolKind to LSP protocol SymbolKind numbers
    # Based on LSP specification: https://microsoft.github.io/language-server-protocol/specifications/lsp/3.17/specification/#symbolKind
    INTERNAL_TO_LSP: Dict[SymbolKind, int] = {
        # File and Module level
        SymbolKind.ROOT: 1,  # File
        SymbolKind.COMPILATION_UNIT: 1,  # File
        SymbolKind.INSTANCE: 2,  # Module
        SymbolKind.INSTANCE_BODY: 2,  # Module
        SymbolKind.PACKAGE: 4,  # Package

        # Classes and Types
        SymbolKind.CLASS_TYPE: 5,  # Class
        SymbolKind.INTERFACE: 2,  # Interface (treated as Module)

        # Legacy compatibility mappings
        SymbolKind.MODULE: 2,  # Module
        SymbolKind.CLASS: 5,  # Class
        SymbolKind.FUNCTION: 12,  # Function
        SymbolKind.TASK: 12,  # Function
        SymbolKind.TYPEDEF: 5,  # Class (typedef)
        SymbolKind.ENUM: 10,  # Enum
        SymbolKind.STRUCT: 23,  # Struct
        SymbolKind.UNION: 23,  # Struct
        SymbolKind.LOCALPARAM: 14,  # Constant
        SymbolKind.ALWAYS_BLOCK: 12,  # Function
        SymbolKind.INITIAL_BLOCK: 12,  # Function
        SymbolKind.FINAL_BLOCK: 12,  # Function
        SymbolKind.CLOCKING: 2,  # Module
        SymbolKind.COVERGROUP: 5,  # Class
        SymbolKind.ASSERTION: 13,  # Variable
        SymbolKind.ENUM_TYPE: 10,  # Enum
        SymbolKind.ENUM_VALUE: 22,  # EnumMember
        SymbolKind.PACKED_STRUCT_TYPE: 23,  # Struct
        SymbolKind.UNPACKED_STRUCT_TYPE: 23,  # Struct
        SymbolKind.TYPE_ALIAS: 5,  # Class (typedef)

        # Functions and Methods
        SymbolKind.SUBROUTINE: 12,  # Function
        SymbolKind.METHOD_PROTOTYPE: 6,  # Method

        # Variables and Properties
        SymbolKind.VARIABLE: 13,  # Variable
        SymbolKind.NET: 13,  # Variable
        SymbolKind.FIELD: 8,  # Field
        SymbolKind.CLASS_PROPERTY: 7,  # Property
        SymbolKind.PARAMETER: 14,  # Constant
        SymbolKind.PORT: 13,  # Variable

        # Control structures
        SymbolKind.PROCEDURAL_BLOCK: 12,  # Function
        SymbolKind.STATEMENT_BLOCK: 12,  # Function
        SymbolKind.GENERATE_BLOCK: 2,  # Module

        # Arrays and Collections
        SymbolKind.PACKED_ARRAY_TYPE: 18,  # Array
        SymbolKind.DYNAMIC_ARRAY_TYPE: 18,  # Array
        SymbolKind.QUEUE_TYPE: 18,  # Array

        # Default mappings
        SymbolKind.UNKNOWN: 13,  # Variable (default)
    }

    @classmethod
    def from_slang(cls, slang_kind: str) -> SymbolKind:
        """
        Convert slang SymbolKind string to our internal SymbolKind.

        Args:
            slang_kind: String representation from slang (e.g., "SymbolKind.Variable")

        Returns:
            Our internal SymbolKind enum value
        """
        return cls.SLANG_TO_INTERNAL.get(slang_kind, SymbolKind.UNKNOWN)

    @classmethod
    def to_lsp(cls, internal_kind: SymbolKind) -> int:
        """
        Convert our internal SymbolKind to LSP protocol SymbolKind number.

        Args:
            internal_kind: Our internal SymbolKind enum value

        Returns:
            LSP protocol SymbolKind number
        """
        return cls.INTERNAL_TO_LSP.get(internal_kind, 13)  # Default to Variable

    @classmethod
    def get_category(cls, internal_kind: SymbolKind) -> str:
        """
        Get the category of a symbol kind for grouping and organization.

        Args:
            internal_kind: Our internal SymbolKind enum value

        Returns:
            Category string
        """
        type_symbols = {
            SymbolKind.PREDEFINED_INTEGER_TYPE, SymbolKind.SCALAR_TYPE,
            SymbolKind.FLOATING_TYPE, SymbolKind.ENUM_TYPE, SymbolKind.PACKED_ARRAY_TYPE,
            SymbolKind.CLASS_TYPE, SymbolKind.PACKED_STRUCT_TYPE, SymbolKind.UNPACKED_STRUCT_TYPE,
            SymbolKind.PACKED_UNION_TYPE, SymbolKind.UNPACKED_UNION_TYPE, SymbolKind.TYPE_ALIAS
        }

        module_symbols = {
            SymbolKind.ROOT, SymbolKind.COMPILATION_UNIT, SymbolKind.DEFINITION,
            SymbolKind.INSTANCE, SymbolKind.INSTANCE_BODY, SymbolKind.PACKAGE,
            SymbolKind.GENERATE_BLOCK
        }

        declaration_symbols = {
            SymbolKind.VARIABLE, SymbolKind.NET, SymbolKind.PARAMETER,
            SymbolKind.PORT, SymbolKind.SUBROUTINE, SymbolKind.FIELD, SymbolKind.GENVAR
        }

        verification_symbols = {
            SymbolKind.PROPERTY, SymbolKind.SEQUENCE, SymbolKind.ASSERTION_PORT,
            SymbolKind.COVERPOINT, SymbolKind.COVER_CROSS, SymbolKind.CHECKER
        }

        timing_symbols = {
            SymbolKind.CLOCKING_BLOCK, SymbolKind.TIMING_PATH, SymbolKind.SYSTEM_TIMING_CHECK
        }

        if internal_kind in type_symbols:
            return "type"
        elif internal_kind in module_symbols:
            return "module"
        elif internal_kind in declaration_symbols:
            return "declaration"
        elif internal_kind in verification_symbols:
            return "verification"
        elif internal_kind in timing_symbols:
            return "timing"
        else:
            return "other"

    @classmethod
    def is_container(cls, internal_kind: SymbolKind) -> bool:
        """
        Check if a symbol kind represents a container that can have children.

        Args:
            internal_kind: Our internal SymbolKind enum value

        Returns:
            True if the symbol can contain other symbols
        """
        container_kinds = {
            SymbolKind.ROOT, SymbolKind.COMPILATION_UNIT, SymbolKind.INSTANCE,
            SymbolKind.INSTANCE_BODY, SymbolKind.PACKAGE, SymbolKind.CLASS_TYPE,
            SymbolKind.GENERATE_BLOCK, SymbolKind.PROCEDURAL_BLOCK, SymbolKind.STATEMENT_BLOCK,
            SymbolKind.PACKED_STRUCT_TYPE, SymbolKind.UNPACKED_STRUCT_TYPE,
            SymbolKind.PACKED_UNION_TYPE, SymbolKind.UNPACKED_UNION_TYPE,
            SymbolKind.COVERGROUP_TYPE, SymbolKind.CHECKER
        }
        return internal_kind in container_kinds
