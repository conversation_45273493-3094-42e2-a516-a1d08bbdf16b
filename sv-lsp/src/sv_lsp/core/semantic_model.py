"""
Semantic Model for SystemVerilog LSP.

This module implements a semantic model similar to slang's approach,
providing bidirectional mapping between syntax nodes and symbols.
"""

from typing import Dict, Optional, List, Tuple, Set, Any
import logging
from dataclasses import dataclass, field
from weakref import WeakKeyDictionary

from .types import SymbolInfo, SymbolKind, Location, Range

logger = logging.getLogger(__name__)


@dataclass(unsafe_hash=True)
class SyntaxNode:
    """Represents a syntax node in the parse tree."""
    kind: str
    location: Location
    text: str = ""
    parent: Optional['SyntaxNode'] = field(default=None, compare=False, hash=False)
    children: List['SyntaxNode'] = field(default_factory=list, compare=False, hash=False)
    node_id: int = field(default_factory=lambda: id(object()), compare=False)

    def __post_init__(self):
        # Set parent for all children
        for child in self.children:
            child.parent = self


@dataclass
class SymbolLocation:
    """Represents a specific location where a symbol appears."""
    location: Location
    kind: str  # "definition", "reference", "declaration"
    syntax_node: Optional[SyntaxNode] = None


class SemanticModel:
    """
    Semantic model that manages the relationship between syntax nodes and symbols.

    This class provides bidirectional mapping similar to slang's SemanticModel:
    - SyntaxNode -> Symbol (getDeclaredSymbol)
    - Symbol -> SyntaxNode (getSyntax)
    """

    def __init__(self):
        """Initialize the semantic model."""
        # Core mappings
        self.symbol_cache: Dict[SyntaxNode, SymbolInfo] = {}
        self.syntax_cache: Dict[str, SyntaxNode] = {}  # symbol_id -> syntax_node

        # Symbol location tracking
        self.symbol_locations: Dict[str, List[SymbolLocation]] = {}  # symbol_id -> locations
        self.location_symbols: Dict[Location, List[SymbolInfo]] = {}  # location -> symbols

        # Hierarchical relationships
        self.parent_child_map: Dict[str, List[str]] = {}  # parent_id -> child_ids
        self.child_parent_map: Dict[str, str] = {}  # child_id -> parent_id

        # Reference tracking
        self.symbol_references: Dict[str, Set[Location]] = {}  # symbol_id -> reference_locations
        self.reference_symbols: Dict[Location, str] = {}  # location -> symbol_id

    def get_declared_symbol(self, syntax_node: SyntaxNode) -> Optional[SymbolInfo]:
        """
        Get the symbol declared by a syntax node.

        Args:
            syntax_node: The syntax node to query

        Returns:
            The symbol declared by this node, or None if not found
        """
        # Check cache first
        if syntax_node in self.symbol_cache:
            return self.symbol_cache[syntax_node]

        # Try to resolve the symbol
        symbol = self._resolve_symbol(syntax_node)
        if symbol:
            self.symbol_cache[syntax_node] = symbol
            self.syntax_cache[symbol.qualified_name] = syntax_node

        return symbol

    def get_syntax_node(self, symbol: SymbolInfo) -> Optional[SyntaxNode]:
        """
        Get the syntax node that declares a symbol.

        Args:
            symbol: The symbol to query

        Returns:
            The syntax node that declares this symbol, or None if not found
        """
        return self.syntax_cache.get(symbol.qualified_name)

    def add_symbol_location(self, symbol: SymbolInfo, location: Location,
                           kind: str, syntax_node: Optional[SyntaxNode] = None) -> None:
        """
        Add a location where a symbol appears.

        Args:
            symbol: The symbol
            location: The location where it appears
            kind: Type of appearance ("definition", "reference", "declaration")
            syntax_node: Optional syntax node at this location
        """
        symbol_id = symbol.qualified_name

        # Add to symbol locations
        if symbol_id not in self.symbol_locations:
            self.symbol_locations[symbol_id] = []

        symbol_location = SymbolLocation(location, kind, syntax_node)
        self.symbol_locations[symbol_id].append(symbol_location)

        # Add to location symbols
        if location not in self.location_symbols:
            self.location_symbols[location] = []
        self.location_symbols[location].append(symbol)

        # Track references separately
        if kind == "reference":
            if symbol_id not in self.symbol_references:
                self.symbol_references[symbol_id] = set()
            self.symbol_references[symbol_id].add(location)
            self.reference_symbols[location] = symbol_id

    def get_symbol_locations(self, symbol: SymbolInfo) -> List[SymbolLocation]:
        """Get all locations where a symbol appears."""
        return self.symbol_locations.get(symbol.qualified_name, [])

    def get_symbols_at_location(self, location: Location) -> List[SymbolInfo]:
        """Get all symbols at a specific location."""
        return self.location_symbols.get(location, [])

    def get_symbol_references(self, symbol: SymbolInfo) -> Set[Location]:
        """Get all reference locations for a symbol."""
        return self.symbol_references.get(symbol.qualified_name, set())

    def get_symbol_definition(self, symbol: SymbolInfo) -> Optional[Location]:
        """Get the definition location of a symbol."""
        locations = self.get_symbol_locations(symbol)
        for loc in locations:
            if loc.kind == "definition":
                return loc.location
        return symbol.definition_location  # Fallback to original location

    def add_parent_child_relationship(self, parent: SymbolInfo, child: SymbolInfo) -> None:
        """Add a parent-child relationship between symbols."""
        parent_id = parent.qualified_name
        child_id = child.qualified_name

        if parent_id not in self.parent_child_map:
            self.parent_child_map[parent_id] = []
        self.parent_child_map[parent_id].append(child_id)
        self.child_parent_map[child_id] = parent_id

    def get_children(self, symbol: SymbolInfo) -> List[str]:
        """Get child symbol IDs for a symbol."""
        return self.parent_child_map.get(symbol.qualified_name, [])

    def get_parent(self, symbol: SymbolInfo) -> Optional[str]:
        """Get parent symbol ID for a symbol."""
        return self.child_parent_map.get(symbol.qualified_name)

    def find_symbol_at_position(self, file_path: str, line: int, column: int) -> Optional[SymbolInfo]:
        """
        Find the symbol at a specific position.

        Args:
            file_path: File path
            line: Line number (0-based)
            column: Column number (0-based)

        Returns:
            Symbol at the position, or None if not found
        """
        target_location = Location(file_path=file_path, line=line, column=column)

        # Look for exact matches first
        symbols = self.get_symbols_at_location(target_location)
        if symbols:
            return symbols[0]  # Return first match

        # Look for symbols that contain this position
        for location, symbols in self.location_symbols.items():
            if (location.file_path == file_path and
                location.line == line and
                abs(location.column - column) <= 10):  # Fuzzy match within 10 characters
                return symbols[0]

        return None

    def get_symbol_hierarchy(self, symbol: SymbolInfo) -> Dict[str, Any]:
        """
        Get the complete hierarchy information for a symbol.

        Returns:
            Dictionary containing hierarchy information
        """
        result = {
            "symbol": symbol,
            "parent": self.get_parent(symbol),
            "children": self.get_children(symbol),
            "locations": [loc.__dict__ for loc in self.get_symbol_locations(symbol)],
            "references": list(self.get_symbol_references(symbol)),
            "definition": self.get_symbol_definition(symbol)
        }
        return result

    def _resolve_symbol(self, syntax_node: SyntaxNode) -> Optional[SymbolInfo]:
        """
        Resolve a symbol from a syntax node.

        This is a simplified version of slang's resolution logic.
        """
        try:
            # Handle different syntax node types
            if "ModuleDeclaration" in syntax_node.kind:
                return self._create_module_symbol(syntax_node)
            elif "VariableDeclaration" in syntax_node.kind:
                return self._create_variable_symbol(syntax_node)
            elif "ParameterDeclaration" in syntax_node.kind:
                return self._create_parameter_symbol(syntax_node)
            elif "PortDeclaration" in syntax_node.kind:
                return self._create_port_symbol(syntax_node)

            return None
        except Exception as e:
            logger.debug(f"Error resolving symbol from syntax node: {e}")
            return None

    def _create_module_symbol(self, syntax_node: SyntaxNode) -> SymbolInfo:
        """Create a module symbol from syntax node."""
        # Extract module name from syntax node text or location
        name = self._extract_identifier(syntax_node)
        return SymbolInfo(
            name=name,
            kind=SymbolKind.MODULE,
            definition_location=syntax_node.location,
            qualified_name=name
        )

    def _create_variable_symbol(self, syntax_node: SyntaxNode) -> SymbolInfo:
        """Create a variable symbol from syntax node."""
        name = self._extract_identifier(syntax_node)
        return SymbolInfo(
            name=name,
            kind=SymbolKind.VARIABLE,
            definition_location=syntax_node.location,
            qualified_name=self._get_qualified_name(syntax_node, name)
        )

    def _create_parameter_symbol(self, syntax_node: SyntaxNode) -> SymbolInfo:
        """Create a parameter symbol from syntax node."""
        name = self._extract_identifier(syntax_node)
        return SymbolInfo(
            name=name,
            kind=SymbolKind.PARAMETER,
            definition_location=syntax_node.location,
            qualified_name=self._get_qualified_name(syntax_node, name)
        )

    def _create_port_symbol(self, syntax_node: SyntaxNode) -> SymbolInfo:
        """Create a port symbol from syntax node."""
        name = self._extract_identifier(syntax_node)
        return SymbolInfo(
            name=name,
            kind=SymbolKind.PORT,
            definition_location=syntax_node.location,
            qualified_name=self._get_qualified_name(syntax_node, name)
        )

    def _extract_identifier(self, syntax_node: SyntaxNode) -> str:
        """Extract identifier name from syntax node."""
        # This is a simplified extraction - in practice would parse the syntax
        if syntax_node.text:
            # Try to extract identifier from text
            text = syntax_node.text.strip()

            # Handle module declarations
            if "module" in text:
                parts = text.split()
                for i, part in enumerate(parts):
                    if part == "module" and i + 1 < len(parts):
                        name = parts[i + 1].split('(')[0].strip()
                        if name.isidentifier():
                            return name

            # Handle other declarations
            words = text.split()
            for word in words:
                # Clean up word (remove punctuation)
                clean_word = ''.join(c for c in word if c.isalnum() or c == '_')
                if clean_word.isidentifier() and not clean_word in ['logic', 'input', 'output', 'wire', 'reg']:
                    return clean_word

        return f"unknown_{id(syntax_node)}"

    def _get_qualified_name(self, syntax_node: SyntaxNode, name: str) -> str:
        """Get qualified name for a symbol based on its syntax context."""
        # Walk up the syntax tree to build qualified name
        parts = [name]
        current = syntax_node.parent

        while current:
            if "ModuleDeclaration" in current.kind:
                module_name = self._extract_identifier(current)
                parts.append(module_name)
                break
            current = current.parent

        return ".".join(reversed(parts))

    def clear(self) -> None:
        """Clear all cached data."""
        self.symbol_cache.clear()
        self.syntax_cache.clear()
        self.symbol_locations.clear()
        self.location_symbols.clear()
        self.parent_child_map.clear()
        self.child_parent_map.clear()
        self.symbol_references.clear()
        self.reference_symbols.clear()

    def get_statistics(self) -> Dict[str, int]:
        """Get statistics about the semantic model."""
        return {
            "cached_symbols": len(self.symbol_cache),
            "syntax_nodes": len(self.syntax_cache),
            "symbol_locations": sum(len(locs) for locs in self.symbol_locations.values()),
            "location_symbols": len(self.location_symbols),
            "hierarchical_relationships": len(self.parent_child_map),
            "references": sum(len(refs) for refs in self.symbol_references.values())
        }
