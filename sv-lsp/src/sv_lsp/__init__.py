"""
SystemVerilog Language Server Protocol implementation.

This package provides a Language Server Protocol (LSP) implementation for SystemVerilog
based on pyslang, offering features like go-to-definition, find-references, and hover
information for SystemVerilog code.
"""

__version__ = "0.1.0"
__author__ = "SystemVerilog LSP Team"
__email__ = "<EMAIL>"

from .core.symbol_manager import SymbolManager
from .core.position_mapper import PositionMapper
from .core.workspace import WorkspaceManager
from .lsp.server import SVLanguageServer

__all__ = [
    "SymbolManager",
    "PositionMapper", 
    "WorkspaceManager",
    "SVLanguageServer",
]
