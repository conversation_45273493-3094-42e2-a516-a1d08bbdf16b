"""
Type resolver for SystemVerilog code.

This module provides functionality to resolve types and type relationships
in SystemVerilog code using pyslang.
"""

from typing import Dict, List, Optional, Set
import logging

try:
    import pyslang
    PYSLANG_AVAILABLE = True
except ImportError:
    PYSLANG_AVAILABLE = False
    pyslang = None

from ..core.types import SymbolInfo, SymbolKind

logger = logging.getLogger(__name__)


class TypeResolver:
    """
    Resolves types and type relationships in SystemVerilog code.
    
    This class provides:
    - Type resolution for variables and expressions
    - Type hierarchy analysis
    - Type compatibility checking
    - Type information extraction
    """
    
    def __init__(self):
        """Initialize the type resolver."""
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - type resolution will be limited")
        
        self.type_cache: Dict[str, str] = {}
        self.type_hierarchy: Dict[str, Set[str]] = {}
    
    def resolve_symbol_type(self, symbol: SymbolInfo) -> Optional[str]:
        """
        Resolve the type of a symbol.
        
        Args:
            symbol: Symbol to resolve type for
            
        Returns:
            Type string or None if not resolvable
        """
        if not PYSLANG_AVAILABLE:
            return symbol.type_info
        
        # Check cache first
        cache_key = f"{symbol.qualified_name}:{symbol.kind.value}"
        if cache_key in self.type_cache:
            return self.type_cache[cache_key]
        
        # Try to resolve type based on symbol kind
        type_str = None
        
        if symbol.kind == SymbolKind.VARIABLE:
            type_str = self._resolve_variable_type(symbol)
        elif symbol.kind == SymbolKind.PARAMETER:
            type_str = self._resolve_parameter_type(symbol)
        elif symbol.kind == SymbolKind.PORT:
            type_str = self._resolve_port_type(symbol)
        elif symbol.kind == SymbolKind.FUNCTION:
            type_str = self._resolve_function_type(symbol)
        elif symbol.kind == SymbolKind.TYPEDEF:
            type_str = self._resolve_typedef_type(symbol)
        else:
            type_str = symbol.type_info
        
        # Cache the result
        if type_str:
            self.type_cache[cache_key] = type_str
        
        return type_str
    
    def _resolve_variable_type(self, symbol: SymbolInfo) -> Optional[str]:
        """
        Resolve the type of a variable symbol.
        
        Args:
            symbol: Variable symbol
            
        Returns:
            Type string or None
        """
        # If we already have type info, use it
        if symbol.type_info:
            return symbol.type_info
        
        # TODO: Use pyslang to get more detailed type information
        # This would involve accessing the symbol's type from the AST
        
        return None
    
    def _resolve_parameter_type(self, symbol: SymbolInfo) -> Optional[str]:
        """
        Resolve the type of a parameter symbol.
        
        Args:
            symbol: Parameter symbol
            
        Returns:
            Type string or None
        """
        if symbol.type_info:
            return symbol.type_info
        
        # Parameters often have implicit types based on their values
        # TODO: Implement parameter type inference
        
        return "parameter"
    
    def _resolve_port_type(self, symbol: SymbolInfo) -> Optional[str]:
        """
        Resolve the type of a port symbol.
        
        Args:
            symbol: Port symbol
            
        Returns:
            Type string or None
        """
        if symbol.type_info:
            return symbol.type_info
        
        # Ports have direction and data type
        # TODO: Extract port direction and type from AST
        
        return "port"
    
    def _resolve_function_type(self, symbol: SymbolInfo) -> Optional[str]:
        """
        Resolve the type of a function symbol.
        
        Args:
            symbol: Function symbol
            
        Returns:
            Type string or None
        """
        if symbol.type_info:
            return symbol.type_info
        
        # Functions have return types and parameter lists
        # TODO: Extract function signature from AST
        
        return "function"
    
    def _resolve_typedef_type(self, symbol: SymbolInfo) -> Optional[str]:
        """
        Resolve the type of a typedef symbol.
        
        Args:
            symbol: Typedef symbol
            
        Returns:
            Type string or None
        """
        if symbol.type_info:
            return symbol.type_info
        
        # Typedefs define new type names
        # TODO: Extract the underlying type from AST
        
        return "typedef"
    
    def get_type_hierarchy(self, type_name: str) -> Set[str]:
        """
        Get the type hierarchy for a given type.
        
        Args:
            type_name: Name of the type
            
        Returns:
            Set of related types in the hierarchy
        """
        return self.type_hierarchy.get(type_name, set())
    
    def is_type_compatible(self, from_type: str, to_type: str) -> bool:
        """
        Check if one type is compatible with another.
        
        Args:
            from_type: Source type
            to_type: Target type
            
        Returns:
            True if types are compatible
        """
        # Basic compatibility rules
        if from_type == to_type:
            return True
        
        # TODO: Implement SystemVerilog type compatibility rules
        # This would include:
        # - Implicit conversions
        # - Packed/unpacked array compatibility
        # - Struct/union compatibility
        # - Class inheritance
        
        return False
    
    def get_type_width(self, type_str: str) -> Optional[int]:
        """
        Get the bit width of a type.
        
        Args:
            type_str: Type string
            
        Returns:
            Bit width or None if not applicable
        """
        if not type_str:
            return None
        
        # Parse common type patterns
        import re
        
        # logic [N:0] or logic [N-1:0]
        match = re.search(r'logic\s*\[(\d+):0\]', type_str)
        if match:
            return int(match.group(1)) + 1
        
        match = re.search(r'logic\s*\[(\d+)-1:0\]', type_str)
        if match:
            return int(match.group(1))
        
        # bit [N:0]
        match = re.search(r'bit\s*\[(\d+):0\]', type_str)
        if match:
            return int(match.group(1)) + 1
        
        # reg [N:0]
        match = re.search(r'reg\s*\[(\d+):0\]', type_str)
        if match:
            return int(match.group(1)) + 1
        
        # Single bit types
        if type_str in ['logic', 'bit', 'reg', 'wire']:
            return 1
        
        # TODO: Handle more complex types like structs, arrays, etc.
        
        return None
    
    def is_packed_type(self, type_str: str) -> bool:
        """
        Check if a type is a packed type.
        
        Args:
            type_str: Type string
            
        Returns:
            True if type is packed
        """
        if not type_str:
            return False
        
        # Packed types include vectors and packed arrays/structs
        packed_keywords = ['logic', 'bit', 'reg']
        
        for keyword in packed_keywords:
            if keyword in type_str:
                return True
        
        # TODO: Handle packed structs, unions, and arrays
        
        return False
    
    def get_array_dimensions(self, type_str: str) -> List[int]:
        """
        Get the dimensions of an array type.
        
        Args:
            type_str: Type string
            
        Returns:
            List of dimension sizes
        """
        if not type_str:
            return []
        
        dimensions = []
        
        # TODO: Parse array dimensions from type string
        # This would handle both packed and unpacked arrays
        
        return dimensions
    
    def clear_cache(self) -> None:
        """Clear the type resolution cache."""
        self.type_cache.clear()
        self.type_hierarchy.clear()
        logger.debug("Type resolver cache cleared")
