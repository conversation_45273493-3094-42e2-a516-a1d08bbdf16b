from typing import Optional, List
import logging

try:
    import pyslang
    PYSLANG_AVAILABLE = True
except ImportError:
    PYSLANG_AVAILABLE = False
    pyslang = None

from ..core.types import SymbolInfo, SymbolKind, Location

logger = logging.getLogger(__name__)


class SymbolVisitor:
    """Visitor for extracting symbols from pyslang syntax trees."""

    def __init__(self, file_path: str, symbols: List[SymbolInfo]):
        self.file_path = file_path
        self.symbols = symbols

    def visit(self, obj):
        """Visit a syntax node and extract symbols."""
        if not PYSLANG_AVAILABLE or pyslang is None:
            return

        try:
            # Handle different node types
            if hasattr(obj, 'kind'):
                kind_str = str(obj.kind)
                logger.debug(f"Visiting node: {kind_str}")

                if 'ModuleDeclaration' in kind_str:
                    self._handle_module(obj)
                elif 'VariableDeclaration' in kind_str:
                    self._handle_variable(obj)
                elif 'ParameterDeclaration' in kind_str:
                    self._handle_parameter(obj)
                elif 'PortDeclaration' in kind_str:
                    self._handle_port(obj)
                elif 'DataDeclaration' in kind_str:
                    self._handle_data_declaration(obj)

            # Continue visiting children (avoid infinite recursion)
            if hasattr(obj, 'childNodes'):
                try:
                    for child in obj.childNodes():
                        if child and hasattr(child, 'visit'):
                            child.visit(lambda x: self.visit(x))
                except:
                    pass
        except Exception as e:
            logger.debug(f"Error visiting node: {e}")

    def _handle_module(self, node):
        """Handle module declaration."""
        try:
            # Try to get module name from different possible locations
            name = "unknown"
            if hasattr(node, 'header') and node.header:
                if hasattr(node.header, 'name') and node.header.name:
                    if hasattr(node.header.name, 'valueText'):
                        name = str(node.header.name.valueText)
                    elif hasattr(node.header.name, 'text'):
                        name = str(node.header.name.text)

            # Create location (use default if we can't get exact location)
            location = self._get_location(node)
            if not location:
                location = Location(file_path=self.file_path, line=0, column=0)

            symbol = SymbolInfo(
                name=name,
                kind=SymbolKind.MODULE,
                definition_location=location
            )
            self.symbols.append(symbol)
            logger.debug(f"Added module symbol: {name}")
        except Exception as e:
            logger.debug(f"Error handling module: {e}")

    def _handle_variable(self, node):
        """Handle variable declaration."""
        try:
            if hasattr(node, 'declarators'):
                for declarator in node.declarators:
                    if hasattr(declarator, 'name'):
                        name = str(declarator.name.valueText)
                        location = self._get_location(declarator)
                        if location:
                            symbol = SymbolInfo(
                                name=name,
                                kind=SymbolKind.VARIABLE,
                                definition_location=location
                            )
                            self.symbols.append(symbol)
        except Exception as e:
            logger.debug(f"Error handling variable: {e}")

    def _handle_parameter(self, node):
        """Handle parameter declaration."""
        try:
            if hasattr(node, 'declarators'):
                for declarator in node.declarators:
                    if hasattr(declarator, 'name'):
                        name = str(declarator.name.valueText)
                        location = self._get_location(declarator)
                        if location:
                            symbol = SymbolInfo(
                                name=name,
                                kind=SymbolKind.PARAMETER,
                                definition_location=location
                            )
                            self.symbols.append(symbol)
        except Exception as e:
            logger.debug(f"Error handling parameter: {e}")

    def _handle_port(self, node):
        """Handle port declaration."""
        try:
            if hasattr(node, 'declarators'):
                for declarator in node.declarators:
                    if hasattr(declarator, 'name'):
                        name = str(declarator.name.valueText)
                        location = self._get_location(declarator)
                        if location:
                            symbol = SymbolInfo(
                                name=name,
                                kind=SymbolKind.PORT,
                                definition_location=location
                            )
                            self.symbols.append(symbol)
        except Exception as e:
            logger.debug(f"Error handling port: {e}")

    def _handle_data_declaration(self, node):
        """Handle data declaration."""
        try:
            if hasattr(node, 'declarators'):
                for declarator in node.declarators:
                    if hasattr(declarator, 'name'):
                        name = str(declarator.name.valueText)
                        location = self._get_location(declarator)
                        if location:
                            symbol = SymbolInfo(
                                name=name,
                                kind=SymbolKind.VARIABLE,
                                definition_location=location
                            )
                            self.symbols.append(symbol)
        except Exception as e:
            logger.debug(f"Error handling data declaration: {e}")

    def _get_location(self, node) -> Optional[Location]:
        """Get location from a syntax node."""
        try:
            if hasattr(node, 'sourceRange'):
                source_range = node.sourceRange
                if source_range and hasattr(source_range, 'start'):
                    start = source_range.start
                    if hasattr(start, 'line') and hasattr(start, 'column'):
                        return Location(
                            file_path=self.file_path,
                            line=start.line,
                            column=start.column
                        )
        except Exception as e:
            logger.debug(f"Error getting location: {e}")
        return None


class SymbolExtractor:
    """
    Extracts symbols from SystemVerilog syntax trees.
    """

    def __init__(self):
        """Initialize the symbol extractor."""
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - symbol extraction will be limited")

    def extract_from_file(self, file_path: str) -> List[SymbolInfo]:
        """
        Extract symbols from a SystemVerilog file.

        Args:
            file_path: Path to the SystemVerilog file

        Returns:
            List of extracted symbols
        """
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - returning empty symbol list")
            return []

        try:
            with open(file_path, 'r') as f:
                content = f.read()
            return self.extract_from_text(content, file_path)
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return []

    def extract_from_text(self, text: str, file_path: str = "<string>") -> List[SymbolInfo]:
        """
        Extract symbols from SystemVerilog text.

        Args:
            text: SystemVerilog source code
            file_path: Optional file path for location information

        Returns:
            List of extracted symbols
        """
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - returning empty symbol list")
            return []

        assert pyslang is not None

        try:
            # Parse the text
            tree = pyslang.SyntaxTree.fromText(text, file_path)
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        logger.error(f"Parse error in {file_path}: {diag}")

            # Extract symbols from the syntax tree
            symbols = []
            visitor = SymbolVisitor(file_path, symbols)

            # Use a lambda to make the visitor callable
            def visit_func(obj):
                visitor.visit(obj)

            tree.root.visit(visit_func)

            logger.info(f"Extracted {len(symbols)} symbols from {file_path}")
            return symbols

        except Exception as e:
            logger.error(f"Error extracting symbols from {file_path}: {e}")
            return []

    def get_symbol_at(self, file_path: str, offset: int) -> Optional[SymbolInfo]:
        """
        Gets the symbol at a specific location in a file.

        Args:
            file_path: Path to the SystemVerilog file
            offset: Offset in the file

        Returns:
            SymbolInfo for the symbol at the location, or None if no symbol is found.
        """
        if not PYSLANG_AVAILABLE:
            logger.error("Cannot get symbol: pyslang not available")
            return None

        assert pyslang is not None

        try:
            # Parse the file
            tree = pyslang.SyntaxTree.fromFile(file_path)
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        logger.error(f"Parse error in {file_path}: {diag}")

            # Create compilation unit
            compilation = pyslang.Compilation()
            compilation.addSyntaxTree(tree)

            # Find the syntax node at the offset
            node = self._find_node_at_offset(tree.root, offset)
            if not node:
                return None

            # Get the scope
            scope = compilation.getRoot()

            # Find the symbol
            symbol = self._find_symbol(scope, node)
            if not symbol:
                return None

            # Create symbol info
            location = self._get_symbol_location(symbol)
            if location:
                symbol_info = SymbolInfo(
                    name=str(symbol.name),
                    kind=self._map_symbol_kind(symbol.kind),
                    definition_location=location,
                )
            else:
                # Create a default location if we can't get one from the symbol
                symbol_info = SymbolInfo(
                    name=str(symbol.name),
                    kind=self._map_symbol_kind(symbol.kind),
                    definition_location=Location(file_path=file_path, line=0, column=0),
                )

            return symbol_info

        except Exception as e:
            logger.error(f"Error getting symbol at {file_path}:{offset}: {e}")
            return None

    def _find_node_at_offset(self, node, offset):
        """
        Recursively finds the syntax node at a given offset.
        """
        for i in range(len(node)):
            child = node[i]
            if hasattr(child, 'sourceRange'):
                if child.sourceRange.start.offset <= offset and \
                   child.sourceRange.end.offset >= offset:
                    # This is a token, not a node, so we can't recurse
                    if not hasattr(child, '__len__'):
                        return child
                    return self._find_node_at_offset(child, offset)
        return node

    def _find_symbol(self, scope, node):
        """
        Finds the symbol for a given syntax node.
        """
        if not hasattr(node, 'identifier'):
            return None

        symbol_name = node.identifier.valueText
        symbol = scope.find(symbol_name)
        if symbol:
            return symbol

        # If the symbol is not in the root scope, try to find it in the module scope
        for member in scope.members:
            if hasattr(member, 'kind') and str(member.kind).endswith('Instance'):
                instance_symbol = member
                symbol = instance_symbol.body.find(symbol_name)
                if symbol:
                    return symbol

        return None

    def _map_symbol_kind(self, pyslang_kind: object) -> SymbolKind:
        """
        Map pyslang SymbolKind to our SymbolKind.
        """
        if not PYSLANG_AVAILABLE:
            return SymbolKind.UNKNOWN

        kind_str = str(pyslang_kind)

        if "Module" in kind_str:
            return SymbolKind.MODULE
        elif "Interface" in kind_str:
            return SymbolKind.INTERFACE
        elif "Package" in kind_str:
            return SymbolKind.PACKAGE
        elif "Class" in kind_str:
            return SymbolKind.CLASS
        elif "Subroutine" in kind_str:
            return SymbolKind.FUNCTION
        elif "Variable" in kind_str:
            return SymbolKind.VARIABLE
        elif "Parameter" in kind_str:
            return SymbolKind.PARAMETER
        elif "Port" in kind_str:
            return SymbolKind.PORT
        elif "TypeAlias" in kind_str:
            return SymbolKind.TYPEDEF
        elif "Enum" in kind_str:
            return SymbolKind.ENUM
        elif "Struct" in kind_str:
            return SymbolKind.STRUCT
        elif "Union" in kind_str:
            return SymbolKind.UNION
        elif "Modport" in kind_str:
            return SymbolKind.MODPORT

        return SymbolKind.UNKNOWN

    def _get_symbol_location(self, symbol) -> Optional[Location]:
        """
        Get the location of a symbol.
        """
        try:
            location_info = symbol.location
            if not location_info:
                return None

            file_path = "<unknown>"
            if hasattr(location_info, 'buffer'):
                buffer = location_info.buffer
                if hasattr(buffer, 'name'):
                    file_path = str(buffer.name)

            return Location(
                file_path=file_path,
                line=location_info.line,
                column=location_info.column,
                offset=location_info.offset
            )

        except Exception as e:
            logger.debug(f"Error getting symbol location: {e}")
            return None