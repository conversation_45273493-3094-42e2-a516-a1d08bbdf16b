from typing import Optional
import logging

try:
    import pyslang
    PYSLANG_AVAILABLE = True
except ImportError:
    PYSLANG_AVAILABLE = False
    pyslang = None

from ..core.types import SymbolInfo, SymbolKind, Location

logger = logging.getLogger(__name__)


class SymbolExtractor:
    """
    Extracts symbols from SystemVerilog syntax trees.
    """

    def __init__(self):
        """Initialize the symbol extractor."""
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - symbol extraction will be limited")

    def get_symbol_at(self, file_path: str, offset: int) -> Optional[SymbolInfo]:
        """
        Gets the symbol at a specific location in a file.

        Args:
            file_path: Path to the SystemVerilog file
            offset: Offset in the file

        Returns:
            SymbolInfo for the symbol at the location, or None if no symbol is found.
        """
        if not PYSLANG_AVAILABLE:
            logger.error("Cannot get symbol: pyslang not available")
            return None

        assert pyslang is not None

        try:
            # Parse the file
            tree = pyslang.SyntaxTree.fromFile(file_path)
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        logger.error(f"Parse error in {file_path}: {diag}")

            # Create compilation unit
            compilation = pyslang.Compilation()
            compilation.addSyntaxTree(tree)

            # Find the syntax node at the offset
            node = self._find_node_at_offset(tree.root, offset)
            if not node:
                return None

            # Get the scope
            scope = compilation.getRoot()

            # Find the symbol
            symbol = self._find_symbol(scope, node)
            if not symbol:
                return None

            # Create symbol info
            symbol_info = SymbolInfo(
                name=str(symbol.name),
                kind=self._map_symbol_kind(symbol.kind),
                definition_location=self._get_symbol_location(symbol),
            )

            return symbol_info

        except Exception as e:
            logger.error(f"Error getting symbol at {file_path}:{offset}: {e}")
            return None

    def _find_node_at_offset(self, node, offset):
        """
        Recursively finds the syntax node at a given offset.
        """
        for i in range(len(node)):
            child = node[i]
            if hasattr(child, 'sourceRange'):
                if child.sourceRange.start.offset <= offset and \
                   child.sourceRange.end.offset >= offset:
                    # This is a token, not a node, so we can't recurse
                    if not hasattr(child, '__len__'):
                        return child
                    return self._find_node_at_offset(child, offset)
        return node

    def _find_symbol(self, scope, node):
        """
        Finds the symbol for a given syntax node.
        """
        if not hasattr(node, 'identifier'):
            return None

        symbol_name = node.identifier.valueText
        symbol = scope.find(symbol_name)
        if symbol:
            return symbol

        # If the symbol is not in the root scope, try to find it in the module scope
        for member in scope.members:
            if member.kind == pyslang.SymbolKind.Instance:
                instance_symbol = member
                symbol = instance_symbol.body.find(symbol_name)
                if symbol:
                    return symbol

        return None

    def _map_symbol_kind(self, pyslang_kind: object) -> SymbolKind:
        """
        Map pyslang SymbolKind to our SymbolKind.
        """
        if not PYSLANG_AVAILABLE:
            return SymbolKind.UNKNOWN

        kind_str = str(pyslang_kind)

        if "Module" in kind_str:
            return SymbolKind.MODULE
        elif "Interface" in kind_str:
            return SymbolKind.INTERFACE
        elif "Package" in kind_str:
            return SymbolKind.PACKAGE
        elif "Class" in kind_str:
            return SymbolKind.CLASS
        elif "Subroutine" in kind_str:
            return SymbolKind.FUNCTION
        elif "Variable" in kind_str:
            return SymbolKind.VARIABLE
        elif "Parameter" in kind_str:
            return SymbolKind.PARAMETER
        elif "Port" in kind_str:
            return SymbolKind.PORT
        elif "TypeAlias" in kind_str:
            return SymbolKind.TYPEDEF
        elif "Enum" in kind_str:
            return SymbolKind.ENUM
        elif "Struct" in kind_str:
            return SymbolKind.STRUCT
        elif "Union" in kind_str:
            return SymbolKind.UNION
        elif "Modport" in kind_str:
            return SymbolKind.MODPORT

        return SymbolKind.UNKNOWN

    def _get_symbol_location(self, symbol) -> Optional[Location]:
        """
        Get the location of a symbol.
        """
        try:
            location_info = symbol.location
            if not location_info:
                return None

            file_path = "<unknown>"
            if hasattr(location_info, 'buffer'):
                buffer = location_info.buffer
                if hasattr(buffer, 'name'):
                    file_path = str(buffer.name)

            return Location(
                file_path=file_path,
                line=location_info.line,
                column=location_info.column,
                offset=location_info.offset
            )

        except Exception as e:
            logger.debug(f"Error getting symbol location: {e}")
            return None