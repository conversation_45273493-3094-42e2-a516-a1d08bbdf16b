"""
Symbol extractor for SystemVerilog code.

This module provides functionality to extract symbols from SystemVerilog
syntax trees using pyslang.
"""

from typing import List, Optional, Union
import logging

try:
    import pyslang
    PYSLANG_AVAILABLE = True
except ImportError:
    PYSLANG_AVAILABLE = False
    pyslang = None

from ..core.types import SymbolInfo, SymbolKind, Location

logger = logging.getLogger(__name__)


class SymbolExtractor:
    """
    Extracts symbols from SystemVerilog syntax trees.

    This class uses the visitor pattern to traverse pyslang AST nodes
    and extract symbol information for the LSP server.
    """

    def __init__(self):
        """Initialize the symbol extractor."""
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - symbol extraction will be limited")

        self.symbols: List[SymbolInfo] = []
        self.current_scope: Optional[SymbolInfo] = None

    def extract_from_file(self, file_path: str) -> List[SymbolInfo]:
        """
        Extract symbols from a SystemVerilog file.

        Args:
            file_path: Path to the SystemVerilog file

        Returns:
            List of extracted symbols
        """
        if not PYSLANG_AVAILABLE:
            logger.error("Cannot extract symbols: pyslang not available")
            return []

        assert pyslang is not None

        try:
            # Parse the file
            tree = pyslang.SyntaxTree.fromFile(file_path)
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        logger.error(f"Parse error in {file_path}: {diag}")

            # Create compilation unit
            compilation = pyslang.Compilation()
            compilation.addSyntaxTree(tree)

            # Extract symbols
            self.symbols = []
            self.current_scope = None

            # Visit all symbols in the compilation
            compilation.getRoot().visit(self)

            logger.info(f"Extracted {len(self.symbols)} symbols from {file_path}")
            return self.symbols.copy()

        except Exception as e:
            logger.error(f"Error extracting symbols from {file_path}: {e}")
            return []

    def extract_from_text(self, text: str, file_path: str = "<text>") -> List[SymbolInfo]:
        """
        Extract symbols from SystemVerilog text.

        Args:
            text: SystemVerilog source code
            file_path: Virtual file path for the text

        Returns:
            List of extracted symbols
        """
        if not PYSLANG_AVAILABLE:
            logger.error("Cannot extract symbols: pyslang not available")
            return []

        assert pyslang is not None

        try:
            # Parse the text
            tree = pyslang.SyntaxTree.fromText(text, file_path)
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        logger.error(f"Parse error in {file_path}: {diag}")

            # Create compilation unit
            compilation = pyslang.Compilation()
            compilation.addSyntaxTree(tree)

            # Extract symbols
            self.symbols = []
            self.current_scope = None

            # Visit all symbols in the compilation
            compilation.getRoot().visit(self)

            logger.debug(f"Extracted {len(self.symbols)} symbols from text")
            return self.symbols.copy()

        except Exception as e:
            logger.error(f"Error extracting symbols from text: {e}")
            return []

    def __call__(self, obj) -> None:
        """
        Visitor method called for each AST node.

        Args:
            obj: AST node (Token or Symbol)
        """
        if not PYSLANG_AVAILABLE:
            return

        # Handle different node types
        try:
            # Check if this is a symbol
            if hasattr(obj, 'kind') and hasattr(obj, 'name'):
                self._visit_symbol(obj)
            # Skip other nodes to avoid infinite recursion
            pass
        except Exception as e:
            logger.debug(f"Error visiting node: {e}")

    def _visit_symbol(self, symbol) -> None:
        """
        Visit a pyslang Symbol.

        Args:
            symbol: pyslang Symbol object
        """
        try:
            if not hasattr(symbol, 'kind') or not hasattr(symbol, 'name'):
                return

            # Map pyslang symbol kinds to our symbol kinds
            symbol_kind = self._map_symbol_kind(symbol.kind)
            print(symbol.name, symbol.kind, symbol_kind)
            if symbol_kind == SymbolKind.UNKNOWN:
                return

            # Get symbol location
            location = self._get_symbol_location(symbol)
            if not location:
                return

            # Create symbol info
            symbol_info = SymbolInfo(
                name=str(symbol.name),
                kind=symbol_kind,
                definition_location=location,
                parent=self.current_scope
            )

            # Add type information if available
            if hasattr(symbol, 'type') and symbol.type:
                symbol_info.type_info = str(symbol.type)
            # Add to parent if we have one
            if self.current_scope:
                self.current_scope.add_child(symbol_info)

            self.symbols.append(symbol_info)

            # If this symbol can contain other symbols, set it as current scope
            if self._is_scope_symbol(symbol_kind):
                old_scope = self.current_scope
                self.current_scope = symbol_info

                # Visit children if this is a scope
                if hasattr(symbol, 'members'):
                    try:
                        for member in symbol.members():
                            if hasattr(member, 'visit'):
                                member.visit(self)
                    except:
                        pass

                self.current_scope = old_scope

        except Exception as e:
            logger.debug(f"Error processing symbol: {e}")
            return

        # Add to parent if we have one
        if self.current_scope:
            self.current_scope.add_child(symbol_info)

        self.symbols.append(symbol_info)

        # If this symbol can contain other symbols, set it as current scope
        if self._is_scope_symbol(symbol_kind):
            old_scope = self.current_scope
            self.current_scope = symbol_info

            # Visit children if this is a scope
            if hasattr(symbol, 'members'):
                for member in symbol.members():
                    member.visit(self)

            self.current_scope = old_scope

    def _map_symbol_kind(self, pyslang_kind: object) -> SymbolKind:
        """
        Map pyslang SymbolKind to our SymbolKind.

        Args:
            pyslang_kind: pyslang SymbolKind

        Returns:
            Mapped SymbolKind
        """
        if not PYSLANG_AVAILABLE:
            return SymbolKind.UNKNOWN

        # Map common symbol kinds - use string comparison for compatibility
        kind_str = str(pyslang_kind)

        if "Module" in kind_str:
            return SymbolKind.MODULE
        elif "Interface" in kind_str:
            return SymbolKind.INTERFACE
        elif "Package" in kind_str:
            return SymbolKind.PACKAGE
        elif "Class" in kind_str:
            return SymbolKind.CLASS
        elif "Subroutine" in kind_str:
            return SymbolKind.FUNCTION
        elif "Variable" in kind_str:
            return SymbolKind.VARIABLE
        elif "Parameter" in kind_str:
            return SymbolKind.PARAMETER
        elif "Port" in kind_str:
            return SymbolKind.PORT
        elif "TypeAlias" in kind_str:
            return SymbolKind.TYPEDEF
        elif "Enum" in kind_str:
            return SymbolKind.ENUM
        elif "Struct" in kind_str:
            return SymbolKind.STRUCT
        elif "Union" in kind_str:
            return SymbolKind.UNION
        elif "Modport" in kind_str:
            return SymbolKind.MODPORT

        return SymbolKind.UNKNOWN

    def _get_symbol_location(self, symbol) -> Optional[Location]:
        """
        Get the location of a symbol.

        Args:
            symbol: pyslang Symbol

        Returns:
            Symbol location or None
        """
        try:
            # Try different ways to get location information
            location_info = None

            if hasattr(symbol, 'location'):
                location_info = symbol.location
            elif hasattr(symbol, 'sourceRange') and hasattr(symbol.sourceRange, 'start'):
                location_info = symbol.sourceRange.start

            if not location_info:
                return None

            # Extract file path and position
            file_path = "<unknown>"
            line = 0
            column = 0
            offset = None

            # Try to get file information
            if hasattr(location_info, 'buffer'):
                buffer = location_info.buffer
                if hasattr(buffer, 'name'):
                    file_path = str(buffer.name)

            # Try to get position information
            if hasattr(location_info, 'offset'):
                offset = int(location_info.offset)

                # If we have buffer, try to convert offset to line/column
                if hasattr(location_info, 'buffer') and hasattr(location_info.buffer, 'data'):
                    try:
                        text = str(location_info.buffer.data)
                        line, column = self._offset_to_position(text, offset)
                    except:
                        pass

            return Location(
                file_path=file_path,
                line=line,
                column=column,
                offset=offset
            )

        except Exception as e:
            logger.debug(f"Error getting symbol location: {e}")
            return None

    def _offset_to_position(self, text: str, offset: int) -> tuple[int, int]:
        """Convert byte offset to line/column position."""
        if offset >= len(text):
            lines = text.split('\n')
            return len(lines) - 1, len(lines[-1])

        lines = text[:offset].split('\n')
        line = len(lines) - 1
        column = len(lines[-1])

        return line, column

    def _is_scope_symbol(self, kind: SymbolKind) -> bool:
        """
        Check if a symbol kind represents a scope that can contain other symbols.

        Args:
            kind: Symbol kind

        Returns:
            True if symbol is a scope
        """
        scope_kinds = {
            SymbolKind.MODULE,
            SymbolKind.INTERFACE,
            SymbolKind.PACKAGE,
            SymbolKind.CLASS,
            SymbolKind.FUNCTION,
            SymbolKind.TASK,
        }
        return kind in scope_kinds
