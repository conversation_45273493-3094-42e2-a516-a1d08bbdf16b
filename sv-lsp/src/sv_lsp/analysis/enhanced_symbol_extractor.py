"""
Enhanced Symbol Extractor with Semantic Model Integration.

This module provides an enhanced symbol extractor that builds relationships
between syntax nodes and symbols, similar to slang's approach.
"""

from typing import List, Optional, Dict, Any
import logging

from ..core.types import SymbolInfo, SymbolKind, Location
from ..core.semantic_model import SemanticModel, SyntaxNode
from ..core.symbol_kind_mapper import SymbolKindMapper
from ..utils.pyslang_wrapper import PYSLANG_AVAILABLE

if PYSLANG_AVAILABLE:
    import pyslang

logger = logging.getLogger(__name__)


class EnhancedSymbolExtractor:
    """
    Enhanced symbol extractor that builds semantic relationships.

    This extractor creates a semantic model that tracks:
    - Bidirectional syntax node <-> symbol mapping
    - Symbol location tracking (definitions, references, declarations)
    - Hierarchical relationships between symbols
    - Reference analysis
    """

    def __init__(self):
        """Initialize the enhanced symbol extractor."""
        self.semantic_model = SemanticModel()
        self.current_file_path = ""

    def extract_from_file(self, file_path: str) -> List[SymbolInfo]:
        """
        Extract symbols from a SystemVerilog file with semantic analysis.

        Args:
            file_path: Path to the SystemVerilog file

        Returns:
            List of extracted symbols with enhanced relationships
        """
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - returning empty symbol list")
            return []

        try:
            with open(file_path, 'r') as f:
                content = f.read()
            return self.extract_from_text(content, file_path)
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return []

    def extract_from_text(self, text: str, file_path: str = "<string>") -> List[SymbolInfo]:
        """
        Extract symbols from SystemVerilog text with semantic analysis.

        Args:
            text: SystemVerilog source code
            file_path: Optional file path for location information

        Returns:
            List of extracted symbols with enhanced relationships
        """
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - returning empty symbol list")
            return []

        assert pyslang is not None
        self.current_file_path = file_path

        try:
            # Parse the text
            tree = pyslang.SyntaxTree.fromText(text, file_path)
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        logger.error(f"Parse error in {file_path}: {diag}")

            # Extract symbols directly using pyslang visitor pattern
            symbols = []
            self._extract_symbols_with_visitor(tree.root, symbols)
            logger.debug(f"Extracted {len(symbols)} symbols using visitor pattern")

            # Semantic relationships are built during extraction

            logger.info(f"Extracted {len(symbols)} symbols with semantic analysis from {file_path}")
            return symbols

        except Exception as e:
            logger.error(f"Error extracting symbols from {file_path}: {e}")
            return []

    def get_semantic_model(self) -> SemanticModel:
        """Get the semantic model with all relationships."""
        return self.semantic_model

    def find_symbol_at_position(self, file_path: str, line: int, column: int) -> Optional[SymbolInfo]:
        """Find symbol at a specific position using semantic model."""
        return self.semantic_model.find_symbol_at_position(file_path, line, column)

    def get_symbol_references(self, symbol: SymbolInfo) -> List[Location]:
        """Get all reference locations for a symbol."""
        return list(self.semantic_model.get_symbol_references(symbol))

    def get_symbol_hierarchy(self, symbol: SymbolInfo) -> Dict[str, Any]:
        """Get complete hierarchy information for a symbol."""
        return self.semantic_model.get_symbol_hierarchy(symbol)

    def _extract_symbols_with_visitor(self, pyslang_root, symbols: List[SymbolInfo]) -> None:
        """
        Extract symbols using pyslang visitor pattern.

        Args:
            pyslang_root: Root pyslang syntax node
            symbols: List to append symbols to
        """
        current_module = None

        def visit_node(node):
            nonlocal current_module

            try:
                kind_str = str(node.kind)
                location = self._get_location_from_pyslang(node)

                # Handle module declarations
                if kind_str == "SyntaxKind.ModuleDeclaration":
                    name = self._extract_module_name(node)
                    if name:
                        symbol = SymbolInfo(
                            name=name,
                            kind=SymbolKind.INSTANCE,
                            definition_location=location,
                            qualified_name=name
                        )
                        symbols.append(symbol)
                        current_module = symbol

                        # Add to semantic model
                        self.semantic_model.add_symbol_location(
                            symbol, location, "definition"
                        )
                        logger.debug(f"Found module: {name}")

                # Handle identifiers (for module names)
                elif kind_str == "TokenKind.Identifier":
                    # Check if this is a module name (follows module keyword)
                    text = str(node).strip()
                    if text and text.isidentifier() and current_module is None:
                        # This might be a module name
                        symbol = SymbolInfo(
                            name=text,
                            kind=SymbolKind.INSTANCE,
                            definition_location=location,
                            qualified_name=text
                        )
                        symbols.append(symbol)
                        current_module = symbol

                        # Add to semantic model
                        self.semantic_model.add_symbol_location(
                            symbol, location, "definition"
                        )
                        logger.debug(f"Found module (from identifier): {text}")

                # Handle variable declarations
                elif kind_str == "SyntaxKind.DataDeclaration":
                    var_names = self._extract_variable_names(node)
                    for name in var_names:
                        if name:
                            qualified_name = f"{current_module.name}.{name}" if current_module else name
                            symbol = SymbolInfo(
                                name=name,
                                kind=SymbolKind.VARIABLE,
                                definition_location=location,
                                qualified_name=qualified_name,
                                parent=current_module
                            )
                            symbols.append(symbol)

                            # Add to semantic model
                            self.semantic_model.add_symbol_location(
                                symbol, location, "definition"
                            )

                            # Add hierarchy relationship
                            if current_module:
                                self.semantic_model.add_parent_child_relationship(current_module, symbol)
                                if current_module.children is None:
                                    current_module.children = []
                                current_module.children.append(symbol)

                            logger.debug(f"Found variable: {name}")

                # Handle port declarations
                elif kind_str == "SyntaxKind.ImplicitAnsiPort":
                    port_name = self._extract_port_name(node)
                    if port_name:
                        qualified_name = f"{current_module.name}.{port_name}" if current_module else port_name
                        symbol = SymbolInfo(
                            name=port_name,
                            kind=SymbolKind.PORT,
                            definition_location=location,
                            qualified_name=qualified_name,
                            parent=current_module
                        )
                        symbols.append(symbol)

                        # Add to semantic model
                        self.semantic_model.add_symbol_location(
                            symbol, location, "definition"
                        )

                        # Add hierarchy relationship
                        if current_module:
                            self.semantic_model.add_parent_child_relationship(current_module, symbol)
                            if current_module.children is None:
                                current_module.children = []
                            current_module.children.append(symbol)

                        logger.debug(f"Found port: {port_name}")

            except Exception as e:
                logger.debug(f"Error processing node {kind_str}: {e}")

        # Use pyslang visitor pattern
        try:
            pyslang_root.visit(visit_node)
        except Exception as e:
            logger.error(f"Error during symbol extraction: {e}")

    def _extract_module_name(self, module_node) -> Optional[str]:
        """Extract module name from module declaration node."""
        try:
            # Convert to string and parse
            text = str(module_node)
            lines = text.split('\n')
            first_line = lines[0].strip()

            # Look for "module name(" pattern
            if first_line.startswith('module '):
                parts = first_line.split()
                if len(parts) >= 2:
                    name = parts[1].split('(')[0].strip()
                    if name.isidentifier():
                        return name
            return None
        except Exception as e:
            logger.debug(f"Error extracting module name: {e}")
            return None

    def _extract_variable_names(self, data_node) -> List[str]:
        """Extract variable names from data declaration node."""
        try:
            text = str(data_node).strip()

            # Simple parsing for "logic variable_name;"
            if 'logic ' in text:
                # Find the part after 'logic'
                parts = text.split('logic')
                if len(parts) > 1:
                    var_part = parts[1].strip()
                    # Remove semicolon and extract identifier
                    var_part = var_part.rstrip(';').strip()
                    if var_part.isidentifier():
                        return [var_part]

            return []
        except Exception as e:
            logger.debug(f"Error extracting variable names: {e}")
            return []

    def _extract_port_name(self, port_node) -> Optional[str]:
        """Extract port name from port declaration node."""
        try:
            text = str(port_node).strip()

            # Look for identifier at the end
            words = text.split()
            for word in reversed(words):
                clean_word = word.strip(',();')
                if clean_word.isidentifier() and clean_word not in ['input', 'output', 'logic']:
                    return clean_word

            return None
        except Exception as e:
            logger.debug(f"Error extracting port name: {e}")
            return None

    def _build_syntax_tree(self, pyslang_node) -> SyntaxNode:
        """
        Build our syntax tree representation from pyslang nodes.

        Args:
            pyslang_node: pyslang syntax node

        Returns:
            Our SyntaxNode representation
        """
        try:
            # Get location information
            location = self._get_location_from_pyslang(pyslang_node)

            # Create syntax node
            syntax_node = SyntaxNode(
                kind=str(pyslang_node.kind),
                location=location,
                text=str(pyslang_node) if hasattr(pyslang_node, '__str__') else ""
            )

            # Build children recursively
            if hasattr(pyslang_node, 'childNodes'):
                try:
                    child_nodes = pyslang_node.childNodes()
                    logger.debug(f"Node {pyslang_node.kind} has {len(list(child_nodes))} children")

                    # Re-get children since we consumed the iterator
                    for child in pyslang_node.childNodes():
                        if child:
                            child_node = self._build_syntax_tree(child)
                            child_node.parent = syntax_node
                            syntax_node.children.append(child_node)
                except Exception as e:
                    logger.debug(f"Error processing children of {pyslang_node.kind}: {e}")

            # Also try to get text content for better symbol extraction
            try:
                if hasattr(pyslang_node, '__str__'):
                    syntax_node.text = str(pyslang_node)[:200]  # Limit text length
            except:
                pass

            return syntax_node

        except Exception as e:
            logger.debug(f"Error building syntax tree: {e}")
            # Return a minimal node
            return SyntaxNode(
                kind="Unknown",
                location=Location(file_path=self.current_file_path, line=0, column=0)
            )

    def _extract_symbols_recursive(self, syntax_node: SyntaxNode, symbols: List[SymbolInfo]) -> None:
        """
        Recursively extract symbols from syntax tree.

        Args:
            syntax_node: Current syntax node
            symbols: List to append symbols to
        """
        try:
            # Try to get a symbol for this node
            symbol = self.semantic_model.get_declared_symbol(syntax_node)
            if symbol:
                symbols.append(symbol)

                # Add definition location
                self.semantic_model.add_symbol_location(
                    symbol, symbol.definition_location, "definition", syntax_node
                )

                # Build hierarchical relationships
                self._build_hierarchy(syntax_node, symbol)

            # Process children
            for child in syntax_node.children:
                self._extract_symbols_recursive(child, symbols)

        except Exception as e:
            logger.debug(f"Error extracting symbols from node: {e}")

    def _build_hierarchy(self, syntax_node: SyntaxNode, symbol: SymbolInfo) -> None:
        """
        Build hierarchical relationships for a symbol.

        Args:
            syntax_node: Syntax node for the symbol
            symbol: The symbol
        """
        try:
            # Find parent symbol
            parent_node = syntax_node.parent
            while parent_node:
                parent_symbol = self.semantic_model.get_declared_symbol(parent_node)
                if parent_symbol:
                    # Add parent-child relationship
                    self.semantic_model.add_parent_child_relationship(parent_symbol, symbol)

                    # Update symbol's parent reference
                    symbol.parent = parent_symbol
                    if parent_symbol.children is None:
                        parent_symbol.children = []
                    parent_symbol.children.append(symbol)

                    # Update qualified name
                    symbol.qualified_name = f"{parent_symbol.qualified_name}.{symbol.name}"
                    break
                parent_node = parent_node.parent

        except Exception as e:
            logger.debug(f"Error building hierarchy: {e}")

    def _analyze_references(self, syntax_root: SyntaxNode, symbols: List[SymbolInfo]) -> None:
        """
        Analyze references in the syntax tree.

        Args:
            syntax_root: Root of syntax tree
            symbols: List of symbols to analyze references for
        """
        try:
            # Create symbol lookup by name
            symbol_by_name = {sym.name: sym for sym in symbols}

            # Walk the syntax tree looking for identifier references
            self._find_references_recursive(syntax_root, symbol_by_name)

        except Exception as e:
            logger.debug(f"Error analyzing references: {e}")

    def _find_references_recursive(self, syntax_node: SyntaxNode, symbol_by_name: Dict[str, SymbolInfo]) -> None:
        """
        Recursively find references in syntax tree.

        Args:
            syntax_node: Current syntax node
            symbol_by_name: Lookup table for symbols by name
        """
        try:
            # Check if this node represents an identifier reference
            if "Identifier" in syntax_node.kind or "Name" in syntax_node.kind:
                # Extract identifier name
                identifier = self._extract_identifier_from_node(syntax_node)
                if identifier and identifier in symbol_by_name:
                    symbol = symbol_by_name[identifier]

                    # Add reference location
                    self.semantic_model.add_symbol_location(
                        symbol, syntax_node.location, "reference", syntax_node
                    )

            # Process children
            for child in syntax_node.children:
                self._find_references_recursive(child, symbol_by_name)

        except Exception as e:
            logger.debug(f"Error finding references: {e}")

    def _extract_identifier_from_node(self, syntax_node: SyntaxNode) -> Optional[str]:
        """
        Extract identifier name from a syntax node.

        Args:
            syntax_node: Syntax node

        Returns:
            Identifier name or None
        """
        try:
            # Try to extract from text
            text = syntax_node.text.strip()
            if text and text.isidentifier():
                return text

            # Try to extract from node text using simple parsing
            words = text.split()
            for word in words:
                if word.isidentifier():
                    return word

            return None
        except:
            return None

    def _get_location_from_pyslang(self, pyslang_node) -> Location:
        """
        Get location from pyslang node.

        Args:
            pyslang_node: pyslang syntax node

        Returns:
            Location object
        """
        try:
            if hasattr(pyslang_node, 'sourceRange'):
                source_range = pyslang_node.sourceRange
                if source_range and hasattr(source_range, 'start'):
                    start = source_range.start
                    if hasattr(start, 'line') and hasattr(start, 'column'):
                        return Location(
                            file_path=self.current_file_path,
                            line=start.line,
                            column=start.column
                        )
        except Exception as e:
            logger.debug(f"Error getting location from pyslang node: {e}")

        # Return default location
        return Location(
            file_path=self.current_file_path,
            line=0,
            column=0
        )

    def clear(self) -> None:
        """Clear all cached data."""
        self.semantic_model.clear()
        self.current_file_path = ""

    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about the extraction process."""
        stats = self.semantic_model.get_statistics()
        stats["extractor_type"] = "enhanced"
        stats["current_file"] = self.current_file_path
        stats["pyslang_available"] = PYSLANG_AVAILABLE
        return stats
