"""
Reference finder for SystemVerilog code.

This module provides functionality to find all references to symbols
in SystemVerilog code using pyslang.
"""

from typing import List, Set, Optional
import logging

try:
    import pyslang
    PYSLANG_AVAILABLE = True
except ImportError:
    PYSLANG_AVAILABLE = False
    pyslang = None

from ..core.types import SymbolInfo, Location

logger = logging.getLogger(__name__)


class ReferenceFinder:
    """
    Finds references to symbols in SystemVerilog code.
    
    This class analyzes SystemVerilog code to find all locations
    where symbols are referenced or used.
    """
    
    def __init__(self):
        """Initialize the reference finder."""
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - reference finding will be limited")
        
        self.target_symbol: Optional[SymbolInfo] = None
        self.references: List[Location] = []
    
    def find_references(self, symbol: SymbolInfo, file_paths: List[str]) -> List[Location]:
        """
        Find all references to a symbol in the given files.
        
        Args:
            symbol: Symbol to find references for
            file_paths: List of files to search in
            
        Returns:
            List of reference locations
        """
        if not PYSLANG_AVAILABLE:
            logger.error("Cannot find references: pyslang not available")
            return []
        
        self.target_symbol = symbol
        self.references = []
        
        try:
            for file_path in file_paths:
                self._find_references_in_file(file_path)
            
            logger.info(f"Found {len(self.references)} references to {symbol.name}")
            return self.references.copy()
            
        except Exception as e:
            logger.error(f"Error finding references: {e}")
            return []
    
    def find_references_in_text(self, symbol: SymbolInfo, text: str, file_path: str = "<text>") -> List[Location]:
        """
        Find references to a symbol in the given text.
        
        Args:
            symbol: Symbol to find references for
            text: SystemVerilog source code
            file_path: Virtual file path for the text
            
        Returns:
            List of reference locations
        """
        if not PYSLANG_AVAILABLE:
            logger.error("Cannot find references: pyslang not available")
            return []
        
        self.target_symbol = symbol
        self.references = []
        
        try:
            self._find_references_in_text(text, file_path)
            
            logger.debug(f"Found {len(self.references)} references to {symbol.name} in text")
            return self.references.copy()
            
        except Exception as e:
            logger.error(f"Error finding references in text: {e}")
            return []
    
    def _find_references_in_file(self, file_path: str) -> None:
        """
        Find references in a specific file.
        
        Args:
            file_path: Path to the file to search
        """
        try:
            # Parse the file
            tree = pyslang.SyntaxTree.fromFile(file_path)
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        logger.debug(f"Parse error in {file_path}: {diag}")
                        return
            
            # Create compilation unit
            compilation = pyslang.Compilation()
            compilation.addSyntaxTree(tree)
            
            # Visit all nodes to find references
            compilation.getRoot().visit(self)
            
        except Exception as e:
            logger.error(f"Error finding references in {file_path}: {e}")
    
    def _find_references_in_text(self, text: str, file_path: str) -> None:
        """
        Find references in text.
        
        Args:
            text: SystemVerilog source code
            file_path: Virtual file path
        """
        try:
            # Parse the text
            tree = pyslang.SyntaxTree.fromText(text, file_path)
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        logger.debug(f"Parse error in {file_path}: {diag}")
                        return
            
            # Create compilation unit
            compilation = pyslang.Compilation()
            compilation.addSyntaxTree(tree)
            
            # Visit all nodes to find references
            compilation.getRoot().visit(self)
            
        except Exception as e:
            logger.error(f"Error finding references in text: {e}")
    
    def __call__(self, obj) -> None:
        """
        Visitor method called for each AST node.
        
        Args:
            obj: AST node
        """
        if not PYSLANG_AVAILABLE or not self.target_symbol:
            return
        
        # Check if this is a reference to our target symbol
        if self._is_reference_to_target(obj):
            location = self._get_reference_location(obj)
            if location:
                self.references.append(location)
    
    def _is_reference_to_target(self, obj) -> bool:
        """
        Check if an AST node is a reference to the target symbol.
        
        Args:
            obj: AST node
            
        Returns:
            True if this is a reference to the target symbol
        """
        if not self.target_symbol:
            return False
        
        # Check for name expressions that reference symbols
        if hasattr(obj, 'symbol') and hasattr(obj, 'name'):
            # This is a simplified check - in practice we'd need more
            # sophisticated symbol resolution
            if str(obj.name) == self.target_symbol.name:
                return True
        
        # Check for hierarchical references
        if hasattr(obj, 'symbol') and obj.symbol:
            if hasattr(obj.symbol, 'name') and str(obj.symbol.name) == self.target_symbol.name:
                return True
        
        return False
    
    def _get_reference_location(self, obj) -> Optional[Location]:
        """
        Get the location of a reference.
        
        Args:
            obj: AST node representing the reference
            
        Returns:
            Reference location or None
        """
        try:
            if hasattr(obj, 'sourceRange'):
                source_range = obj.sourceRange
                if hasattr(source_range, 'start'):
                    loc = source_range.start
                    if hasattr(loc, 'buffer') and hasattr(loc, 'offset'):
                        # TODO: Convert buffer + offset to file path + line/column
                        # This requires access to the source manager
                        return Location(
                            file_path="<unknown>",  # TODO: Get actual file path
                            line=0,  # TODO: Convert to line number
                            column=0,  # TODO: Convert to column number
                            offset=int(loc.offset) if hasattr(loc, 'offset') else None
                        )
            
            # Fallback: try to get location from symbol
            if hasattr(obj, 'location'):
                loc = obj.location
                if hasattr(loc, 'buffer') and hasattr(loc, 'offset'):
                    return Location(
                        file_path="<unknown>",  # TODO: Get actual file path
                        line=0,  # TODO: Convert to line number
                        column=0,  # TODO: Convert to column number
                        offset=int(loc.offset) if hasattr(loc, 'offset') else None
                    )
            
        except Exception as e:
            logger.debug(f"Error getting reference location: {e}")
        
        return None
    
    def find_definition_references(self, symbol: SymbolInfo) -> List[Location]:
        """
        Find references that are definitions (not just uses).
        
        Args:
            symbol: Symbol to find definition references for
            
        Returns:
            List of definition reference locations
        """
        # For now, just return the symbol's definition location
        # In a more complete implementation, this would find all
        # places where the symbol is defined (e.g., forward declarations)
        return [symbol.definition_location]
    
    def find_usage_references(self, symbol: SymbolInfo, file_paths: List[str]) -> List[Location]:
        """
        Find references that are usages (not definitions).
        
        Args:
            symbol: Symbol to find usage references for
            file_paths: List of files to search in
            
        Returns:
            List of usage reference locations
        """
        all_refs = self.find_references(symbol, file_paths)
        
        # Filter out the definition location
        usage_refs = []
        for ref in all_refs:
            if (ref.file_path != symbol.definition_location.file_path or
                ref.line != symbol.definition_location.line or
                ref.column != symbol.definition_location.column):
                usage_refs.append(ref)
        
        return usage_refs
