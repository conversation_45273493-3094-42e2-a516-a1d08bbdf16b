"""
Symbol Analyzer for SystemVerilog.

This module provides complete symbol analysis that covers all
symbol kinds defined in slang, providing full language support.
"""

from typing import List, Dict, Set, Optional, Any
import logging

from ..core.types import SymbolInfo, SymbolKind, Location
from ..core.semantic_model import SemanticModel
from ..core.symbol_kind_mapper import SymbolKindMapper
from ..utils.pyslang_wrapper import PYSLANG_AVAILABLE

if PYSLANG_AVAILABLE:
    import pyslang

logger = logging.getLogger(__name__)


class SymbolAnalyzer:
    """
    Symbol analyzer that handles all SystemVerilog symbol types.

    This analyzer provides complete coverage of slang's SymbolKind definitions,
    enabling full-featured LSP support for SystemVerilog.
    """

    def __init__(self):
        """Initialize the symbol analyzer."""
        self.semantic_model = SemanticModel()
        self.current_file_path = ""
        self.symbol_stack: List[SymbolInfo] = []  # Stack for nested symbols
        self.current_scope: Optional[SymbolInfo] = None

        # Statistics
        self.stats = {
            "total_symbols": 0,
            "symbols_by_kind": {},
            "nested_levels": 0,
            "max_nesting": 0
        }

    def analyze_file(self, file_path: str) -> List[SymbolInfo]:
        """
        Analyze a SystemVerilog file comprehensively.

        Args:
            file_path: Path to the SystemVerilog file

        Returns:
            List of all symbols found in the file
        """
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - returning empty symbol list")
            return []

        try:
            with open(file_path, 'r') as f:
                content = f.read()
            return self.analyze_text(content, file_path)
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return []

    def analyze_text(self, text: str, file_path: str = "<string>") -> List[SymbolInfo]:
        """
        Analyze SystemVerilog text comprehensively.

        Args:
            text: SystemVerilog source code
            file_path: Optional file path for location information

        Returns:
            List of all symbols found in the text
        """
        if not PYSLANG_AVAILABLE:
            logger.warning("pyslang not available - returning empty symbol list")
            return []

        assert pyslang is not None
        self.current_file_path = file_path
        self._reset_state()

        try:
            # Parse the text
            tree = pyslang.SyntaxTree.fromText(text, file_path)
            if tree.diagnostics:
                for diag in tree.diagnostics:
                    if diag.isError():
                        logger.error(f"Parse error in {file_path}: {diag}")

            # Analyze symbols using visitor pattern
            symbols = []
            self._analyze_with_visitor(tree.root, symbols)

            # Update statistics
            self._update_statistics(symbols)

            logger.info(f"Symbol analysis completed for {file_path}: {len(symbols)} symbols")
            return symbols

        except Exception as e:
            logger.error(f"Error in symbol analysis of {file_path}: {e}")
            return []

    def get_semantic_model(self) -> SemanticModel:
        """Get the semantic model with all relationships."""
        return self.semantic_model

    def get_statistics(self) -> Dict[str, Any]:
        """Get analysis statistics."""
        stats = self.semantic_model.get_statistics()
        stats.update(self.stats)
        stats["analyzer_type"] = "complete"
        stats["current_file"] = self.current_file_path
        stats["pyslang_available"] = PYSLANG_AVAILABLE
        return stats

    def _reset_state(self) -> None:
        """Reset analyzer state for new analysis."""
        self.symbol_stack.clear()
        self.current_scope = None
        self.stats = {
            "total_symbols": 0,
            "symbols_by_kind": {},
            "nested_levels": 0,
            "max_nesting": 0
        }

    def _analyze_with_visitor(self, pyslang_root, symbols: List[SymbolInfo]) -> None:
        """
        Analyze symbols using comprehensive visitor pattern.

        Args:
            pyslang_root: Root pyslang syntax node
            symbols: List to append symbols to
        """
        def visit_node(node):
            try:
                kind_str = str(node.kind)
                location = self._get_location_from_pyslang(node)

                # Handle different node types comprehensively
                symbol = self._create_symbol_from_node(node, kind_str, location)
                if symbol:
                    symbols.append(symbol)
                    self._add_to_semantic_model(symbol, location)
                    self._manage_scope_stack(symbol)

            except Exception as e:
                logger.debug(f"Error processing node {kind_str}: {e}")

        # Use pyslang visitor pattern
        try:
            pyslang_root.visit(visit_node)
        except Exception as e:
            logger.error(f"Error during symbol analysis: {e}")

    def _create_symbol_from_node(self, node, kind_str: str, location: Location) -> Optional[SymbolInfo]:
        """
        Create a symbol from a pyslang node based on its kind.

        Args:
            node: pyslang syntax node
            kind_str: String representation of node kind
            location: Location of the node

        Returns:
            SymbolInfo if a symbol should be created, None otherwise
        """
        # Module and interface declarations
        if kind_str == "SyntaxKind.ModuleDeclaration":
            return self._create_module_symbol(node, location)
        elif kind_str == "SyntaxKind.InterfaceDeclaration":
            return self._create_interface_symbol(node, location)
        elif kind_str == "SyntaxKind.ProgramDeclaration":
            return self._create_program_symbol(node, location)
        elif kind_str == "SyntaxKind.PackageDeclaration":
            return self._create_package_symbol(node, location)

        # Class declarations
        elif kind_str == "SyntaxKind.ClassDeclaration":
            return self._create_class_symbol(node, location)

        # Function and task declarations
        elif kind_str == "SyntaxKind.FunctionDeclaration":
            return self._create_function_symbol(node, location)
        elif kind_str == "SyntaxKind.TaskDeclaration":
            return self._create_task_symbol(node, location)

        # Variable and net declarations
        elif kind_str == "SyntaxKind.DataDeclaration":
            return self._create_variable_symbol(node, location)
        elif kind_str == "SyntaxKind.NetDeclaration":
            return self._create_net_symbol(node, location)

        # Parameter declarations
        elif kind_str == "SyntaxKind.ParameterDeclaration":
            return self._create_parameter_symbol(node, location)
        elif kind_str == "SyntaxKind.LocalparamDeclaration":
            return self._create_localparam_symbol(node, location)

        # Port declarations
        elif kind_str == "SyntaxKind.ImplicitAnsiPort":
            return self._create_port_symbol(node, location)
        elif kind_str == "SyntaxKind.ExplicitAnsiPort":
            return self._create_explicit_port_symbol(node, location)

        # Type declarations
        elif kind_str == "SyntaxKind.TypedefDeclaration":
            return self._create_typedef_symbol(node, location)
        elif kind_str == "SyntaxKind.EnumType":
            return self._create_enum_symbol(node, location)
        elif kind_str == "SyntaxKind.StructType":
            return self._create_struct_symbol(node, location)
        elif kind_str == "SyntaxKind.UnionType":
            return self._create_union_symbol(node, location)

        # Generate blocks
        elif kind_str == "SyntaxKind.IfGenerate":
            return self._create_generate_block_symbol(node, location)
        elif kind_str == "SyntaxKind.LoopGenerate":
            return self._create_generate_loop_symbol(node, location)

        # Procedural blocks
        elif kind_str == "SyntaxKind.AlwaysBlock":
            return self._create_always_block_symbol(node, location)
        elif kind_str == "SyntaxKind.InitialBlock":
            return self._create_initial_block_symbol(node, location)
        elif kind_str == "SyntaxKind.FinalBlock":
            return self._create_final_block_symbol(node, location)

        # Assertion and property declarations
        elif kind_str == "SyntaxKind.PropertyDeclaration":
            return self._create_property_symbol(node, location)
        elif kind_str == "SyntaxKind.SequenceDeclaration":
            return self._create_sequence_symbol(node, location)

        # Covergroup declarations
        elif kind_str == "SyntaxKind.CovergroupDeclaration":
            return self._create_covergroup_symbol(node, location)

        # Clocking blocks
        elif kind_str == "SyntaxKind.ClockingDeclaration":
            return self._create_clocking_symbol(node, location)

        # Modport declarations
        elif kind_str == "SyntaxKind.ModportDeclaration":
            return self._create_modport_symbol(node, location)

        # Instance declarations
        elif kind_str == "SyntaxKind.HierarchyInstantiation":
            return self._create_instance_symbol(node, location)

        return None

    def _create_module_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create a module symbol."""
        name = self._extract_identifier_from_node(node, "module")
        if name:
            return SymbolInfo(
                name=name,
                kind=SymbolKind.INSTANCE,
                definition_location=location,
                qualified_name=self._build_qualified_name(name)
            )
        return None

    def _create_interface_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create an interface symbol."""
        name = self._extract_identifier_from_node(node, "interface")
        if name:
            return SymbolInfo(
                name=name,
                kind=SymbolKind.INSTANCE,  # Interface instances
                definition_location=location,
                qualified_name=self._build_qualified_name(name)
            )
        return None

    def _create_class_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create a class symbol."""
        name = self._extract_identifier_from_node(node, "class")
        if name:
            return SymbolInfo(
                name=name,
                kind=SymbolKind.CLASS_TYPE,
                definition_location=location,
                qualified_name=self._build_qualified_name(name)
            )
        return None

    def _create_function_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create a function symbol."""
        name = self._extract_identifier_from_node(node, "function")
        if name:
            return SymbolInfo(
                name=name,
                kind=SymbolKind.SUBROUTINE,
                definition_location=location,
                qualified_name=self._build_qualified_name(name)
            )
        return None

    def _create_variable_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create a variable symbol."""
        names = self._extract_variable_names_from_node(node)
        if names:
            # Return the first variable (could be extended to return all)
            return SymbolInfo(
                name=names[0],
                kind=SymbolKind.VARIABLE,
                definition_location=location,
                qualified_name=self._build_qualified_name(names[0])
            )
        return None

    def _create_port_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create a port symbol."""
        name = self._extract_port_name_from_node(node)
        if name:
            return SymbolInfo(
                name=name,
                kind=SymbolKind.PORT,
                definition_location=location,
                qualified_name=self._build_qualified_name(name)
            )
        return None

    def _extract_identifier_from_node(self, node, keyword: str) -> Optional[str]:
        """Extract identifier following a keyword from a node."""
        try:
            text = str(node).strip()
            if keyword in text:
                parts = text.split()

                # Find the keyword position
                keyword_index = -1
                for i, part in enumerate(parts):
                    if part == keyword:
                        keyword_index = i
                        break

                if keyword_index == -1:
                    return None

                # For functions, skip qualifiers like 'automatic', 'static', return type
                if keyword == "function":
                    return self._extract_function_name(parts, keyword_index)
                elif keyword == "task":
                    return self._extract_task_name(parts, keyword_index)
                else:
                    # For other keywords, take the next identifier
                    for i in range(keyword_index + 1, len(parts)):
                        name = parts[i].split('(')[0].split(';')[0].strip()
                        if name.isidentifier():
                            return name
            return None
        except Exception as e:
            logger.debug(f"Error extracting identifier for {keyword}: {e}")
            return None

    def _extract_function_name(self, parts: List[str], keyword_index: int) -> Optional[str]:
        """Extract function name, skipping qualifiers and return type."""
        try:
            # Function syntax: [qualifiers] function [return_type] name(...)
            # Qualifiers: automatic, static
            # Return type: void, logic, int, etc.

            qualifiers = {'automatic', 'static'}
            type_keywords = {'void', 'logic', 'int', 'bit', 'byte', 'shortint', 'longint', 'real', 'string'}

            i = keyword_index + 1
            while i < len(parts):
                part = parts[i].split('(')[0].split(';')[0].strip()

                # Skip qualifiers
                if part in qualifiers:
                    i += 1
                    continue

                # Skip return type (but be careful with user-defined types)
                if part in type_keywords:
                    i += 1
                    continue

                # Skip array dimensions like [7:0]
                if part.startswith('[') or part.endswith(']'):
                    i += 1
                    continue

                # This should be the function name
                if part.isidentifier():
                    return part

                i += 1

            return None
        except Exception as e:
            logger.debug(f"Error extracting function name: {e}")
            return None

    def _extract_task_name(self, parts: List[str], keyword_index: int) -> Optional[str]:
        """Extract task name, skipping qualifiers."""
        try:
            # Task syntax: [qualifiers] task name(...)
            qualifiers = {'automatic', 'static'}

            i = keyword_index + 1
            while i < len(parts):
                part = parts[i].split('(')[0].split(';')[0].strip()

                # Skip qualifiers
                if part in qualifiers:
                    i += 1
                    continue

                # This should be the task name
                if part.isidentifier():
                    return part

                i += 1

            return None
        except Exception as e:
            logger.debug(f"Error extracting task name: {e}")
            return None

    def _extract_variable_names_from_node(self, node) -> List[str]:
        """Extract variable names from a data declaration node."""
        try:
            text = str(node).strip()
            # Simple parsing for variable declarations
            if any(keyword in text for keyword in ['logic', 'reg', 'wire', 'int', 'bit']):
                words = text.replace(';', '').split()
                names = []
                for word in words:
                    clean_word = word.strip(',();')
                    if (clean_word.isidentifier() and
                        clean_word not in ['logic', 'reg', 'wire', 'int', 'bit', 'input', 'output', 'inout']):
                        names.append(clean_word)
                return names
            return []
        except Exception as e:
            logger.debug(f"Error extracting variable names: {e}")
            return []

    def _extract_port_name_from_node(self, node) -> Optional[str]:
        """Extract port name from a port declaration node."""
        try:
            text = str(node).strip()
            words = text.split()
            for word in reversed(words):
                clean_word = word.strip(',();')
                if clean_word.isidentifier() and clean_word not in ['input', 'output', 'inout', 'logic', 'wire']:
                    return clean_word
            return None
        except Exception as e:
            logger.debug(f"Error extracting port name: {e}")
            return None

    def _build_qualified_name(self, name: str) -> str:
        """Build qualified name based on current scope."""
        if self.current_scope:
            return f"{self.current_scope.qualified_name}.{name}"
        return name

    def _get_location_from_pyslang(self, pyslang_node) -> Location:
        """Get location from pyslang node."""
        try:
            if hasattr(pyslang_node, 'sourceRange'):
                source_range = pyslang_node.sourceRange
                if source_range and hasattr(source_range, 'start'):
                    start = source_range.start
                    if hasattr(start, 'line') and hasattr(start, 'column'):
                        return Location(
                            file_path=self.current_file_path,
                            line=start.line,
                            column=start.column
                        )
        except Exception as e:
            logger.debug(f"Error getting location from pyslang node: {e}")

        return Location(file_path=self.current_file_path, line=0, column=0)

    def _add_to_semantic_model(self, symbol: SymbolInfo, location: Location) -> None:
        """Add symbol to semantic model."""
        self.semantic_model.add_symbol_location(symbol, location, "definition")

        # Add hierarchical relationship if we have a current scope
        if self.current_scope:
            self.semantic_model.add_parent_child_relationship(self.current_scope, symbol)
            symbol.parent = self.current_scope
            if self.current_scope.children is None:
                self.current_scope.children = []
            self.current_scope.children.append(symbol)

    def _manage_scope_stack(self, symbol: SymbolInfo) -> None:
        """Manage the scope stack for nested symbols."""
        # Check if this symbol can contain other symbols
        if SymbolKindMapper.is_container(symbol.kind):
            self.symbol_stack.append(symbol)
            self.current_scope = symbol
            self.stats["nested_levels"] += 1
            self.stats["max_nesting"] = max(self.stats["max_nesting"], len(self.symbol_stack))

    def _update_statistics(self, symbols: List[SymbolInfo]) -> None:
        """Update analysis statistics."""
        self.stats["total_symbols"] = len(symbols)

        # Count symbols by kind
        for symbol in symbols:
            kind_name = symbol.kind.value
            self.stats["symbols_by_kind"][kind_name] = self.stats["symbols_by_kind"].get(kind_name, 0) + 1

    # Placeholder methods for other symbol types (to be implemented as needed)
    def _create_program_symbol(self, node, location): return None
    def _create_package_symbol(self, node, location): return None
    def _create_task_symbol(self, node, location): return None
    def _create_net_symbol(self, node, location): return None
    def _create_parameter_symbol(self, node, location): return None
    def _create_localparam_symbol(self, node, location): return None
    def _create_explicit_port_symbol(self, node, location): return None
    def _create_typedef_symbol(self, node, location): return None
    def _create_enum_symbol(self, node, location): return None
    def _create_struct_symbol(self, node, location): return None
    def _create_union_symbol(self, node, location): return None
    def _create_generate_block_symbol(self, node, location): return None
    def _create_generate_loop_symbol(self, node, location): return None
    def _create_always_block_symbol(self, node, location): return None
    def _create_initial_block_symbol(self, node, location): return None
    def _create_final_block_symbol(self, node, location): return None
    def _create_property_symbol(self, node, location): return None
    def _create_sequence_symbol(self, node, location): return None
    def _create_covergroup_symbol(self, node, location): return None
    def _create_clocking_symbol(self, node, location): return None
    def _create_modport_symbol(self, node, location): return None
    def _create_instance_symbol(self, node, location): return None
