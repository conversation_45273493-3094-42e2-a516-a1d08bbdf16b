#!/usr/bin/env python3
"""
Comparison test for different symbol analyzers.
"""

import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from sv_lsp.analysis.symbol_extractor import SymbolExtractor
    from sv_lsp.analysis.enhanced_symbol_extractor import EnhancedSymbolExtractor
    from sv_lsp.analysis.comprehensive_symbol_analyzer import ComprehensiveSymbolAnalyzer
    from sv_lsp.core.types import SymbolKind
    
    print("🔬 Symbol Analyzer Comparison Test")
    print("=" * 60)
    
    # Test SystemVerilog code with various constructs
    test_code = """
// Test code for analyzer comparison
package test_pkg;
    typedef enum logic [1:0] {
        IDLE = 2'b00,
        ACTIVE = 2'b01,
        DONE = 2'b10
    } state_t;
    
    parameter int DATA_WIDTH = 32;
endpackage

interface simple_if(input logic clk);
    logic [7:0] data;
    logic valid;
    
    modport master (output data, valid);
    modport slave (input data, valid);
endinterface

class transaction;
    rand bit [31:0] addr;
    rand bit [31:0] data;
    
    function new();
        addr = 0;
        data = 0;
    endfunction
    
    virtual task execute();
        $display("Executing transaction");
    endtask
endclass

module test_module #(
    parameter int WIDTH = 8
) (
    input logic clk,
    input logic rst_n,
    simple_if.slave bus,
    output logic [WIDTH-1:0] result
);
    
    // Internal signals
    logic [WIDTH-1:0] counter;
    state_t current_state;
    
    // Function declaration
    function automatic logic [WIDTH-1:0] increment(
        input logic [WIDTH-1:0] value
    );
        return value + 1;
    endfunction
    
    // Task declaration
    task automatic reset_counter();
        counter <= 0;
        current_state <= test_pkg::IDLE;
    endtask
    
    // Always block
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            reset_counter();
        end else begin
            counter <= increment(counter);
        end
    end
    
    assign result = counter;
    
    // Generate block
    genvar i;
    generate
        for (i = 0; i < 4; i++) begin : gen_regs
            logic [7:0] reg_array;
        end
    endgenerate
    
    // Assertion
    property counter_bounds;
        @(posedge clk) disable iff (!rst_n)
        counter < (1 << WIDTH);
    endproperty
    
    assert property (counter_bounds);
    
endmodule
"""
    
    print("📝 Test Code Analysis:")
    print(f"  Lines of code: {len(test_code.splitlines())}")
    print(f"  Characters: {len(test_code)}")
    print("  Contains: package, interface, class, module, functions, tasks, generate blocks, assertions")
    print()
    
    # Test each analyzer
    analyzers = [
        ("SymbolExtractor", SymbolExtractor()),
        ("EnhancedSymbolExtractor", EnhancedSymbolExtractor()),
        ("ComprehensiveSymbolAnalyzer", ComprehensiveSymbolAnalyzer())
    ]
    
    results = {}
    
    for name, analyzer in analyzers:
        print(f"🔍 Testing {name}:")
        
        # Measure performance
        start_time = time.time()
        
        try:
            if hasattr(analyzer, 'analyze_text'):
                symbols = analyzer.analyze_text(test_code, "test_comparison.sv")
            else:
                symbols = analyzer.extract_from_text(test_code, "test_comparison.sv")
            
            end_time = time.time()
            duration = (end_time - start_time) * 1000  # Convert to milliseconds
            
            # Analyze results
            symbol_types = {}
            for symbol in symbols:
                kind_name = symbol.kind.value
                if kind_name not in symbol_types:
                    symbol_types[kind_name] = []
                symbol_types[kind_name].append(symbol.name)
            
            # Get additional capabilities
            capabilities = {
                "semantic_model": hasattr(analyzer, 'get_semantic_model'),
                "statistics": hasattr(analyzer, 'get_statistics'),
                "position_lookup": hasattr(analyzer, 'find_symbol_at_position'),
                "references": hasattr(analyzer, 'get_symbol_references')
            }
            
            results[name] = {
                "symbols": symbols,
                "symbol_types": symbol_types,
                "duration": duration,
                "capabilities": capabilities
            }
            
            print(f"  ✅ Completed in {duration:.2f}ms")
            print(f"  📊 Found {len(symbols)} symbols")
            print(f"  🏷️  Symbol types: {len(symbol_types)}")
            
            for kind_name, names in sorted(symbol_types.items()):
                print(f"    {kind_name}: {len(names)} symbols")
                if len(names) <= 3:
                    print(f"      → {', '.join(names)}")
                else:
                    print(f"      → {', '.join(names[:3])} ... (+{len(names)-3} more)")
            
            print(f"  🔧 Capabilities:")
            for cap_name, has_cap in capabilities.items():
                status = "✅" if has_cap else "❌"
                print(f"    {status} {cap_name}")
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            results[name] = {"error": str(e)}
        
        print()
    
    # Comparison summary
    print("📊 Comparison Summary:")
    print("-" * 60)
    
    # Performance comparison
    print("⚡ Performance:")
    for name, result in results.items():
        if "duration" in result:
            print(f"  {name}: {result['duration']:.2f}ms")
    print()
    
    # Symbol count comparison
    print("🔢 Symbol Count:")
    for name, result in results.items():
        if "symbols" in result:
            print(f"  {name}: {len(result['symbols'])} symbols")
    print()
    
    # Symbol type coverage comparison
    print("🏷️  Symbol Type Coverage:")
    all_types = set()
    for result in results.values():
        if "symbol_types" in result:
            all_types.update(result["symbol_types"].keys())
    
    for symbol_type in sorted(all_types):
        print(f"  {symbol_type}:")
        for name, result in results.items():
            if "symbol_types" in result:
                count = len(result["symbol_types"].get(symbol_type, []))
                status = "✅" if count > 0 else "❌"
                print(f"    {name}: {status} {count}")
    print()
    
    # Capability comparison
    print("🔧 Capability Comparison:")
    all_capabilities = set()
    for result in results.values():
        if "capabilities" in result:
            all_capabilities.update(result["capabilities"].keys())
    
    for capability in sorted(all_capabilities):
        print(f"  {capability}:")
        for name, result in results.items():
            if "capabilities" in result:
                has_cap = result["capabilities"].get(capability, False)
                status = "✅" if has_cap else "❌"
                print(f"    {name}: {status}")
    print()
    
    # Recommendations
    print("💡 Recommendations:")
    print("  📌 For simple projects: SymbolExtractor")
    print("     - Fastest performance")
    print("     - Minimal memory usage")
    print("     - Basic symbol extraction")
    print()
    print("  📌 For medium projects: EnhancedSymbolExtractor")
    print("     - Good balance of features and performance")
    print("     - Semantic model support")
    print("     - Reference tracking")
    print()
    print("  📌 For complex projects: ComprehensiveSymbolAnalyzer")
    print("     - Complete SystemVerilog support")
    print("     - All 101 SymbolKind types")
    print("     - Advanced semantic analysis")
    print()
    
    # Detailed analysis for ComprehensiveSymbolAnalyzer
    if "ComprehensiveSymbolAnalyzer" in results and "symbols" in results["ComprehensiveSymbolAnalyzer"]:
        print("🔬 Detailed Analysis (ComprehensiveSymbolAnalyzer):")
        analyzer = analyzers[2][1]  # ComprehensiveSymbolAnalyzer
        
        if hasattr(analyzer, 'get_statistics'):
            stats = analyzer.get_statistics()
            print("  📈 Statistics:")
            for key, value in stats.items():
                print(f"    {key}: {value}")
        
        if hasattr(analyzer, 'get_semantic_model'):
            semantic_model = analyzer.get_semantic_model()
            sm_stats = semantic_model.get_statistics()
            print("  🧠 Semantic Model:")
            for key, value in sm_stats.items():
                print(f"    {key}: {value}")
    
    print()
    print("🎉 Analyzer Comparison Complete!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
