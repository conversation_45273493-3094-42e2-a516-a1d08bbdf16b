# SystemVerilog LSP Server API Documentation

This document describes the API and architecture of the SystemVerilog Language Server Protocol implementation.

## Overview

The SystemVerilog LSP server provides language support for SystemVerilog files through the Language Server Protocol. It is built on top of pyslang for accurate parsing and semantic analysis.

## Core Components

### SymbolManager

The `SymbolManager` class is responsible for managing symbol tables and providing efficient symbol lookup capabilities.

#### Key Methods

```python
class SymbolManager:
    def add_file(self, file_path: str, force_rebuild: bool = False) -> bool:
        """Add or update a file in the symbol table."""
    
    def find_symbol(self, name: str, qualified: bool = False) -> Optional[SymbolInfo]:
        """Find a symbol by name."""
    
    def find_symbols_by_kind(self, kind: SymbolKind) -> List[SymbolInfo]:
        """Find all symbols of a specific kind."""
    
    def search_symbols(self, query: str, limit: int = 50) -> List[SymbolInfo]:
        """Search for symbols using a query string."""
```

#### Performance Characteristics

- Symbol indexing: ~1.3ms per symbol
- Symbol search: <1ms for most queries
- Memory usage: ~500 bytes per symbol

### PositionMapper

The `PositionMapper` class handles mapping between source code positions and symbols.

#### Key Methods

```python
class PositionMapper:
    def find_symbol_at_position(self, file_path: str, line: int, column: int) -> Optional[SymbolInfo]:
        """Find the symbol at the given position."""
    
    def find_symbols_on_line(self, file_path: str, line: int) -> List[SymbolInfo]:
        """Find all symbols on a specific line."""
    
    def update_file_incrementally(self, file_path: str, changes: List[Dict]) -> None:
        """Update position indices incrementally based on file changes."""
```

### WorkspaceManager

The `WorkspaceManager` class manages SystemVerilog workspaces and project files.

#### Key Methods

```python
class WorkspaceManager:
    def discover_files(self, recursive: bool = True) -> List[str]:
        """Discover SystemVerilog files in the workspace."""
    
    def resolve_include_path(self, include_file: str, current_file: str) -> Optional[str]:
        """Resolve an include file path."""
    
    def get_compilation_order(self) -> List[str]:
        """Get files in compilation order based on dependencies."""
```

## LSP Protocol Implementation

### Supported LSP Features

| Feature | Status | Description |
|---------|--------|-------------|
| textDocument/definition | ✅ | Go to definition |
| textDocument/references | ✅ | Find all references |
| textDocument/hover | ✅ | Hover information |
| textDocument/documentSymbol | ✅ | Document outline |
| workspace/symbol | ✅ | Workspace symbol search |
| textDocument/didOpen | ✅ | Document open notification |
| textDocument/didChange | ✅ | Document change notification |
| textDocument/didSave | ✅ | Document save notification |
| textDocument/didClose | ✅ | Document close notification |

### LSP Message Examples

#### Go to Definition Request

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "textDocument/definition",
  "params": {
    "textDocument": {
      "uri": "file:///path/to/file.sv"
    },
    "position": {
      "line": 10,
      "character": 15
    }
  }
}
```

#### Go to Definition Response

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "uri": "file:///path/to/file.sv",
    "range": {
      "start": {"line": 5, "character": 10},
      "end": {"line": 5, "character": 20}
    }
  }
}
```

## Data Types

### SymbolInfo

Represents information about a SystemVerilog symbol.

```python
@dataclass
class SymbolInfo:
    name: str                           # Symbol name
    kind: SymbolKind                   # Symbol kind (module, variable, etc.)
    definition_location: Location      # Where the symbol is defined
    qualified_name: str               # Fully qualified name
    parent: Optional[SymbolInfo]      # Parent symbol
    children: List[SymbolInfo]        # Child symbols
    references: List[Location]        # Reference locations
    type_info: Optional[str]          # Type information
    documentation: Optional[str]      # Documentation string
```

### Location

Represents a location in source code.

```python
@dataclass(frozen=True)
class Location:
    file_path: str      # File path
    line: int          # Line number (0-based)
    column: int        # Column number (0-based)
    offset: Optional[int] = None  # Byte offset
```

### SymbolKind

Enumeration of SystemVerilog symbol kinds.

```python
class SymbolKind(Enum):
    MODULE = "module"
    INTERFACE = "interface"
    PACKAGE = "package"
    CLASS = "class"
    FUNCTION = "function"
    TASK = "task"
    VARIABLE = "variable"
    PARAMETER = "parameter"
    PORT = "port"
    TYPEDEF = "typedef"
    ENUM = "enum"
    STRUCT = "struct"
    UNION = "union"
    MODPORT = "modport"
    # ... and more
```

## Configuration

### Workspace Configuration

```json
{
  "sv-lsp": {
    "includePaths": ["./include", "./src"],
    "defines": {"DEBUG": "1", "SYNTHESIS": "0"},
    "maxErrors": 100,
    "enableDiagnostics": true,
    "fileExtensions": [".sv", ".svh", ".v", ".vh"]
  }
}
```

### Server Initialization

```python
from sv_lsp.lsp.server import SVLanguageServer

# Create server
server = SVLanguageServer(max_workers=4)

# Start on stdin/stdout (standard LSP mode)
await server.start_io()

# Or start on TCP (for debugging)
await server.start_tcp("localhost", 8080)
```

## Performance Monitoring

The server includes built-in performance monitoring capabilities.

### Using Performance Monitoring

```python
from sv_lsp.utils.performance import performance_monitor, Timer, timed

# Enable monitoring
performance_monitor.enable()

# Use timer context manager
with Timer("operation_name"):
    # Your code here
    pass

# Use decorator
@timed("function_name")
def my_function():
    # Your code here
    pass

# Get performance report
report = performance_monitor.get_report()
```

### Performance Metrics

The server tracks the following metrics:

- Symbol indexing time
- Symbol search time
- Position lookup time
- File parsing time
- LSP request processing time

## Error Handling

### Common Error Codes

| Code | Description |
|------|-------------|
| -32601 | Method not found |
| -32602 | Invalid params |
| -32603 | Internal error |

### Error Response Format

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32603,
    "message": "Internal error: Failed to parse file"
  }
}
```

## Extending the Server

### Adding New Symbol Kinds

1. Add to `SymbolKind` enum
2. Update `SymbolExtractor._map_symbol_kind()`
3. Update LSP symbol kind mapping in `SymbolInfo._symbol_kind_to_lsp()`

### Adding New LSP Features

1. Add handler method to `LSPHandlers`
2. Register handler in `SVLanguageServer.request_handlers`
3. Update server capabilities in `get_capabilities()`

## Debugging

### Enable Debug Logging

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Performance Profiling

```python
from sv_lsp.utils.performance import benchmark_function

# Benchmark a function
results = benchmark_function(my_function, arg1, arg2, iterations=100)
print(f"Average time: {results['average_time']:.4f}s")
```

### Memory Usage

The server is designed to be memory-efficient:

- Symbols are stored with minimal overhead
- Indices use efficient data structures
- Caching is used to avoid redundant parsing

## Limitations

- Currently supports SystemVerilog IEEE 1800-2017
- Requires pyslang for full functionality
- Some advanced SystemVerilog features may not be fully supported
- Performance may degrade with very large files (>10k lines)

## Future Enhancements

- Incremental parsing for better performance
- More sophisticated type analysis
- Code completion support
- Refactoring operations
- Integration with simulation tools
