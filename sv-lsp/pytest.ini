[tool:pytest]
# Pytest configuration for SystemVerilog LSP

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --strict-markers
    --strict-config
    --tb=short
    --disable-warnings
    -ra

# Markers for test categorization
markers =
    unit: Unit tests
    integration: Integration tests
    analysis: Symbol analysis tests
    lsp: LSP protocol tests
    performance: Performance tests
    slow: Slow tests that take more than 1 second
    requires_pyslang: Tests that require pyslang to be installed
    
# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# addopts = -n auto

# Coverage options (when using pytest-cov)
# addopts = --cov=sv_lsp --cov-report=term-missing

# Logging
log_cli = false
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pyslang.*
