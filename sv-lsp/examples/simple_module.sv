// Simple SystemVerilog module for testing LSP functionality
module counter(
    input  logic        clk,
    input  logic        reset_n,
    input  logic        enable,
    output logic [7:0]  count,
    output logic        overflow
);

    // Internal signals
    logic [7:0] next_count;
    logic       will_overflow;
    
    // Parameters
    parameter int MAX_COUNT = 255;
    
    // Combinational logic
    always_comb begin
        next_count = count + 1;
        will_overflow = (count == MAX_COUNT);
        overflow = will_overflow && enable;
    end
    
    // Sequential logic
    always_ff @(posedge clk or negedge reset_n) begin
        if (!reset_n) begin
            count <= 8'b0;
        end else if (enable) begin
            if (will_overflow) begin
                count <= 8'b0;
            end else begin
                count <= next_count;
            end
        end
    end

endmodule

// Test interface
interface counter_if(input logic clk);
    logic        reset_n;
    logic        enable;
    logic [7:0]  count;
    logic        overflow;
    
    modport dut (
        input  reset_n,
        input  enable,
        output count,
        output overflow
    );
    
    modport tb (
        output reset_n,
        output enable,
        input  count,
        input  overflow
    );
endinterface
