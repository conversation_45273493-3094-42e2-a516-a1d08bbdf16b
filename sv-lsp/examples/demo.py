#!/usr/bin/env python3
"""
Demo script for SystemVerilog LSP functionality.

This script demonstrates the basic capabilities of the SystemVerilog LSP server
including symbol extraction, type resolution, and position mapping.
"""

import sys
import os
import logging
from pathlib import Path

# Enable info logging for symbol extraction
logging.basicConfig(level=logging.INFO)

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from sv_lsp.core.types import SymbolKind, Location, WorkspaceConfig
from sv_lsp.core.symbol_manager import SymbolManager
from sv_lsp.core.position_mapper import PositionMapper
from sv_lsp.core.workspace import WorkspaceManager
from sv_lsp.utils.helpers import extract_identifier_at_position, is_systemverilog_file

# Try to import pyslang components
try:
    from sv_lsp.utils.pyslang_wrapper import PyslangWrapper
    from sv_lsp.analysis.symbol_extractor import SymbolExtractor
    PYSLANG_AVAILABLE = True
except ImportError as e:
    print(f"Warning: pyslang not available - {e}")
    PYSLANG_AVAILABLE = False


def demo_basic_types():
    """Demonstrate basic type functionality."""
    print("=== Basic Types Demo ===")

    # Create a location
    location = Location(
        file_path="examples/simple_module.sv",
        line=5,
        column=10
    )
    print(f"Location: {location}")
    print(f"LSP Position: {location.to_lsp_position()}")

    # Create a symbol
    from sv_lsp.core.types import SymbolInfo
    symbol = SymbolInfo(
        name="counter",
        kind=SymbolKind.VARIABLE,
        definition_location=location,
        type_info="logic [7:0]"
    )
    print(f"Symbol: {symbol.name} ({symbol.kind.value})")
    print(f"Qualified name: {symbol.qualified_name}")
    print(f"LSP Symbol Kind: {symbol._symbol_kind_to_lsp()}")
    print()


def demo_symbol_manager():
    """Demonstrate symbol manager functionality."""
    print("=== Symbol Manager Demo ===")

    config = WorkspaceConfig()
    manager = SymbolManager(config)

    # Add a file (this would normally parse with pyslang)
    success = manager.add_file("examples/simple_module.sv")
    print(f"Added file: {success}")

    # Get statistics
    stats = manager.get_statistics()
    print(f"Statistics: {stats}")
    print()


def demo_workspace_manager():
    """Demonstrate workspace manager functionality."""
    print("=== Workspace Manager Demo ===")

    # Create workspace manager
    workspace = WorkspaceManager(root_path="examples")

    # Discover files
    files = workspace.discover_files()
    print(f"Discovered files: {files}")

    # Check file types
    for file_path in files:
        is_sv = is_systemverilog_file(file_path)
        print(f"  {file_path}: SystemVerilog = {is_sv}")

    # Get statistics
    stats = workspace.get_statistics()
    print(f"Workspace statistics: {stats}")
    print()


def demo_helper_functions():
    """Demonstrate utility helper functions."""
    print("=== Helper Functions Demo ===")

    # Test identifier extraction
    test_code = """\
module test_module(input logic clk, output logic [7:0] data);"""

    # Extract identifier at different positions
    positions = [
        (0, 7),   # "test_module"
        (0, 20),  # "input"
        (0, 26),  # "logic"
        (0, 32),  # "clk"
        (0, 45),  # "logic"
        (0, 58),  # "data"
    ]

    for line, col in positions:
        identifier = extract_identifier_at_position(test_code, line, col)
        print(f"Position ({line}, {col}): '{identifier}'")

    print()


def demo_pyslang_integration():
    """Demonstrate pyslang integration if available."""
    print("=== Pyslang Integration Demo ===")

    if not PYSLANG_AVAILABLE:
        print("Pyslang not available - skipping integration demo")
        print("To enable pyslang features, install with: pip install pyslang")
        return

    try:
        # Create pyslang wrapper
        wrapper = PyslangWrapper()

        # Get version info
        version_info = wrapper.get_version_info()
        print(f"Pyslang info: {version_info}")

        # Test syntax validation
        test_code = """
        module simple_test;
            logic [7:0] data;
            logic       valid;

            initial begin
                data = 8'h42;
                valid = 1'b1;
            end
        endmodule
        """
        test_code = '''
// Simple SystemVerilog module for testing LSP functionality
module counter(
    input  logic        clk,
    input  logic        reset_n,
    input  logic        enable,
    output logic [7:0]  count,
    output logic        overflow
);

    // Internal signals
    logic [7:0] next_count;
    logic       will_overflow;

    // Parameters
    parameter int MAX_COUNT = 255;

    // Combinational logic
    always_comb begin
        next_count = count + 1;
        will_overflow = (count == MAX_COUNT);
        overflow = will_overflow && enable;
    end

    // Sequential logic
    always_ff @(posedge clk or negedge reset_n) begin
        if (!reset_n) begin
            count <= 8'b0;
        end else if (enable) begin
            if (will_overflow) begin
                count <= 8'b0;
            end else begin
                count <= next_count;
            end
        end
    end

endmodule

// Test interface
interface counter_if(input logic clk);
    logic        reset_n;
    logic        enable;
    logic [7:0]  count;
    logic        overflow;

    modport dut (
        input  reset_n,
        input  enable,
        output count,
        output overflow
    );

    modport tb (
        output reset_n,
        output enable,
        input  count,
        input  overflow
    );
endinterface

'''

        is_valid, errors = wrapper.validate_syntax(test_code, "test.sv")
        print(f"Syntax validation: valid = {is_valid}")
        if errors:
            print(f"Errors: {errors}")

        # Test symbol extraction
        symbols = wrapper.extract_symbols_from_text(test_code, "test.sv")
        print(f"Extracted {len(symbols)} symbols:")
        for symbol in symbols:
            print(f"  - {symbol.name} ({symbol.kind.value})")

    except Exception as e:
        print(f"Error in pyslang demo: {e}")

    print()


def demo_lsp_capabilities():
    """Demonstrate LSP server capabilities."""
    print("=== LSP Capabilities Demo ===")

    from sv_lsp.lsp.server import SVLanguageServer

    # Create server
    server = SVLanguageServer()

    # Get capabilities
    capabilities = server.get_capabilities()
    print("Server capabilities:")
    for key, value in capabilities.items():
        print(f"  {key}: {value}")

    print()


def main():
    """Main demo function."""
    print("SystemVerilog LSP Server Demo")
    print("=" * 50)
    print()

    # Run all demos
    demo_basic_types()
    demo_symbol_manager()
    demo_workspace_manager()
    demo_helper_functions()
    demo_pyslang_integration()
    demo_lsp_capabilities()

    print("Demo completed!")
    print()
    print("Next steps:")
    print("1. Install pyslang: pip install pyslang")
    print("2. Run tests: pytest tests/")
    print("3. Start LSP server: python -m sv_lsp.main")
    print("4. Integrate with your editor (VS Code, Vim, etc.)")


if __name__ == "__main__":
    main()
