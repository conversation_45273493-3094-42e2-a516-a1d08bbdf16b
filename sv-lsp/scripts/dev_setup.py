#!/usr/bin/env python3
"""
Development setup script for SystemVerilog LSP.

This script helps set up the development environment and verify
that all dependencies are properly installed.
"""

import subprocess
import sys
from pathlib import Path


def run_command(cmd: list, description: str) -> bool:
    """Run a command and return success status."""
    print(f"Running: {description}")
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"  Command: {' '.join(cmd)}")
        print(f"  Return code: {e.returncode}")
        if e.stdout:
            print(f"  Stdout: {e.stdout}")
        if e.stderr:
            print(f"  Stderr: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"✗ {description} failed: Command not found")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    print("Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("  Requires Python 3.8 or higher")
        return False


def check_pyslang():
    """Check if pyslang is available."""
    print("Checking pyslang availability...")
    try:
        import pyslang
        print(f"✓ pyslang is available")
        return True
    except ImportError:
        print("✗ pyslang is not available")
        print("  You may need to install pyslang: pip install pyslang")
        return False


def main():
    """Main setup function."""
    print("SystemVerilog LSP Development Setup")
    print("=" * 40)
    
    # Change to project directory
    project_root = Path(__file__).parent.parent
    print(f"Project root: {project_root.absolute()}")
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    print()
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✓ Running in virtual environment")
    else:
        print("⚠ Not running in virtual environment (recommended)")
    
    print()
    
    # Install package in development mode
    if not run_command(
        [sys.executable, "-m", "pip", "install", "-e", ".[dev]"],
        "Installing package in development mode"
    ):
        success = False
    
    print()
    
    # Check pyslang
    if not check_pyslang():
        success = False
    
    print()
    
    # Run basic tests
    if not run_command(
        [sys.executable, "-m", "pytest", "tests/", "-v"],
        "Running basic tests"
    ):
        success = False
    
    print()
    
    # Run type checking
    if not run_command(
        [sys.executable, "-m", "mypy", "src/sv_lsp", "--ignore-missing-imports"],
        "Running type checking"
    ):
        print("⚠ Type checking failed (this is expected for incomplete implementation)")
    
    print()
    
    # Run linting
    if not run_command(
        [sys.executable, "-m", "flake8", "src", "tests", "--max-line-length=88", "--extend-ignore=E203"],
        "Running linting"
    ):
        print("⚠ Linting failed (this is expected for incomplete implementation)")
    
    print()
    print("=" * 40)
    
    if success:
        print("✓ Development setup completed successfully!")
        print()
        print("Next steps:")
        print("1. Start implementing the core components")
        print("2. Run tests with: pytest")
        print("3. Format code with: black src tests")
        print("4. Check types with: mypy src")
    else:
        print("✗ Development setup encountered issues")
        print("Please resolve the issues above before continuing")
        sys.exit(1)


if __name__ == "__main__":
    main()
