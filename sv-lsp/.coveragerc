[run]
# Coverage configuration for SystemVerilog LSP

source = src/sv_lsp
branch = True
parallel = True

# Files to include
include = 
    src/sv_lsp/*

# Files to exclude
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */.*
    setup.py
    */examples/*
    */scripts/*

[report]
# Reporting options
precision = 2
show_missing = True
skip_covered = False
sort = Cover

# Fail if coverage is below threshold
fail_under = 80

exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover
    
    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug
    
    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError
    
    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:
    
    # Don't complain about abstract methods
    @(abc\.)?abstractmethod
    
    # Don't complain about type checking imports
    if TYPE_CHECKING:
    
    # Don't complain about platform specific code
    if sys.platform
    
    # Don't complain about pyslang availability checks
    if not PYSLANG_AVAILABLE
    if pyslang is None

[html]
# HTML report options
directory = htmlcov
title = SystemVerilog LSP Coverage Report

[xml]
# XML report options
output = coverage.xml

[json]
# JSON report options
output = coverage.json
