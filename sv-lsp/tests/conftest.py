"""
Pytest configuration and fixtures for SystemVerilog LSP tests.
"""

import pytest
import tempfile
import os
from pathlib import Path
from typing import Generator

from sv_lsp.core.types import Location, SymbolInfo, SymbolKind

# Add src to path for all tests
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def sample_sv_file(temp_dir: Path) -> Path:
    """Create a sample SystemVerilog file for testing."""
    content = """
module test_module(
    input  logic        clk,
    input  logic        reset_n,
    input  logic [7:0]  data_in,
    output logic [15:0] data_out,
    output logic        valid_out
);

    // Internal signals
    logic [3:0]  counter;
    logic        enable;
    logic [1:0]  state, next_state;

    // Parameters
    parameter int WIDTH = 8;
    parameter int DEPTH = 16;

    // Function definition
    function automatic int calculate_sum(input int a, input int b);
        return a + b;
    endfunction

    // Task definition
    task automatic reset_counters();
        counter <= 4'b0;
        state <= 2'b00;
    endtask

    // Always block
    always_ff @(posedge clk or negedge reset_n) begin
        if (!reset_n) begin
            reset_counters();
        end else begin
            counter <= counter + 1;
            state <= next_state;
        end
    end

    // Combinational logic
    always_comb begin
        next_state = state + 1;
        enable = (counter == 4'hF);
        data_out = {data_in, data_in};
        valid_out = enable;
    end

endmodule

// Interface definition
interface test_interface(input logic clk);
    logic [7:0] data;
    logic       valid;

    modport master (
        output data,
        output valid
    );

    modport slave (
        input data,
        input valid
    );
endinterface

// Package definition
package test_package;
    typedef struct packed {
        logic [7:0] data;
        logic       valid;
    } data_packet_t;

    parameter int MAX_COUNT = 256;
endpackage
"""

    file_path = temp_dir / "test_module.sv"
    file_path.write_text(content)
    return file_path


@pytest.fixture
def sample_location() -> Location:
    """Create a sample location for testing."""
    return Location(
        file_path="/test/file.sv",
        line=10,
        column=5,
        offset=150
    )


@pytest.fixture
def sample_symbol() -> SymbolInfo:
    """Create a sample symbol for testing."""
    location = Location(
        file_path="/test/file.sv",
        line=5,
        column=10
    )

    return SymbolInfo(
        name="test_signal",
        kind=SymbolKind.VARIABLE,
        definition_location=location,
        qualified_name="test_module.test_signal",
        type_info="logic [7:0]",
        documentation="Test signal for unit testing"
    )


@pytest.fixture
def symbol_analyzer():
    """Provide a SymbolAnalyzer instance for testing."""
    from sv_lsp.analysis.symbol_analyzer import SymbolAnalyzer
    return SymbolAnalyzer()


@pytest.fixture
def semantic_model():
    """Provide a SemanticModel instance for testing."""
    from sv_lsp.core.semantic_model import SemanticModel
    return SemanticModel()


@pytest.fixture
def complex_sv_code():
    """Provide complex SystemVerilog code for testing."""
    return """
package test_pkg;
    typedef enum logic [1:0] {
        STATE_IDLE = 2'b00,
        STATE_ACTIVE = 2'b01,
        STATE_DONE = 2'b10
    } state_t;

    parameter int DATA_WIDTH = 32;
endpackage

interface axi_if #(
    parameter int DATA_WIDTH = 32
) (
    input logic clk,
    input logic rst_n
);
    logic [DATA_WIDTH-1:0] data;
    logic valid;

    modport master (output data, valid);
    modport slave (input data, valid);
endinterface

class transaction_base;
    rand bit [31:0] addr;
    rand bit [31:0] data;

    function new();
        addr = 0;
        data = 0;
    endfunction

    virtual task execute();
        $display("Executing transaction");
    endtask
endclass

module cpu_complex #(
    parameter int DATA_WIDTH = 32
) (
    input logic clk,
    input logic rst_n,
    axi_if.master mem_if,
    output logic [DATA_WIDTH-1:0] result
);

    logic [DATA_WIDTH-1:0] pc;
    logic [DATA_WIDTH-1:0] instruction;

    function automatic logic [DATA_WIDTH-1:0] alu_add(
        input logic [DATA_WIDTH-1:0] a,
        input logic [DATA_WIDTH-1:0] b
    );
        return a + b;
    endfunction

    task automatic fetch_instruction();
        instruction <= pc;
    endtask

    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            pc <= 0;
        end else begin
            pc <= pc + 4;
        end
    end

    assign result = alu_add(pc, instruction);

endmodule
"""


@pytest.fixture
def sample_workspace(tmp_path):
    """Create a sample workspace with multiple SystemVerilog files."""
    # Create directory structure
    src_dir = tmp_path / "src"
    src_dir.mkdir()

    tb_dir = tmp_path / "testbench"
    tb_dir.mkdir()

    # Create sample files
    (src_dir / "cpu.sv").write_text("""
module cpu(
    input logic clk,
    input logic reset,
    output logic [31:0] data_out
);
    logic [31:0] pc;

    always_ff @(posedge clk) begin
        if (reset)
            pc <= 0;
        else
            pc <= pc + 4;
    end

    assign data_out = pc;
endmodule
""")

    (src_dir / "memory.sv").write_text("""
module memory(
    input logic clk,
    input logic [31:0] addr,
    output logic [31:0] data
);
    logic [31:0] mem_array [0:1023];

    always_ff @(posedge clk) begin
        data <= mem_array[addr[9:0]];
    end
endmodule
""")

    (tb_dir / "cpu_tb.sv").write_text("""
module cpu_tb;
    logic clk = 0;
    logic reset = 1;
    logic [31:0] data_out;

    cpu dut(
        .clk(clk),
        .reset(reset),
        .data_out(data_out)
    );

    always #5 clk = ~clk;

    initial begin
        #100 reset = 0;
        #1000 $finish;
    end
endmodule
""")

    return tmp_path
