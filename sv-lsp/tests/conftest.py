"""
Pytest configuration and fixtures for SystemVerilog LSP tests.
"""

import pytest
import tempfile
import os
from pathlib import Path
from typing import Generator

from sv_lsp.core.types import Location, SymbolInfo, SymbolKind


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def sample_sv_file(temp_dir: Path) -> Path:
    """Create a sample SystemVerilog file for testing."""
    content = """
module test_module(
    input  logic        clk,
    input  logic        reset_n,
    input  logic [7:0]  data_in,
    output logic [15:0] data_out,
    output logic        valid_out
);

    // Internal signals
    logic [3:0]  counter;
    logic        enable;
    logic [1:0]  state, next_state;
    
    // Parameters
    parameter int WIDTH = 8;
    parameter int DEPTH = 16;
    
    // Function definition
    function automatic int calculate_sum(input int a, input int b);
        return a + b;
    endfunction
    
    // Task definition
    task automatic reset_counters();
        counter <= 4'b0;
        state <= 2'b00;
    endtask
    
    // Always block
    always_ff @(posedge clk or negedge reset_n) begin
        if (!reset_n) begin
            reset_counters();
        end else begin
            counter <= counter + 1;
            state <= next_state;
        end
    end
    
    // Combinational logic
    always_comb begin
        next_state = state + 1;
        enable = (counter == 4'hF);
        data_out = {data_in, data_in};
        valid_out = enable;
    end

endmodule

// Interface definition
interface test_interface(input logic clk);
    logic [7:0] data;
    logic       valid;
    
    modport master (
        output data,
        output valid
    );
    
    modport slave (
        input data,
        input valid
    );
endinterface

// Package definition
package test_package;
    typedef struct packed {
        logic [7:0] data;
        logic       valid;
    } data_packet_t;
    
    parameter int MAX_COUNT = 256;
endpackage
"""
    
    file_path = temp_dir / "test_module.sv"
    file_path.write_text(content)
    return file_path


@pytest.fixture
def sample_location() -> Location:
    """Create a sample location for testing."""
    return Location(
        file_path="/test/file.sv",
        line=10,
        column=5,
        offset=150
    )


@pytest.fixture
def sample_symbol() -> SymbolInfo:
    """Create a sample symbol for testing."""
    location = Location(
        file_path="/test/file.sv",
        line=5,
        column=10
    )
    
    return SymbolInfo(
        name="test_signal",
        kind=SymbolKind.VARIABLE,
        definition_location=location,
        qualified_name="test_module.test_signal",
        type_info="logic [7:0]",
        documentation="Test signal for unit testing"
    )
