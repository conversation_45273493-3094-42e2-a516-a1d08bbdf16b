"""
Tests for SymbolManager class.
"""

import pytest
from pathlib import Path
from sv_lsp.core.symbol_manager import SymbolManager
from sv_lsp.core.types import SymbolInfo, SymbolKind, Location, WorkspaceConfig


class TestSymbolManager:
    """Test SymbolManager class."""
    
    def test_symbol_manager_creation(self):
        """Test creating a symbol manager."""
        manager = SymbolManager()
        
        assert manager.config is not None
        assert isinstance(manager.file_indices, dict)
        assert isinstance(manager.global_symbols, dict)
        assert isinstance(manager.name_index, dict)
        assert isinstance(manager.kind_index, dict)
        assert isinstance(manager.scope_index, dict)
        assert isinstance(manager.prefix_index, dict)
    
    def test_symbol_manager_with_config(self):
        """Test creating a symbol manager with config."""
        config = WorkspaceConfig(
            include_paths=["/test/include"],
            defines={"TEST": "1"}
        )
        manager = SymbolManager(config)
        
        assert manager.config == config
        assert manager.config.include_paths == ["/test/include"]
        assert manager.config.defines == {"TEST": "1"}
    
    def test_add_symbol_to_indices(self):
        """Test adding a symbol to indices."""
        manager = SymbolManager()
        
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location,
            type_info="logic [7:0]"
        )
        
        manager._add_symbol_to_indices(symbol)
        
        # Check global symbols
        assert symbol.qualified_name in manager.global_symbols
        assert manager.global_symbols[symbol.qualified_name] == symbol
        
        # Check name index
        assert symbol.name in manager.name_index
        assert symbol in manager.name_index[symbol.name]
        
        # Check kind index
        assert symbol.kind in manager.kind_index
        assert symbol in manager.kind_index[symbol.kind]
        
        # Check scope index
        assert "<global>" in manager.scope_index
        assert symbol in manager.scope_index["<global>"]
        
        # Check prefix index
        for i in range(1, len(symbol.name) + 1):
            prefix = symbol.name[:i].lower()
            assert prefix in manager.prefix_index
            assert symbol in manager.prefix_index[prefix]
    
    def test_remove_symbol_from_indices(self):
        """Test removing a symbol from indices."""
        manager = SymbolManager()
        
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )
        
        # Add then remove
        manager._add_symbol_to_indices(symbol)
        manager._remove_symbol_from_indices(symbol)
        
        # Check all indices are clean
        assert symbol.qualified_name not in manager.global_symbols
        assert symbol.name not in manager.name_index
        assert symbol.kind not in manager.kind_index
        assert "<global>" not in manager.scope_index
        
        # Check prefix index is clean
        for i in range(1, len(symbol.name) + 1):
            prefix = symbol.name[:i].lower()
            assert prefix not in manager.prefix_index
    
    def test_find_symbol(self):
        """Test finding a symbol by name."""
        manager = SymbolManager()
        
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )
        
        manager._add_symbol_to_indices(symbol)
        
        # Find by simple name
        found = manager.find_symbol("test_signal")
        assert found == symbol
        
        # Find by qualified name
        found = manager.find_symbol("test_signal", qualified=True)
        assert found == symbol
        
        # Not found
        found = manager.find_symbol("nonexistent")
        assert found is None
    
    def test_find_symbols_by_kind(self):
        """Test finding symbols by kind."""
        manager = SymbolManager()
        
        location1 = Location(file_path="/test/file.sv", line=5, column=10)
        symbol1 = SymbolInfo(
            name="signal1",
            kind=SymbolKind.VARIABLE,
            definition_location=location1
        )
        
        location2 = Location(file_path="/test/file.sv", line=10, column=5)
        symbol2 = SymbolInfo(
            name="signal2",
            kind=SymbolKind.VARIABLE,
            definition_location=location2
        )
        
        location3 = Location(file_path="/test/file.sv", line=15, column=0)
        symbol3 = SymbolInfo(
            name="module1",
            kind=SymbolKind.MODULE,
            definition_location=location3
        )
        
        manager._add_symbol_to_indices(symbol1)
        manager._add_symbol_to_indices(symbol2)
        manager._add_symbol_to_indices(symbol3)
        
        # Find variables
        variables = manager.find_symbols_by_kind(SymbolKind.VARIABLE)
        assert len(variables) == 2
        assert symbol1 in variables
        assert symbol2 in variables
        
        # Find modules
        modules = manager.find_symbols_by_kind(SymbolKind.MODULE)
        assert len(modules) == 1
        assert symbol3 in modules
        
        # Find non-existent kind
        functions = manager.find_symbols_by_kind(SymbolKind.FUNCTION)
        assert len(functions) == 0
    
    def test_find_symbols_by_prefix(self):
        """Test finding symbols by prefix."""
        manager = SymbolManager()
        
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol1 = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )
        
        symbol2 = SymbolInfo(
            name="test_module",
            kind=SymbolKind.MODULE,
            definition_location=location
        )
        
        symbol3 = SymbolInfo(
            name="other_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )
        
        manager._add_symbol_to_indices(symbol1)
        manager._add_symbol_to_indices(symbol2)
        manager._add_symbol_to_indices(symbol3)
        
        # Find by prefix "test"
        results = manager.find_symbols_by_prefix("test")
        assert len(results) == 2
        assert symbol1 in results
        assert symbol2 in results
        
        # Find by prefix "other"
        results = manager.find_symbols_by_prefix("other")
        assert len(results) == 1
        assert symbol3 in results
        
        # Find by non-existent prefix
        results = manager.find_symbols_by_prefix("nonexistent")
        assert len(results) == 0
    
    def test_search_symbols(self):
        """Test searching symbols with query."""
        manager = SymbolManager()
        
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol1 = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )
        
        symbol2 = SymbolInfo(
            name="signal_test",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )
        
        manager._add_symbol_to_indices(symbol1)
        manager._add_symbol_to_indices(symbol2)
        
        # Search for "test"
        results = manager.search_symbols("test")
        assert len(results) >= 1  # Should find at least one
        
        # Search for exact name
        results = manager.search_symbols("test_signal")
        assert symbol1 in results
    
    def test_relationship_management(self):
        """Test symbol relationship management."""
        manager = SymbolManager()
        
        # Add inheritance relationship
        manager.add_inheritance_relationship("child_class", "parent_class")
        
        # Add instantiation relationship
        manager.add_instantiation_relationship("cpu_inst", "cpu_module")
        
        # Add reference relationship
        manager.add_reference_relationship("function1", "variable1")
        
        # Check relationships are stored
        assert "child_class" in manager.inheritance_map
        assert "parent_class" in manager.inheritance_map["child_class"]
        
        assert "cpu_inst" in manager.instantiation_map
        assert "cpu_module" in manager.instantiation_map["cpu_inst"]
        
        assert "function1" in manager.reference_map
        assert "variable1" in manager.reference_map["function1"]
    
    def test_clear(self):
        """Test clearing all symbol data."""
        manager = SymbolManager()
        
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )
        
        manager._add_symbol_to_indices(symbol)
        manager.add_inheritance_relationship("child", "parent")
        
        # Verify data exists
        assert len(manager.global_symbols) > 0
        assert len(manager.inheritance_map) > 0
        
        # Clear and verify empty
        manager.clear()
        
        assert len(manager.global_symbols) == 0
        assert len(manager.name_index) == 0
        assert len(manager.kind_index) == 0
        assert len(manager.scope_index) == 0
        assert len(manager.prefix_index) == 0
        assert len(manager.inheritance_map) == 0
        assert len(manager.instantiation_map) == 0
        assert len(manager.reference_map) == 0
