"""
Tests for core type definitions.
"""

import pytest
from sv_lsp.core.types import Location, Range, SymbolInfo, SymbolKind


class TestLocation:
    """Test Location class."""
    
    def test_location_creation(self):
        """Test creating a location."""
        loc = Location(
            file_path="/test/file.sv",
            line=10,
            column=5,
            offset=150
        )
        
        assert loc.file_path == "/test/file.sv"
        assert loc.line == 10
        assert loc.column == 5
        assert loc.offset == 150
    
    def test_location_str(self):
        """Test location string representation."""
        loc = Location(file_path="/test/file.sv", line=9, column=4)
        assert str(loc) == "/test/file.sv:10:5"  # 1-based in string
    
    def test_to_lsp_position(self):
        """Test conversion to LSP position format."""
        loc = Location(file_path="/test/file.sv", line=10, column=5)
        lsp_pos = loc.to_lsp_position()
        
        assert lsp_pos == {"line": 10, "character": 5}
    
    def test_to_lsp_location(self):
        """Test conversion to LSP location format."""
        loc = Location(file_path="/test/file.sv", line=10, column=5)
        lsp_loc = loc.to_lsp_location()
        
        assert "uri" in lsp_loc
        assert "range" in lsp_loc
        assert lsp_loc["range"]["start"] == {"line": 10, "character": 5}
        assert lsp_loc["range"]["end"] == {"line": 10, "character": 5}


class TestRange:
    """Test Range class."""
    
    def test_range_creation(self):
        """Test creating a range."""
        start = Location(file_path="/test/file.sv", line=10, column=5)
        end = Location(file_path="/test/file.sv", line=10, column=15)
        range_obj = Range(start=start, end=end)
        
        assert range_obj.start == start
        assert range_obj.end == end
    
    def test_contains_position_same_line(self):
        """Test position containment on same line."""
        start = Location(file_path="/test/file.sv", line=10, column=5)
        end = Location(file_path="/test/file.sv", line=10, column=15)
        range_obj = Range(start=start, end=end)
        
        # Position within range
        pos_inside = Location(file_path="/test/file.sv", line=10, column=10)
        assert range_obj.contains_position(pos_inside)
        
        # Position at start
        pos_start = Location(file_path="/test/file.sv", line=10, column=5)
        assert range_obj.contains_position(pos_start)
        
        # Position at end
        pos_end = Location(file_path="/test/file.sv", line=10, column=15)
        assert range_obj.contains_position(pos_end)
        
        # Position before range
        pos_before = Location(file_path="/test/file.sv", line=10, column=3)
        assert not range_obj.contains_position(pos_before)
        
        # Position after range
        pos_after = Location(file_path="/test/file.sv", line=10, column=20)
        assert not range_obj.contains_position(pos_after)
    
    def test_contains_position_different_lines(self):
        """Test position containment across lines."""
        start = Location(file_path="/test/file.sv", line=10, column=5)
        end = Location(file_path="/test/file.sv", line=12, column=15)
        range_obj = Range(start=start, end=end)
        
        # Position on middle line
        pos_middle = Location(file_path="/test/file.sv", line=11, column=10)
        assert range_obj.contains_position(pos_middle)
        
        # Position before range
        pos_before = Location(file_path="/test/file.sv", line=9, column=10)
        assert not range_obj.contains_position(pos_before)
        
        # Position after range
        pos_after = Location(file_path="/test/file.sv", line=13, column=10)
        assert not range_obj.contains_position(pos_after)
    
    def test_contains_position_different_file(self):
        """Test position containment with different file."""
        start = Location(file_path="/test/file1.sv", line=10, column=5)
        end = Location(file_path="/test/file1.sv", line=10, column=15)
        range_obj = Range(start=start, end=end)
        
        pos_different_file = Location(file_path="/test/file2.sv", line=10, column=10)
        assert not range_obj.contains_position(pos_different_file)
    
    def test_to_lsp_range(self):
        """Test conversion to LSP range format."""
        start = Location(file_path="/test/file.sv", line=10, column=5)
        end = Location(file_path="/test/file.sv", line=10, column=15)
        range_obj = Range(start=start, end=end)
        
        lsp_range = range_obj.to_lsp_range()
        
        assert lsp_range == {
            "start": {"line": 10, "character": 5},
            "end": {"line": 10, "character": 15}
        }


class TestSymbolInfo:
    """Test SymbolInfo class."""
    
    def test_symbol_creation(self):
        """Test creating a symbol."""
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location,
            type_info="logic [7:0]"
        )
        
        assert symbol.name == "test_signal"
        assert symbol.kind == SymbolKind.VARIABLE
        assert symbol.definition_location == location
        assert symbol.qualified_name == "test_signal"  # No parent
        assert symbol.type_info == "logic [7:0]"
    
    def test_qualified_name_with_parent(self):
        """Test qualified name generation with parent."""
        parent_location = Location(file_path="/test/file.sv", line=1, column=0)
        parent = SymbolInfo(
            name="test_module",
            kind=SymbolKind.MODULE,
            definition_location=parent_location
        )
        
        child_location = Location(file_path="/test/file.sv", line=5, column=10)
        child = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=child_location,
            parent=parent
        )
        
        assert child.qualified_name == "test_module.test_signal"
    
    def test_add_reference(self):
        """Test adding references to a symbol."""
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )
        
        ref1 = Location(file_path="/test/file.sv", line=10, column=5)
        ref2 = Location(file_path="/test/file.sv", line=15, column=8)
        
        symbol.add_reference(ref1)
        symbol.add_reference(ref2)
        
        assert len(symbol.references) == 2
        assert ref1 in symbol.references
        assert ref2 in symbol.references
        
        # Adding same reference again should not duplicate
        symbol.add_reference(ref1)
        assert len(symbol.references) == 2
    
    def test_add_child(self):
        """Test adding child symbols."""
        parent_location = Location(file_path="/test/file.sv", line=1, column=0)
        parent = SymbolInfo(
            name="test_module",
            kind=SymbolKind.MODULE,
            definition_location=parent_location
        )
        
        child_location = Location(file_path="/test/file.sv", line=5, column=10)
        child = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=child_location
        )
        
        parent.add_child(child)
        
        assert len(parent.children) == 1
        assert child in parent.children
        assert child.parent == parent
        assert child.qualified_name == "test_module.test_signal"
    
    def test_symbol_kind_to_lsp(self):
        """Test conversion of symbol kinds to LSP format."""
        location = Location(file_path="/test/file.sv", line=5, column=10)
        
        # Test a few key symbol kinds
        module_symbol = SymbolInfo(
            name="test_module",
            kind=SymbolKind.MODULE,
            definition_location=location
        )
        assert module_symbol._symbol_kind_to_lsp() == 2  # Module
        
        var_symbol = SymbolInfo(
            name="test_var",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )
        assert var_symbol._symbol_kind_to_lsp() == 13  # Variable
        
        func_symbol = SymbolInfo(
            name="test_func",
            kind=SymbolKind.FUNCTION,
            definition_location=location
        )
        assert func_symbol._symbol_kind_to_lsp() == 12  # Function
