"""
Tests for utility helper functions.
"""

import pytest
import os
from pathlib import Path

from sv_lsp.utils.helpers import (
    uri_to_path,
    path_to_uri,
    position_to_offset,
    offset_to_position,
    is_systemverilog_file,
    extract_identifier_at_position,
    split_qualified_name,
)


class TestUriPathConversion:
    """Test URI and path conversion functions."""
    
    def test_uri_to_path_unix(self):
        """Test URI to path conversion on Unix-like systems."""
        uri = "file:///home/<USER>/test.sv"
        expected = "/home/<USER>/test.sv"
        
        if os.name != "nt":  # Skip on Windows
            result = uri_to_path(uri)
            assert result == expected
    
    def test_uri_to_path_windows(self):
        """Test URI to path conversion on Windows."""
        uri = "file:///C:/Users/<USER>/test.sv"
        expected = "C:/Users/<USER>/test.sv"
        
        if os.name == "nt":  # Only on Windows
            result = uri_to_path(uri)
            assert result == expected
    
    def test_uri_to_path_invalid_scheme(self):
        """Test URI to path with invalid scheme."""
        uri = "http://example.com/test.sv"
        
        with pytest.raises(ValueError, match="Expected file URI"):
            uri_to_path(uri)
    
    def test_path_to_uri(self):
        """Test path to URI conversion."""
        # Use a relative path that exists
        test_path = "test.sv"
        result = path_to_uri(test_path)
        
        assert result.startswith("file://")
        assert result.endswith("test.sv")


class TestPositionConversion:
    """Test position and offset conversion functions."""
    
    def test_position_to_offset_simple(self):
        """Test position to offset conversion."""
        text = "line1\nline2\nline3"
        
        # Start of file
        assert position_to_offset(text, 0, 0) == 0
        
        # Start of second line
        assert position_to_offset(text, 1, 0) == 6  # "line1\n" = 6 chars
        
        # Middle of second line
        assert position_to_offset(text, 1, 2) == 8  # "line1\nli" = 8 chars
    
    def test_position_to_offset_beyond_bounds(self):
        """Test position to offset with out-of-bounds positions."""
        text = "line1\nline2"
        
        # Line beyond end
        result = position_to_offset(text, 10, 0)
        assert result == len(text)
        
        # Column beyond line end
        result = position_to_offset(text, 0, 10)
        assert result == 5  # End of first line
    
    def test_offset_to_position_simple(self):
        """Test offset to position conversion."""
        text = "line1\nline2\nline3"
        
        # Start of file
        line, col = offset_to_position(text, 0)
        assert (line, col) == (0, 0)
        
        # Start of second line
        line, col = offset_to_position(text, 6)
        assert (line, col) == (1, 0)
        
        # Middle of second line
        line, col = offset_to_position(text, 8)
        assert (line, col) == (1, 2)
    
    def test_offset_to_position_beyond_bounds(self):
        """Test offset to position with out-of-bounds offset."""
        text = "line1\nline2"
        
        line, col = offset_to_position(text, 100)
        assert line == 1  # Last line
        assert col == 5   # End of last line


class TestFileTypeDetection:
    """Test file type detection functions."""
    
    def test_is_systemverilog_file_positive(self):
        """Test SystemVerilog file detection for valid extensions."""
        assert is_systemverilog_file("test.sv")
        assert is_systemverilog_file("test.svh")
        assert is_systemverilog_file("test.v")
        assert is_systemverilog_file("test.vh")
        assert is_systemverilog_file("test.svi")
        
        # Case insensitive
        assert is_systemverilog_file("test.SV")
        assert is_systemverilog_file("test.SVH")
    
    def test_is_systemverilog_file_negative(self):
        """Test SystemVerilog file detection for invalid extensions."""
        assert not is_systemverilog_file("test.py")
        assert not is_systemverilog_file("test.cpp")
        assert not is_systemverilog_file("test.txt")
        assert not is_systemverilog_file("test")


class TestIdentifierExtraction:
    """Test identifier extraction functions."""
    
    def test_extract_identifier_at_position_simple(self):
        """Test extracting identifier at position."""
        text = "module test_module;"
        
        # Position on 'test_module'
        result = extract_identifier_at_position(text, 0, 7)
        assert result == "test_module"
        
        # Position on 'module'
        result = extract_identifier_at_position(text, 0, 2)
        assert result == "module"
    
    def test_extract_identifier_at_position_no_identifier(self):
        """Test extracting identifier when none exists at position."""
        text = "module test_module;"
        
        # Position on space
        result = extract_identifier_at_position(text, 0, 6)
        assert result is None
        
        # Position on semicolon
        result = extract_identifier_at_position(text, 0, 18)
        assert result is None
    
    def test_extract_identifier_at_position_out_of_bounds(self):
        """Test extracting identifier with out-of-bounds position."""
        text = "module test_module;"
        
        # Line out of bounds
        result = extract_identifier_at_position(text, 10, 0)
        assert result is None
        
        # Column out of bounds
        result = extract_identifier_at_position(text, 0, 100)
        assert result is None
    
    def test_extract_identifier_complex(self):
        """Test extracting identifier from complex text."""
        text = "logic [7:0] data_signal = 8'h42;"
        
        # Position on 'logic'
        result = extract_identifier_at_position(text, 0, 2)
        assert result == "logic"
        
        # Position on 'data_signal'
        result = extract_identifier_at_position(text, 0, 15)
        assert result == "data_signal"


class TestQualifiedNames:
    """Test qualified name handling functions."""
    
    def test_split_qualified_name_simple(self):
        """Test splitting simple qualified names."""
        parent, local = split_qualified_name("module.signal")
        assert parent == "module"
        assert local == "signal"
    
    def test_split_qualified_name_nested(self):
        """Test splitting nested qualified names."""
        parent, local = split_qualified_name("top.cpu.register")
        assert parent == "top.cpu"
        assert local == "register"
    
    def test_split_qualified_name_no_parent(self):
        """Test splitting name with no parent."""
        parent, local = split_qualified_name("signal")
        assert parent is None
        assert local == "signal"
    
    def test_split_qualified_name_empty(self):
        """Test splitting empty name."""
        parent, local = split_qualified_name("")
        assert parent is None
        assert local == ""
