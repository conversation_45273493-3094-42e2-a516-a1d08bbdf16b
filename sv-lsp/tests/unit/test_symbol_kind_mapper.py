"""
Tests for SymbolKindMapper.
"""

import pytest
import sys
from pathlib import Path

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from sv_lsp.core.types import SymbolKind
from sv_lsp.core.symbol_kind_mapper import SymbolKindMapper


class TestSymbolKindMapper:
    """Test cases for SymbolKindMapper."""
    
    def test_slang_to_internal_mapping(self):
        """Test mapping from slang SymbolKind to internal SymbolKind."""
        # Test basic mappings
        assert SymbolKindMapper.from_slang("SymbolKind.Variable") == SymbolKind.VARIABLE
        assert SymbolKindMapper.from_slang("SymbolKind.Instance") == SymbolKind.INSTANCE
        assert SymbolKindMapper.from_slang("SymbolKind.Parameter") == SymbolKind.PARAMETER
        assert SymbolKindMapper.from_slang("SymbolKind.Port") == SymbolKind.PORT
        
        # Test type system mappings
        assert SymbolKindMapper.from_slang("SymbolKind.ClassType") == SymbolKind.CLASS_TYPE
        assert SymbolKindMapper.from_slang("SymbolKind.EnumType") == SymbolKind.ENUM_TYPE
        assert SymbolKindMapper.from_slang("SymbolKind.PackedStructType") == SymbolKind.PACKED_STRUCT_TYPE
        
        # Test unknown mapping
        assert SymbolKindMapper.from_slang("SymbolKind.NonExistent") == SymbolKind.UNKNOWN
        assert SymbolKindMapper.from_slang("InvalidString") == SymbolKind.UNKNOWN
    
    def test_internal_to_lsp_mapping(self):
        """Test mapping from internal SymbolKind to LSP protocol numbers."""
        # Test basic mappings
        assert SymbolKindMapper.to_lsp(SymbolKind.INSTANCE) == 2  # Module
        assert SymbolKindMapper.to_lsp(SymbolKind.CLASS_TYPE) == 5  # Class
        assert SymbolKindMapper.to_lsp(SymbolKind.SUBROUTINE) == 12  # Function
        assert SymbolKindMapper.to_lsp(SymbolKind.VARIABLE) == 13  # Variable
        assert SymbolKindMapper.to_lsp(SymbolKind.PARAMETER) == 14  # Constant
        
        # Test array types
        assert SymbolKindMapper.to_lsp(SymbolKind.PACKED_ARRAY_TYPE) == 18  # Array
        assert SymbolKindMapper.to_lsp(SymbolKind.DYNAMIC_ARRAY_TYPE) == 18  # Array
        
        # Test enum types
        assert SymbolKindMapper.to_lsp(SymbolKind.ENUM_TYPE) == 10  # Enum
        assert SymbolKindMapper.to_lsp(SymbolKind.ENUM_VALUE) == 22  # EnumMember
        
        # Test struct types
        assert SymbolKindMapper.to_lsp(SymbolKind.PACKED_STRUCT_TYPE) == 23  # Struct
        assert SymbolKindMapper.to_lsp(SymbolKind.UNPACKED_STRUCT_TYPE) == 23  # Struct
        
        # Test default mapping for unknown types
        assert SymbolKindMapper.to_lsp(SymbolKind.UNKNOWN) == 13  # Variable (default)
    
    def test_category_classification(self):
        """Test symbol category classification."""
        # Test type category
        assert SymbolKindMapper.get_category(SymbolKind.CLASS_TYPE) == "type"
        assert SymbolKindMapper.get_category(SymbolKind.ENUM_TYPE) == "type"
        assert SymbolKindMapper.get_category(SymbolKind.PACKED_STRUCT_TYPE) == "type"
        assert SymbolKindMapper.get_category(SymbolKind.SCALAR_TYPE) == "type"
        
        # Test module category
        assert SymbolKindMapper.get_category(SymbolKind.ROOT) == "module"
        assert SymbolKindMapper.get_category(SymbolKind.INSTANCE) == "module"
        assert SymbolKindMapper.get_category(SymbolKind.PACKAGE) == "module"
        assert SymbolKindMapper.get_category(SymbolKind.GENERATE_BLOCK) == "module"
        
        # Test declaration category
        assert SymbolKindMapper.get_category(SymbolKind.VARIABLE) == "declaration"
        assert SymbolKindMapper.get_category(SymbolKind.NET) == "declaration"
        assert SymbolKindMapper.get_category(SymbolKind.PARAMETER) == "declaration"
        assert SymbolKindMapper.get_category(SymbolKind.PORT) == "declaration"
        assert SymbolKindMapper.get_category(SymbolKind.SUBROUTINE) == "declaration"
        
        # Test verification category
        assert SymbolKindMapper.get_category(SymbolKind.PROPERTY) == "verification"
        assert SymbolKindMapper.get_category(SymbolKind.SEQUENCE) == "verification"
        assert SymbolKindMapper.get_category(SymbolKind.ASSERTION_PORT) == "verification"
        assert SymbolKindMapper.get_category(SymbolKind.COVERPOINT) == "verification"
        
        # Test timing category
        assert SymbolKindMapper.get_category(SymbolKind.CLOCKING_BLOCK) == "timing"
        assert SymbolKindMapper.get_category(SymbolKind.TIMING_PATH) == "timing"
        assert SymbolKindMapper.get_category(SymbolKind.SYSTEM_TIMING_CHECK) == "timing"
        
        # Test other category
        assert SymbolKindMapper.get_category(SymbolKind.UNKNOWN) == "other"
    
    def test_container_detection(self):
        """Test container symbol detection."""
        # Test container symbols
        assert SymbolKindMapper.is_container(SymbolKind.ROOT) == True
        assert SymbolKindMapper.is_container(SymbolKind.INSTANCE) == True
        assert SymbolKindMapper.is_container(SymbolKind.CLASS_TYPE) == True
        assert SymbolKindMapper.is_container(SymbolKind.PACKAGE) == True
        assert SymbolKindMapper.is_container(SymbolKind.GENERATE_BLOCK) == True
        assert SymbolKindMapper.is_container(SymbolKind.PROCEDURAL_BLOCK) == True
        assert SymbolKindMapper.is_container(SymbolKind.PACKED_STRUCT_TYPE) == True
        assert SymbolKindMapper.is_container(SymbolKind.UNPACKED_STRUCT_TYPE) == True
        
        # Test non-container symbols
        assert SymbolKindMapper.is_container(SymbolKind.VARIABLE) == False
        assert SymbolKindMapper.is_container(SymbolKind.NET) == False
        assert SymbolKindMapper.is_container(SymbolKind.PARAMETER) == False
        assert SymbolKindMapper.is_container(SymbolKind.PORT) == False
        assert SymbolKindMapper.is_container(SymbolKind.SUBROUTINE) == False
        assert SymbolKindMapper.is_container(SymbolKind.FIELD) == False
    
    def test_mapping_completeness(self):
        """Test that all important slang mappings are covered."""
        # Test that we have mappings for common slang symbol kinds
        common_slang_kinds = [
            "SymbolKind.Variable",
            "SymbolKind.Net",
            "SymbolKind.Parameter",
            "SymbolKind.Port",
            "SymbolKind.Instance",
            "SymbolKind.Subroutine",
            "SymbolKind.ClassType",
            "SymbolKind.EnumType",
            "SymbolKind.PackedStructType",
            "SymbolKind.UnpackedStructType",
            "SymbolKind.Package",
            "SymbolKind.GenerateBlock",
            "SymbolKind.ProceduralBlock",
        ]
        
        for slang_kind in common_slang_kinds:
            internal_kind = SymbolKindMapper.from_slang(slang_kind)
            assert internal_kind != SymbolKind.UNKNOWN, f"Missing mapping for {slang_kind}"
    
    def test_lsp_mapping_coverage(self):
        """Test that all internal symbol kinds have LSP mappings."""
        # Test that all symbol kinds can be mapped to LSP
        for symbol_kind in SymbolKind:
            lsp_kind = SymbolKindMapper.to_lsp(symbol_kind)
            assert isinstance(lsp_kind, int), f"Invalid LSP mapping for {symbol_kind}"
            assert 1 <= lsp_kind <= 26, f"LSP kind {lsp_kind} out of valid range for {symbol_kind}"
    
    def test_category_coverage(self):
        """Test that all symbol kinds have categories."""
        for symbol_kind in SymbolKind:
            category = SymbolKindMapper.get_category(symbol_kind)
            assert category in ["type", "module", "declaration", "verification", "timing", "other"], \
                f"Invalid category '{category}' for {symbol_kind}"
    
    def test_bidirectional_consistency(self):
        """Test consistency between different mapping directions."""
        # Test some round-trip mappings where applicable
        test_cases = [
            ("SymbolKind.Variable", SymbolKind.VARIABLE),
            ("SymbolKind.Instance", SymbolKind.INSTANCE),
            ("SymbolKind.ClassType", SymbolKind.CLASS_TYPE),
            ("SymbolKind.Parameter", SymbolKind.PARAMETER),
        ]
        
        for slang_str, expected_internal in test_cases:
            # Test slang -> internal
            internal = SymbolKindMapper.from_slang(slang_str)
            assert internal == expected_internal
            
            # Test internal -> LSP (should not fail)
            lsp_kind = SymbolKindMapper.to_lsp(internal)
            assert isinstance(lsp_kind, int)
            
            # Test category assignment
            category = SymbolKindMapper.get_category(internal)
            assert isinstance(category, str)


class TestSymbolKindMapperEdgeCases:
    """Test edge cases for SymbolKindMapper."""
    
    def test_empty_and_none_inputs(self):
        """Test handling of empty and None inputs."""
        # Test empty string
        assert SymbolKindMapper.from_slang("") == SymbolKind.UNKNOWN
        
        # Test None (should handle gracefully)
        try:
            result = SymbolKindMapper.from_slang(None)
            assert result == SymbolKind.UNKNOWN
        except (TypeError, AttributeError):
            # This is also acceptable behavior
            pass
    
    def test_case_sensitivity(self):
        """Test case sensitivity in mappings."""
        # Test that mappings are case-sensitive
        assert SymbolKindMapper.from_slang("symbolkind.variable") == SymbolKind.UNKNOWN
        assert SymbolKindMapper.from_slang("SYMBOLKIND.VARIABLE") == SymbolKind.UNKNOWN
        assert SymbolKindMapper.from_slang("SymbolKind.variable") == SymbolKind.UNKNOWN
    
    def test_partial_matches(self):
        """Test that partial matches don't work."""
        # Test that partial strings don't match
        assert SymbolKindMapper.from_slang("Variable") == SymbolKind.UNKNOWN
        assert SymbolKindMapper.from_slang("SymbolKind.") == SymbolKind.UNKNOWN
        assert SymbolKindMapper.from_slang("Kind.Variable") == SymbolKind.UNKNOWN
    
    def test_whitespace_handling(self):
        """Test handling of whitespace in inputs."""
        # Test that whitespace doesn't break mappings
        assert SymbolKindMapper.from_slang(" SymbolKind.Variable ") == SymbolKind.UNKNOWN
        assert SymbolKindMapper.from_slang("SymbolKind.Variable\n") == SymbolKind.UNKNOWN
        assert SymbolKindMapper.from_slang("\tSymbolKind.Variable") == SymbolKind.UNKNOWN


if __name__ == "__main__":
    pytest.main([__file__])
