"""
Tests for EnhancedLSPHandlers.
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from sv_lsp.lsp.enhanced_handlers import EnhancedLSPHandlers
from sv_lsp.core.types import SymbolKind, Location


class TestEnhancedLSPHandlers:
    """Test cases for EnhancedLSPHandlers."""

    def setup_method(self):
        """Set up test fixtures."""
        self.handlers = EnhancedLSPHandlers()

    def test_initialization(self):
        """Test handler initialization."""
        assert self.handlers is not None
        assert self.handlers.symbol_analyzer is not None
        assert self.handlers.semantic_model is None  # Not set until document is opened

    def test_document_open_handling(self):
        """Test document open handling."""
        params = {
            "textDocument": {
                "uri": "file:///test.sv",
                "text": """
                module test_module(
                    input logic clk,
                    output logic data
                );
                    logic internal_signal;
                endmodule
                """
            }
        }

        # Should not raise an exception
        self.handlers.handle_text_document_did_open(params)

        # Should have created semantic model
        assert self.handlers.semantic_model is not None

    def test_goto_definition(self):
        """Test go-to-definition functionality."""
        # First open a document
        open_params = {
            "textDocument": {
                "uri": "file:///test.sv",
                "text": """
                module test_module(
                    input logic clk,
                    output logic data
                );
                    logic internal_signal;
                    assign data = internal_signal;
                endmodule
                """
            }
        }
        self.handlers.handle_text_document_did_open(open_params)

        # Test go-to-definition
        def_params = {
            "textDocument": {"uri": "file:///test.sv"},
            "position": {"line": 2, "character": 20}  # Position on 'clk'
        }

        result = self.handlers.handle_goto_definition(def_params)
        # Result might be None if symbol not found, which is acceptable
        if result:
            assert "uri" in result
            assert "range" in result

    def test_find_references(self):
        """Test find references functionality."""
        # First open a document
        open_params = {
            "textDocument": {
                "uri": "file:///test.sv",
                "text": """
                module test_module;
                    logic signal1;
                    logic signal2;
                    assign signal2 = signal1;
                endmodule
                """
            }
        }
        self.handlers.handle_text_document_did_open(open_params)

        # Test find references
        ref_params = {
            "textDocument": {"uri": "file:///test.sv"},
            "position": {"line": 2, "character": 10},  # Position on signal1
            "context": {"includeDeclaration": True}
        }

        result = self.handlers.handle_find_references(ref_params)
        assert isinstance(result, list)
        # Result might be empty if no references found

    def test_hover(self):
        """Test hover functionality."""
        # First open a document
        open_params = {
            "textDocument": {
                "uri": "file:///test.sv",
                "text": """
                module test_module(
                    input logic clk
                );
                endmodule
                """
            }
        }
        self.handlers.handle_text_document_did_open(open_params)

        # Test hover
        hover_params = {
            "textDocument": {"uri": "file:///test.sv"},
            "position": {"line": 1, "character": 7}  # Position on module name
        }

        result = self.handlers.handle_hover(hover_params)
        # Result might be None if no hover info available
        if result:
            assert "contents" in result
            assert "range" in result

    def test_workspace_symbol(self):
        """Test workspace symbol search."""
        # First open a document
        open_params = {
            "textDocument": {
                "uri": "file:///test.sv",
                "text": """
                module cpu_module;
                    logic cpu_signal;
                endmodule

                module memory_module;
                    logic memory_signal;
                endmodule
                """
            }
        }
        self.handlers.handle_text_document_did_open(open_params)

        # Test workspace symbol search
        symbol_params = {"query": "cpu"}

        result = self.handlers.handle_workspace_symbol(symbol_params)
        assert isinstance(result, list)
        # Should find symbols matching "cpu"

    def test_enhanced_hover_content(self):
        """Test enhanced hover content generation."""
        from sv_lsp.core.types import SymbolInfo

        # Create a test symbol
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=Location(file_path="test.sv", line=5, column=10),
            qualified_name="test_module.test_signal",
            type_info="logic [7:0]",
            documentation="Test signal for demonstration"
        )

        # Mock semantic model
        self.handlers.semantic_model = Mock()
        self.handlers.semantic_model.get_symbol_hierarchy.return_value = {
            "parent": "test_module",
            "children": [],
            "references": [Location(file_path="test.sv", line=10, column=5)]
        }

        content = self.handlers._build_enhanced_hover_content(symbol)

        assert "test_signal" in content
        assert "variable" in content
        assert "logic [7:0]" in content
        assert "test_module.test_signal" in content
        assert "test_module" in content
        assert "1 locations" in content
        assert "Test signal for demonstration" in content

    def test_symbol_relevance_ranking(self):
        """Test symbol relevance ranking."""
        from sv_lsp.core.types import SymbolInfo

        symbols = [
            SymbolInfo(name="exact_match", kind=SymbolKind.VARIABLE,
                      definition_location=Location("test.sv", 1, 1)),
            SymbolInfo(name="starts_with_exact", kind=SymbolKind.MODULE,
                      definition_location=Location("test.sv", 2, 1)),
            SymbolInfo(name="contains_exact_somewhere", kind=SymbolKind.FUNCTION,
                      definition_location=Location("test.sv", 3, 1)),
            SymbolInfo(name="no_match", kind=SymbolKind.VARIABLE,
                      definition_location=Location("test.sv", 4, 1)),
        ]

        # Mock semantic model
        self.handlers.semantic_model = Mock()
        self.handlers.semantic_model.get_symbol_references.return_value = set()

        ranked = self.handlers._rank_symbols_by_relevance(symbols, "exact")

        # Should be ranked by relevance
        assert len(ranked) == 4
        assert ranked[0].name == "exact_match"  # Exact match should be first

    def test_error_handling(self):
        """Test error handling in handlers."""
        # Test with invalid URI
        params = {
            "textDocument": {
                "uri": "invalid://uri",
                "text": "module test; endmodule"
            }
        }

        # Should not raise an exception
        self.handlers.handle_text_document_did_open(params)

        # Test go-to-definition with invalid params
        def_params = {
            "textDocument": {"uri": "invalid://uri"},
            "position": {"line": 0, "character": 0}
        }

        result = self.handlers.handle_goto_definition(def_params)
        # Should return None or handle gracefully
        assert result is None or isinstance(result, dict)

    def test_semantic_statistics(self):
        """Test semantic statistics retrieval."""
        stats = self.handlers.get_semantic_statistics()

        if self.handlers.semantic_model:
            assert isinstance(stats, dict)
            assert "cached_symbols" in stats or "semantic_model" in stats
        else:
            assert stats == {"semantic_model": "not_available"}


class TestEnhancedLSPHandlersIntegration:
    """Integration tests for EnhancedLSPHandlers."""

    def setup_method(self):
        """Set up test fixtures."""
        self.handlers = EnhancedLSPHandlers()

    def test_full_workflow(self):
        """Test a complete LSP workflow."""
        # 1. Open document
        open_params = {
            "textDocument": {
                "uri": "file:///workflow_test.sv",
                "text": """
                module workflow_test(
                    input logic clk,
                    input logic reset,
                    output logic [7:0] data
                );
                    logic [7:0] counter;

                    always_ff @(posedge clk) begin
                        if (reset)
                            counter <= 0;
                        else
                            counter <= counter + 1;
                    end

                    assign data = counter;
                endmodule
                """
            }
        }

        self.handlers.handle_text_document_did_open(open_params)
        assert self.handlers.semantic_model is not None

        # 2. Test hover on module name
        hover_result = self.handlers.handle_hover({
            "textDocument": {"uri": "file:///workflow_test.sv"},
            "position": {"line": 1, "character": 15}
        })

        # 3. Test workspace symbol search
        symbol_result = self.handlers.handle_workspace_symbol({"query": "counter"})
        assert isinstance(symbol_result, list)

        # 4. Test find references
        ref_result = self.handlers.handle_find_references({
            "textDocument": {"uri": "file:///workflow_test.sv"},
            "position": {"line": 6, "character": 10},
            "context": {"includeDeclaration": True}
        })
        assert isinstance(ref_result, list)

    def test_multiple_documents(self):
        """Test handling multiple documents."""
        # Open first document
        self.handlers.handle_text_document_did_open({
            "textDocument": {
                "uri": "file:///doc1.sv",
                "text": "module doc1; logic signal1; endmodule"
            }
        })

        # Open second document (should replace the first in this simple implementation)
        self.handlers.handle_text_document_did_open({
            "textDocument": {
                "uri": "file:///doc2.sv",
                "text": "module doc2; logic signal2; endmodule"
            }
        })

        # Should have semantic model for the latest document
        assert self.handlers.semantic_model is not None


if __name__ == "__main__":
    pytest.main([__file__])
