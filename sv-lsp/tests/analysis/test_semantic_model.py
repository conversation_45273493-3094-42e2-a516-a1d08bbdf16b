#!/usr/bin/env python3
"""
Test script for the enhanced semantic model functionality.
"""

import sys
import logging
from pathlib import Path

# Enable info logging
logging.basicConfig(level=logging.INFO)

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from sv_lsp.analysis.symbol_analyzer import SymbolAnalyzer
    from sv_lsp.core.semantic_model import SemanticModel, SyntaxNode
    from sv_lsp.core.types import SymbolKind, Location
    from sv_lsp.lsp.enhanced_handlers import EnhancedLSPHandlers

    print("🎯 Enhanced Semantic Model Demo")
    print("=" * 50)

    # Test SystemVerilog code with hierarchical structure
    test_code = """
module cpu_top(
    input logic clk,
    input logic reset,
    output logic [31:0] data_out
);
    // Internal signals
    logic [31:0] instruction;
    logic [31:0] pc;
    logic [31:0] alu_result;

    // Parameters
    parameter DATA_WIDTH = 32;
    parameter ADDR_WIDTH = 16;

    // CPU core instance
    cpu_core core_inst (
        .clk(clk),
        .reset(reset),
        .instruction(instruction),
        .pc(pc),
        .alu_result(alu_result)
    );

    // Memory interface
    memory_interface mem_if (
        .clk(clk),
        .addr(pc[ADDR_WIDTH-1:0]),
        .data_out(instruction)
    );

    assign data_out = alu_result;

endmodule

module cpu_core(
    input logic clk,
    input logic reset,
    input logic [31:0] instruction,
    output logic [31:0] pc,
    output logic [31:0] alu_result
);
    logic [31:0] register_file [0:31];
    logic [4:0] rs1, rs2, rd;

    always_ff @(posedge clk) begin
        if (reset) begin
            pc <= 0;
        end else begin
            pc <= pc + 4;
        end
    end

    // ALU operation
    always_comb begin
        alu_result = register_file[rs1] + register_file[rs2];
    end

endmodule
"""

    print("📝 Test SystemVerilog Code:")
    print("- 2 modules (cpu_top, cpu_core)")
    print("- Multiple signals and parameters")
    print("- Hierarchical instantiation")
    print("- Cross-module references")
    print()

    # Create symbol analyzer
    analyzer = SymbolAnalyzer()

    print("🔍 Extracting symbols with semantic analysis...")
    symbols = analyzer.analyze_text(test_code, "test_cpu.sv")
    semantic_model = analyzer.get_semantic_model()

    print(f"✅ Extracted {len(symbols)} symbols")
    print()

    # Display symbols with hierarchy
    print("📊 Symbol Hierarchy:")
    for symbol in symbols:
        hierarchy = semantic_model.get_symbol_hierarchy(symbol)
        indent = "  " if hierarchy.get("parent") else ""
        print(f"{indent}- {symbol.name} ({symbol.kind.value})")
        if hierarchy.get("parent"):
            print(f"    Parent: {hierarchy['parent']}")
        children = hierarchy.get("children", [])
        if children:
            print(f"    Children: {len(children)}")
        references = hierarchy.get("references", [])
        if references:
            print(f"    References: {len(references)}")
    print()

    # Test position-based symbol lookup
    print("🎯 Position-based Symbol Lookup:")
    test_positions = [
        (1, 7),   # "cpu_top" module name
        (6, 11),  # "instruction" signal
        (13, 14), # "DATA_WIDTH" parameter
        (17, 4),  # "cpu_core" instance type
        (30, 7),  # "cpu_core" module name
    ]

    for line, col in test_positions:
        symbol = semantic_model.find_symbol_at_position("test_cpu.sv", line, col)
        if symbol:
            print(f"  Position ({line}, {col}): {symbol.name} ({symbol.kind.value})")
        else:
            print(f"  Position ({line}, {col}): No symbol found")
    print()

    # Test reference analysis
    print("🔗 Reference Analysis:")
    for symbol in symbols[:5]:  # Show first 5 symbols
        references = semantic_model.get_symbol_references(symbol)
        if references:
            print(f"  {symbol.name}: {len(references)} references")
            for ref in list(references)[:3]:  # Show first 3 references
                print(f"    - Line {ref.line}, Column {ref.column}")
    print()

    # Test enhanced LSP handlers
    print("🚀 Enhanced LSP Handlers Demo:")
    handlers = EnhancedLSPHandlers()

    # Simulate document open
    open_params = {
        "textDocument": {
            "uri": "file://test_cpu.sv",
            "text": test_code
        }
    }
    handlers.handle_text_document_did_open(open_params)

    # Test go-to-definition
    def_params = {
        "textDocument": {"uri": "file://test_cpu.sv"},
        "position": {"line": 6, "character": 11}  # "instruction" signal
    }
    definition = handlers.handle_goto_definition(def_params)
    if definition:
        print(f"  Go-to-definition: Found at line {definition['range']['start']['line']}")

    # Test find references
    ref_params = {
        "textDocument": {"uri": "file://test_cpu.sv"},
        "position": {"line": 6, "character": 11},  # "instruction" signal
        "context": {"includeDeclaration": True}
    }
    references = handlers.handle_find_references(ref_params)
    print(f"  Find references: Found {len(references)} references")

    # Test hover
    hover_params = {
        "textDocument": {"uri": "file://test_cpu.sv"},
        "position": {"line": 1, "character": 7}  # "cpu_top" module
    }
    hover = handlers.handle_hover(hover_params)
    if hover:
        print(f"  Hover: Generated {len(hover['contents']['value'])} characters of info")

    # Test workspace symbol search
    symbol_params = {"query": "cpu"}
    workspace_symbols = handlers.handle_workspace_symbol(symbol_params)
    print(f"  Workspace symbols: Found {len(workspace_symbols)} symbols matching 'cpu'")
    print()

    # Display semantic model statistics
    print("📈 Semantic Model Statistics:")
    stats = semantic_model.get_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    print()

    # Test bidirectional mapping
    print("🔄 Bidirectional Mapping Test:")
    for symbol in symbols[:3]:  # Test first 3 symbols
        syntax_node = semantic_model.get_syntax_node(symbol)
        if syntax_node:
            print(f"  {symbol.name} -> SyntaxNode({syntax_node.kind})")
            back_symbol = semantic_model.get_declared_symbol(syntax_node)
            if back_symbol and back_symbol.name == symbol.name:
                print(f"    ✅ Bidirectional mapping verified")
            else:
                print(f"    ❌ Bidirectional mapping failed")
        else:
            print(f"  {symbol.name} -> No syntax node found")
    print()

    print("🎉 Enhanced Semantic Model Demo Complete!")
    print()
    print("Key Features Demonstrated:")
    print("✅ Bidirectional syntax node <-> symbol mapping")
    print("✅ Hierarchical symbol relationships")
    print("✅ Reference tracking and analysis")
    print("✅ Position-based symbol lookup")
    print("✅ Enhanced LSP capabilities")
    print("✅ Semantic ranking and relevance")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
