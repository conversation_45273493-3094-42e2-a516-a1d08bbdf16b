"""
Tests for PositionMapper class.
"""

import pytest
from sv_lsp.core.position_mapper import PositionMapper
from sv_lsp.core.types import SymbolInfo, SymbolKind, Location, Range, FileIndex


class TestPositionMapper:
    """Test PositionMapper class."""

    def test_position_mapper_creation(self):
        """Test creating a position mapper."""
        mapper = PositionMapper()

        assert isinstance(mapper.file_indices, dict)
        assert isinstance(mapper.line_index, dict)
        assert isinstance(mapper.range_tree, dict)

    def test_add_file_index(self):
        """Test adding a file index."""
        mapper = PositionMapper()

        # Create test symbols and ranges
        location1 = Location(file_path="/test/file.sv", line=5, column=10)
        symbol1 = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location1
        )

        location2 = Location(file_path="/test/file.sv", line=10, column=5)
        symbol2 = SymbolInfo(
            name="test_module",
            kind=SymbolKind.MODULE,
            definition_location=location2
        )

        # Create ranges
        range1 = Range(
            start=Location(file_path="/test/file.sv", line=5, column=10),
            end=Location(file_path="/test/file.sv", line=5, column=20)
        )

        range2 = Range(
            start=Location(file_path="/test/file.sv", line=10, column=5),
            end=Location(file_path="/test/file.sv", line=15, column=10)
        )

        # Create file index
        file_index = FileIndex(file_path="/test/file.sv")
        file_index.add_symbol(symbol1, range1)
        file_index.add_symbol(symbol2, range2)

        # Add to mapper
        mapper.add_file_index(file_index)

        # Verify file index was added
        assert "/test/file.sv" in mapper.file_indices
        assert mapper.file_indices["/test/file.sv"] == file_index

        # Verify line index was built
        assert "/test/file.sv" in mapper.line_index
        assert 5 in mapper.line_index["/test/file.sv"]
        assert 10 in mapper.line_index["/test/file.sv"]
        assert symbol1 in mapper.line_index["/test/file.sv"][5]
        assert symbol2 in mapper.line_index["/test/file.sv"][10]

        # Verify range tree was built
        assert "/test/file.sv" in mapper.range_tree
        assert len(mapper.range_tree["/test/file.sv"]) == 2

    def test_find_symbol_at_position(self):
        """Test finding symbol at position."""
        mapper = PositionMapper()

        # Create test symbol and range
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )

        range_info = Range(
            start=Location(file_path="/test/file.sv", line=5, column=10),
            end=Location(file_path="/test/file.sv", line=5, column=20)
        )

        # Create and add file index
        file_index = FileIndex(file_path="/test/file.sv")
        file_index.add_symbol(symbol, range_info)
        mapper.add_file_index(file_index)

        # Test finding symbol within range
        found = mapper.find_symbol_at_position("/test/file.sv", 5, 15)
        assert found == symbol

        # Test finding symbol at start of range
        found = mapper.find_symbol_at_position("/test/file.sv", 5, 10)
        assert found == symbol

        # Test finding symbol at end of range
        found = mapper.find_symbol_at_position("/test/file.sv", 5, 20)
        assert found == symbol

        # Test not finding symbol outside range
        found = mapper.find_symbol_at_position("/test/file.sv", 5, 25)
        assert found is None

        # Test not finding symbol in different file
        found = mapper.find_symbol_at_position("/other/file.sv", 5, 15)
        assert found is None

    def test_find_symbols_on_line(self):
        """Test finding symbols on a specific line."""
        mapper = PositionMapper()

        # Create test symbols
        location1 = Location(file_path="/test/file.sv", line=5, column=10)
        symbol1 = SymbolInfo(
            name="signal1",
            kind=SymbolKind.VARIABLE,
            definition_location=location1
        )

        location2 = Location(file_path="/test/file.sv", line=5, column=20)
        symbol2 = SymbolInfo(
            name="signal2",
            kind=SymbolKind.VARIABLE,
            definition_location=location2
        )

        location3 = Location(file_path="/test/file.sv", line=10, column=5)
        symbol3 = SymbolInfo(
            name="module1",
            kind=SymbolKind.MODULE,
            definition_location=location3
        )

        # Create ranges
        range1 = Range(
            start=Location(file_path="/test/file.sv", line=5, column=10),
            end=Location(file_path="/test/file.sv", line=5, column=15)
        )

        range2 = Range(
            start=Location(file_path="/test/file.sv", line=5, column=20),
            end=Location(file_path="/test/file.sv", line=5, column=25)
        )

        range3 = Range(
            start=Location(file_path="/test/file.sv", line=10, column=5),
            end=Location(file_path="/test/file.sv", line=12, column=10)
        )

        # Create and add file index
        file_index = FileIndex(file_path="/test/file.sv")
        file_index.add_symbol(symbol1, range1)
        file_index.add_symbol(symbol2, range2)
        file_index.add_symbol(symbol3, range3)
        mapper.add_file_index(file_index)

        # Find symbols on line 5
        symbols_line5 = mapper.find_symbols_on_line("/test/file.sv", 5)
        assert len(symbols_line5) == 2
        assert symbol1 in symbols_line5
        assert symbol2 in symbols_line5

        # Find symbols on line 10
        symbols_line10 = mapper.find_symbols_on_line("/test/file.sv", 10)
        assert len(symbols_line10) == 1
        assert symbol3 in symbols_line10

        # Find symbols on empty line
        symbols_empty = mapper.find_symbols_on_line("/test/file.sv", 20)
        assert len(symbols_empty) == 0

    def test_find_symbols_in_line_range(self):
        """Test finding symbols in a range of lines."""
        mapper = PositionMapper()

        # Create file index once
        file_index = FileIndex(file_path="/test/file.sv")

        # Create test symbols on different lines
        symbols = []
        for i in range(5):
            location = Location(file_path="/test/file.sv", line=i * 2, column=10)
            symbol = SymbolInfo(
                name=f"symbol{i}",
                kind=SymbolKind.VARIABLE,
                definition_location=location
            )
            symbols.append(symbol)

            range_info = Range(
                start=Location(file_path="/test/file.sv", line=i * 2, column=10),
                end=Location(file_path="/test/file.sv", line=i * 2, column=20)
            )

            file_index.add_symbol(symbol, range_info)

        # Add file index to mapper once
        mapper.add_file_index(file_index)

        # Find symbols in range 0-4 (should include symbols on lines 0, 2, 4)
        symbols_in_range = mapper.find_symbols_in_line_range("/test/file.sv", 0, 4)
        assert len(symbols_in_range) == 3
        assert symbols[0] in symbols_in_range  # line 0
        assert symbols[1] in symbols_in_range  # line 2
        assert symbols[2] in symbols_in_range  # line 4

    def test_remove_file(self):
        """Test removing a file from the mapper."""
        mapper = PositionMapper()

        # Create and add file index
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )

        file_index = FileIndex(file_path="/test/file.sv")
        file_index.add_symbol(symbol)
        mapper.add_file_index(file_index)

        # Verify file was added
        assert "/test/file.sv" in mapper.file_indices

        # Remove file
        success = mapper.remove_file("/test/file.sv")
        assert success

        # Verify file was removed
        assert "/test/file.sv" not in mapper.file_indices

        # Try to remove non-existent file
        success = mapper.remove_file("/nonexistent/file.sv")
        assert not success

    def test_incremental_update(self):
        """Test incremental file updates."""
        mapper = PositionMapper()

        # Create and add file index
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )

        file_index = FileIndex(file_path="/test/file.sv")
        file_index.add_symbol(symbol)
        mapper.add_file_index(file_index)

        # Verify initial state
        assert "/test/file.sv" in mapper.line_index

        # Update incrementally
        changes = [{"line": 5, "character": 10, "text": "new_signal"}]
        mapper.update_file_incrementally("/test/file.sv", changes)

        # Verify update was processed (indices should still exist)
        assert "/test/file.sv" in mapper.line_index

    def test_invalidate_file_cache(self):
        """Test invalidating file cache."""
        mapper = PositionMapper()

        # Create and add file index
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )

        file_index = FileIndex(file_path="/test/file.sv")
        file_index.add_symbol(symbol)
        mapper.add_file_index(file_index)

        # Verify cache exists
        assert "/test/file.sv" in mapper.line_index
        assert "/test/file.sv" in mapper.range_tree

        # Invalidate cache
        mapper.invalidate_file_cache("/test/file.sv")

        # Verify cache was cleared
        assert "/test/file.sv" not in mapper.line_index
        assert "/test/file.sv" not in mapper.range_tree

        # File index should still exist
        assert "/test/file.sv" in mapper.file_indices

    def test_clear(self):
        """Test clearing all position mapping data."""
        mapper = PositionMapper()

        # Add some data
        location = Location(file_path="/test/file.sv", line=5, column=10)
        symbol = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location
        )

        file_index = FileIndex(file_path="/test/file.sv")
        file_index.add_symbol(symbol)
        mapper.add_file_index(file_index)

        # Verify data exists
        assert len(mapper.file_indices) > 0
        assert len(mapper.line_index) > 0
        assert len(mapper.range_tree) > 0

        # Clear and verify empty
        mapper.clear()

        assert len(mapper.file_indices) == 0
        assert len(mapper.line_index) == 0
        assert len(mapper.range_tree) == 0
