"""
Performance tests for SystemVerilog LSP server.

These tests measure and validate the performance characteristics
of the LSP server components.
"""

import pytest
import time
from sv_lsp.core.symbol_manager import <PERSON>ym<PERSON><PERSON>anager
from sv_lsp.core.position_mapper import PositionMapper
from sv_lsp.core.types import <PERSON><PERSON>bolInfo, SymbolKind, Location, Range, FileIndex
from sv_lsp.utils.performance import (
    PerformanceMonitor, Timer, timed, benchmark_function, performance_monitor
)


class TestPerformanceMonitoring:
    """Test performance monitoring utilities."""

    def test_performance_monitor_basic(self):
        """Test basic performance monitor functionality."""
        monitor = PerformanceMonitor()

        # Test measurement
        monitor.measure("test_operation", 0.1)
        monitor.measure("test_operation", 0.2)

        # Check metric
        metric = monitor.get_metric("test_operation")
        assert metric is not None
        assert metric.call_count == 2
        assert abs(metric.total_time - 0.3) < 1e-10  # Handle floating point precision
        assert abs(metric.average_time - 0.15) < 1e-10  # Handle floating point precision
        assert metric.min_time == 0.1
        assert metric.max_time == 0.2

    def test_timer_context_manager(self):
        """Test Timer context manager."""
        monitor = PerformanceMonitor()

        with Timer("test_timer", monitor=monitor):
            time.sleep(0.01)  # Small delay

        metric = monitor.get_metric("test_timer")
        assert metric is not None
        assert metric.call_count == 1
        assert metric.total_time > 0.005  # Should be at least 5ms

    def test_timed_decorator(self):
        """Test timed decorator."""
        # Use global monitor for decorator test
        performance_monitor.clear()

        @timed("test_function")
        def test_func():
            time.sleep(0.01)
            return "result"

        result = test_func()
        assert result == "result"

        metric = performance_monitor.get_metric("test_function")
        assert metric is not None
        assert metric.call_count == 1
        assert metric.total_time > 0.005

    def test_benchmark_function(self):
        """Test function benchmarking."""
        def simple_function(x, y):
            return x + y

        results = benchmark_function(simple_function, 1, 2, iterations=100)

        assert results["iterations"] == 100
        assert results["total_time"] > 0
        assert results["average_time"] > 0
        assert results["min_time"] >= 0
        assert results["max_time"] >= results["min_time"]
        assert results["median_time"] >= 0


class TestSymbolManagerPerformance:
    """Test SymbolManager performance."""

    def test_symbol_indexing_performance(self):
        """Test performance of symbol indexing."""
        manager = SymbolManager()

        # Create many symbols
        symbols = []
        for i in range(1000):
            location = Location(file_path=f"/test/file{i % 10}.sv", line=i % 100, column=10)
            symbol = SymbolInfo(
                name=f"symbol_{i}",
                kind=SymbolKind.VARIABLE if i % 2 == 0 else SymbolKind.MODULE,
                definition_location=location,
                type_info=f"logic [{i % 32}:0]"
            )
            symbols.append(symbol)

        # Benchmark adding symbols
        start_time = time.perf_counter()
        for symbol in symbols:
            manager._add_symbol_to_indices(symbol)
        end_time = time.perf_counter()

        indexing_time = end_time - start_time
        print(f"Indexed {len(symbols)} symbols in {indexing_time:.4f}s")
        print(f"Average time per symbol: {indexing_time / len(symbols) * 1000:.2f}ms")

        # Verify indexing worked
        assert len(manager.global_symbols) == 1000
        assert len(manager.name_index) == 1000

        # Test search performance
        search_results = benchmark_function(
            manager.find_symbols_by_kind, SymbolKind.VARIABLE, iterations=100
        )
        print(f"Symbol search average time: {search_results['average_time'] * 1000:.2f}ms")

        # Should be fast (under 10ms average)
        assert search_results['average_time'] < 0.01

    def test_prefix_search_performance(self):
        """Test performance of prefix-based symbol search."""
        manager = SymbolManager()

        # Create symbols with various prefixes
        prefixes = ["test", "signal", "module", "interface", "class"]
        for prefix in prefixes:
            for i in range(200):
                location = Location(file_path="/test/file.sv", line=i, column=10)
                symbol = SymbolInfo(
                    name=f"{prefix}_{i}",
                    kind=SymbolKind.VARIABLE,
                    definition_location=location
                )
                manager._add_symbol_to_indices(symbol)

        # Benchmark prefix search
        search_results = benchmark_function(
            manager.find_symbols_by_prefix, "test", iterations=100
        )

        print(f"Prefix search average time: {search_results['average_time'] * 1000:.2f}ms")

        # Should be very fast (under 5ms average)
        assert search_results['average_time'] < 0.005

        # Verify results (limited by default limit of 50)
        results = manager.find_symbols_by_prefix("test")
        assert len(results) == 50  # Default limit

        # Test with higher limit
        results_unlimited = manager.find_symbols_by_prefix("test", limit=300)
        assert len(results_unlimited) == 200


class TestPositionMapperPerformance:
    """Test PositionMapper performance."""

    def test_position_lookup_performance(self):
        """Test performance of position-based symbol lookup."""
        mapper = PositionMapper()

        # Create file index with many symbols
        file_index = FileIndex(file_path="/test/large_file.sv")

        symbols = []
        for i in range(1000):
            location = Location(file_path="/test/large_file.sv", line=i, column=10)
            symbol = SymbolInfo(
                name=f"symbol_{i}",
                kind=SymbolKind.VARIABLE,
                definition_location=location
            )
            symbols.append(symbol)

            range_info = Range(
                start=Location(file_path="/test/large_file.sv", line=i, column=10),
                end=Location(file_path="/test/large_file.sv", line=i, column=20)
            )

            file_index.add_symbol(symbol, range_info)

        # Add to mapper
        mapper.add_file_index(file_index)

        # Benchmark position lookup
        def lookup_random_position():
            import random
            line = random.randint(0, 999)
            return mapper.find_symbol_at_position("/test/large_file.sv", line, 15)

        lookup_results = benchmark_function(lookup_random_position, iterations=100)

        print(f"Position lookup average time: {lookup_results['average_time'] * 1000:.2f}ms")

        # Should be fast (under 5ms average)
        assert lookup_results['average_time'] < 0.005

    def test_range_tree_performance(self):
        """Test performance of range tree operations."""
        mapper = PositionMapper()

        # Create overlapping ranges
        file_index = FileIndex(file_path="/test/file.sv")

        # Create nested symbols (like modules containing signals)
        for module_i in range(10):
            # Module range
            module_start = Location(file_path="/test/file.sv", line=module_i * 100, column=0)
            module_end = Location(file_path="/test/file.sv", line=(module_i + 1) * 100 - 1, column=50)
            module_range = Range(start=module_start, end=module_end)

            module_symbol = SymbolInfo(
                name=f"module_{module_i}",
                kind=SymbolKind.MODULE,
                definition_location=module_start
            )

            file_index.add_symbol(module_symbol, module_range)

            # Signals within module
            for signal_i in range(50):
                signal_line = module_i * 100 + signal_i + 10
                signal_location = Location(file_path="/test/file.sv", line=signal_line, column=4)
                signal_range = Range(
                    start=signal_location,
                    end=Location(file_path="/test/file.sv", line=signal_line, column=20)
                )

                signal_symbol = SymbolInfo(
                    name=f"signal_{module_i}_{signal_i}",
                    kind=SymbolKind.VARIABLE,
                    definition_location=signal_location
                )

                file_index.add_symbol(signal_symbol, signal_range)

        # Add to mapper
        start_time = time.perf_counter()
        mapper.add_file_index(file_index)
        end_time = time.perf_counter()

        build_time = end_time - start_time
        print(f"Built range tree in {build_time:.4f}s")

        # Test lookup in nested ranges
        def lookup_nested():
            # Look for signal inside a module
            return mapper.find_symbol_at_position("/test/file.sv", 55, 10)

        nested_results = benchmark_function(lookup_nested, iterations=100)

        print(f"Nested lookup average time: {nested_results['average_time'] * 1000:.2f}ms")

        # Should handle nested ranges efficiently
        assert nested_results['average_time'] < 0.01


class TestIntegratedPerformance:
    """Test integrated performance scenarios."""

    def test_large_file_processing(self):
        """Test processing a large file with many symbols."""
        manager = SymbolManager()
        mapper = PositionMapper()

        # Simulate a large file with many symbols
        file_index = FileIndex(file_path="/test/large_project.sv")

        # Create hierarchical structure
        num_modules = 20
        signals_per_module = 100

        start_time = time.perf_counter()

        for module_i in range(num_modules):
            # Module
            module_location = Location(
                file_path="/test/large_project.sv",
                line=module_i * (signals_per_module + 10),
                column=0
            )
            module_symbol = SymbolInfo(
                name=f"module_{module_i}",
                kind=SymbolKind.MODULE,
                definition_location=module_location
            )

            manager._add_symbol_to_indices(module_symbol)
            file_index.add_symbol(module_symbol)

            # Signals in module
            for signal_i in range(signals_per_module):
                signal_location = Location(
                    file_path="/test/large_project.sv",
                    line=module_i * (signals_per_module + 10) + signal_i + 5,
                    column=4
                )
                signal_symbol = SymbolInfo(
                    name=f"signal_{module_i}_{signal_i}",
                    kind=SymbolKind.VARIABLE,
                    definition_location=signal_location,
                    parent=module_symbol
                )

                manager._add_symbol_to_indices(signal_symbol)

                signal_range = Range(
                    start=signal_location,
                    end=Location(
                        file_path="/test/large_project.sv",
                        line=signal_location.line,
                        column=signal_location.column + 15
                    )
                )
                file_index.add_symbol(signal_symbol, signal_range)

        # Add to position mapper
        mapper.add_file_index(file_index)

        end_time = time.perf_counter()
        total_time = end_time - start_time

        total_symbols = num_modules + (num_modules * signals_per_module)
        print(f"Processed {total_symbols} symbols in {total_time:.4f}s")
        print(f"Average time per symbol: {total_time / total_symbols * 1000:.2f}ms")

        # Verify processing was successful
        assert len(manager.global_symbols) == total_symbols

        # Test search performance on large dataset
        search_time = time.perf_counter()
        results = manager.search_symbols("signal_5")
        search_end = time.perf_counter()

        print(f"Search found {len(results)} results in {(search_end - search_time) * 1000:.2f}ms")

        # Should handle large datasets efficiently (relaxed timing for CI)
        assert total_time < 10.0  # Should process 2000+ symbols in under 10 seconds
        assert (search_end - search_time) < 0.1  # Search should be under 100ms
