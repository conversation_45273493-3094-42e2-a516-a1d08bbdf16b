# SystemVerilog LSP Tests

This directory contains the comprehensive test suite for the SystemVerilog LSP server.

## 📁 Test Structure

```
tests/
├── unit/                   # Unit tests for individual components
│   ├── test_types.py      # Core type system tests
│   ├── test_position_mapper.py  # Position mapping tests
│   ├── test_helpers.py    # Utility function tests
│   └── test_symbol_kind_mapper.py  # Symbol kind mapping tests
├── integration/           # Integration tests
│   ├── test_integration.py  # End-to-end integration tests
│   └── test_symbol_manager.py  # Symbol manager integration
├── analysis/              # Symbol analysis tests
│   ├── test_symbol_analyzer.py  # Main symbol analyzer tests
│   ├── test_analyzer_comparison.py  # Analyzer comparison tests
│   ├── test_comprehensive_symbols.py  # Complete symbol support tests
│   └── test_semantic_model.py  # Semantic model tests
├── lsp/                   # LSP protocol tests
│   └── test_enhanced_handlers.py  # Enhanced LSP handler tests
├── performance/           # Performance and benchmark tests
│   └── test_performance.py  # Performance benchmarks
├── utils/                 # Utility and debugging tests
│   ├── test_fix.py       # Quick fix verification
│   └── debug_pyslang.py  # pyslang debugging utilities
├── conftest.py           # Pytest configuration and fixtures
└── README.md            # This file
```

## 🚀 Running Tests

### Quick Start

```bash
# Run all tests
python run_tests.py

# Run specific test category
python run_tests.py --category unit
python run_tests.py --category integration
python run_tests.py --category analysis
```

### Using pytest directly

```bash
# Set PYTHONPATH and run all tests
PYTHONPATH=src python -m pytest tests/

# Run specific test file
PYTHONPATH=src python -m pytest tests/unit/test_symbol_analyzer.py

# Run with verbose output
PYTHONPATH=src python -m pytest tests/ -v

# Run with coverage
PYTHONPATH=src python -m pytest tests/ --cov=sv_lsp --cov-report=html
```

### Test Categories

#### Unit Tests (`tests/unit/`)
- **Purpose**: Test individual components in isolation
- **Speed**: Fast (< 1 second per test)
- **Dependencies**: Minimal external dependencies
- **Examples**:
  - Type system validation
  - Position mapping algorithms
  - Symbol kind conversions

#### Integration Tests (`tests/integration/`)
- **Purpose**: Test component interactions
- **Speed**: Medium (1-10 seconds per test)
- **Dependencies**: Multiple components working together
- **Examples**:
  - Symbol manager with file system
  - LSP handlers with symbol analysis
  - End-to-end workflows

#### Analysis Tests (`tests/analysis/`)
- **Purpose**: Test symbol analysis capabilities
- **Speed**: Medium to slow (1-30 seconds per test)
- **Dependencies**: pyslang (optional)
- **Examples**:
  - Symbol extraction from complex code
  - Semantic model construction
  - Reference analysis

#### LSP Tests (`tests/lsp/`)
- **Purpose**: Test LSP protocol compliance
- **Speed**: Medium (1-5 seconds per test)
- **Dependencies**: LSP handlers and symbol analysis
- **Examples**:
  - Go-to-definition requests
  - Find references functionality
  - Hover information generation

#### Performance Tests (`tests/performance/`)
- **Purpose**: Benchmark and performance validation
- **Speed**: Slow (10+ seconds per test)
- **Dependencies**: Large test files, timing measurements
- **Examples**:
  - Large file processing speed
  - Memory usage patterns
  - Scalability tests

## 🧪 Test Fixtures

The test suite provides several useful fixtures defined in `conftest.py`:

### File Fixtures
- `temp_dir`: Temporary directory for test files
- `sample_sv_file`: Basic SystemVerilog test file
- `sample_workspace`: Multi-file workspace structure
- `complex_sv_code`: Complex SystemVerilog code string

### Object Fixtures
- `symbol_analyzer`: SymbolAnalyzer instance
- `semantic_model`: SemanticModel instance
- `sample_location`: Sample Location object
- `sample_symbol`: Sample SymbolInfo object

### Usage Example

```python
def test_symbol_analysis(symbol_analyzer, complex_sv_code):
    """Test symbol analysis with complex code."""
    symbols = symbol_analyzer.analyze_text(complex_sv_code, "test.sv")
    assert len(symbols) > 0
```

## 🏷️ Test Markers

Tests are categorized using pytest markers:

- `@pytest.mark.unit`: Unit tests
- `@pytest.mark.integration`: Integration tests
- `@pytest.mark.analysis`: Analysis tests
- `@pytest.mark.lsp`: LSP protocol tests
- `@pytest.mark.performance`: Performance tests
- `@pytest.mark.slow`: Tests that take > 1 second
- `@pytest.mark.requires_pyslang`: Tests requiring pyslang

### Running Specific Markers

```bash
# Run only unit tests
PYTHONPATH=src python -m pytest -m unit

# Run only fast tests
PYTHONPATH=src python -m pytest -m "not slow"

# Run tests that don't require pyslang
PYTHONPATH=src python -m pytest -m "not requires_pyslang"
```

## 📊 Coverage Reporting

### Generate Coverage Report

```bash
# Terminal report
python run_tests.py --coverage

# HTML report
python run_tests.py --coverage --html-report

# View HTML report
open htmlcov/index.html
```

### Coverage Targets

- **Overall**: 80%+ line coverage
- **Core modules**: 90%+ line coverage
- **Critical paths**: 95%+ line coverage

## 🔧 Test Configuration

### pytest.ini
- Test discovery patterns
- Marker definitions
- Default options
- Timeout settings

### .coveragerc
- Coverage measurement settings
- File inclusion/exclusion rules
- Reporting options
- Failure thresholds

## 🐛 Debugging Tests

### Running Individual Tests

```bash
# Run single test with full output
PYTHONPATH=src python -m pytest tests/unit/test_symbol_analyzer.py::TestSymbolAnalyzer::test_simple_module_analysis -v -s

# Run with debugger
PYTHONPATH=src python -m pytest tests/unit/test_symbol_analyzer.py --pdb
```

### Debug Utilities

```bash
# Test pyslang availability
PYTHONPATH=src python tests/utils/debug_pyslang.py

# Quick functionality check
PYTHONPATH=src python tests/utils/test_fix.py
```

## 📈 Performance Testing

### Benchmarking

```bash
# Run performance tests only
python run_tests.py --category performance

# Run with benchmark reporting
python run_tests.py --benchmark
```

### Performance Targets

- **Small files** (< 1KB): < 100ms analysis time
- **Medium files** (1-10KB): < 500ms analysis time
- **Large files** (10-100KB): < 2s analysis time
- **Memory usage**: < 100MB for typical projects

## 🚨 Continuous Integration

### GitHub Actions

Tests are automatically run on:
- Pull requests
- Main branch commits
- Release tags

### Test Matrix

- **Python versions**: 3.8, 3.9, 3.10, 3.11
- **Operating systems**: Ubuntu, macOS, Windows
- **Dependencies**: With/without pyslang

## 📝 Writing New Tests

### Test Naming Convention

```python
# File naming
test_<component>.py

# Class naming
class Test<Component>:

# Method naming
def test_<functionality>_<scenario>(self):
```

### Test Structure

```python
def test_functionality_scenario(fixture1, fixture2):
    """Test description.
    
    This test verifies that functionality works correctly
    under specific scenario conditions.
    """
    # Arrange
    setup_data = create_test_data()
    
    # Act
    result = component.method(setup_data)
    
    # Assert
    assert result.is_valid()
    assert len(result.items) == expected_count
```

### Best Practices

1. **Use descriptive test names**
2. **Test one thing per test**
3. **Use fixtures for common setup**
4. **Add appropriate markers**
5. **Include docstrings for complex tests**
6. **Mock external dependencies**
7. **Test both success and failure cases**

## 🔍 Test Analysis

### Test Metrics

```bash
# Test count by category
find tests/ -name "test_*.py" | wc -l

# Test execution time
python run_tests.py --verbose | grep "seconds"

# Coverage by module
python run_tests.py --coverage | grep "src/sv_lsp"
```

### Quality Metrics

- **Test coverage**: > 80%
- **Test speed**: Average < 1s per test
- **Test reliability**: > 99% pass rate
- **Test maintainability**: Clear, readable test code

This comprehensive test suite ensures the reliability, performance, and correctness of the SystemVerilog LSP server across all supported features and use cases.
