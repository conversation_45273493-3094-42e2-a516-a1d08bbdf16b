#!/usr/bin/env python3
"""
Debug script to test pyslang functionality.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    import pyslang
    print("✅ pyslang imported successfully")
    
    # Simple test code
    test_code = """
module test_module(
    input logic clk,
    output logic data
);
    logic internal_signal;
endmodule
"""
    
    print("🔍 Testing pyslang parsing...")
    tree = pyslang.SyntaxTree.fromText(test_code, "test.sv")
    print(f"✅ Parse tree created: {tree}")
    
    print(f"📊 Root node: {tree.root}")
    print(f"📊 Root kind: {tree.root.kind}")
    
    # Test child nodes
    print("🔍 Testing child nodes...")
    try:
        children = list(tree.root.childNodes())
        print(f"✅ Found {len(children)} children")
        
        for i, child in enumerate(children):
            print(f"  Child {i}: {child.kind}")
            
            # Test grandchildren
            try:
                grandchildren = list(child.childNodes())
                print(f"    Has {len(grandchildren)} grandchildren")
                for j, grandchild in enumerate(grandchildren[:3]):  # Show first 3
                    print(f"      Grandchild {j}: {grandchild.kind}")
            except Exception as e:
                print(f"    Error getting grandchildren: {e}")
                
    except Exception as e:
        print(f"❌ Error getting children: {e}")
    
    # Test visitor pattern
    print("🔍 Testing visitor pattern...")
    
    def visit_func(node):
        print(f"Visiting: {node.kind}")
        return True
    
    try:
        tree.root.visit(visit_func)
        print("✅ Visitor pattern works")
    except Exception as e:
        print(f"❌ Visitor pattern error: {e}")
    
    print("🎉 pyslang debug complete!")
    
except ImportError:
    print("❌ pyslang not available")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
