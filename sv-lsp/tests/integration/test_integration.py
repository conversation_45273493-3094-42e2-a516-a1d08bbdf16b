"""
Integration tests for SystemVerilog LSP server.

These tests verify that different components work together correctly.
"""

import pytest
from pathlib import Path
from sv_lsp.core.symbol_manager import SymbolManager
from sv_lsp.core.position_mapper import PositionMapper
from sv_lsp.core.workspace import Workspace<PERSON>anager
from sv_lsp.core.types import SymbolInfo, SymbolKind, Location, Range, FileIndex, WorkspaceConfig
from sv_lsp.lsp.handlers import LSPHandlers
from sv_lsp.lsp.server import SVLanguageServer


class TestIntegration:
    """Integration tests for LSP components."""
    
    def test_symbol_manager_position_mapper_integration(self):
        """Test integration between SymbolManager and PositionMapper."""
        # Create components
        symbol_manager = SymbolManager()
        position_mapper = PositionMapper()
        
        # Create test symbols
        location1 = Location(file_path="/test/file.sv", line=5, column=10)
        symbol1 = SymbolInfo(
            name="test_signal",
            kind=SymbolKind.VARIABLE,
            definition_location=location1,
            type_info="logic [7:0]"
        )
        
        location2 = Location(file_path="/test/file.sv", line=10, column=5)
        symbol2 = SymbolInfo(
            name="test_module",
            kind=SymbolKind.MODULE,
            definition_location=location2
        )
        
        # Add symbols to symbol manager
        symbol_manager._add_symbol_to_indices(symbol1)
        symbol_manager._add_symbol_to_indices(symbol2)
        
        # Create file index and add to position mapper
        file_index = FileIndex(file_path="/test/file.sv")
        
        range1 = Range(
            start=Location(file_path="/test/file.sv", line=5, column=10),
            end=Location(file_path="/test/file.sv", line=5, column=20)
        )
        
        range2 = Range(
            start=Location(file_path="/test/file.sv", line=10, column=5),
            end=Location(file_path="/test/file.sv", line=15, column=10)
        )
        
        file_index.add_symbol(symbol1, range1)
        file_index.add_symbol(symbol2, range2)
        position_mapper.add_file_index(file_index)
        
        # Test integration: find symbol by name in symbol manager
        found_symbol = symbol_manager.find_symbol("test_signal")
        assert found_symbol == symbol1
        
        # Test integration: find symbol by position in position mapper
        found_by_position = position_mapper.find_symbol_at_position("/test/file.sv", 5, 15)
        assert found_by_position == symbol1
        
        # Test integration: find symbols by kind
        variables = symbol_manager.find_symbols_by_kind(SymbolKind.VARIABLE)
        assert symbol1 in variables
        
        modules = symbol_manager.find_symbols_by_kind(SymbolKind.MODULE)
        assert symbol2 in modules
    
    def test_workspace_symbol_manager_integration(self):
        """Test integration between WorkspaceManager and SymbolManager."""
        # Create components
        config = WorkspaceConfig(
            include_paths=["/test/include"],
            file_extensions={".sv", ".svh"}
        )
        workspace = WorkspaceManager(config=config)
        symbol_manager = SymbolManager(config)
        
        # Test file type detection
        assert workspace._is_systemverilog_file("/test/file.sv")
        assert workspace._is_systemverilog_file("/test/header.svh")
        assert not workspace._is_systemverilog_file("/test/file.txt")
        
        # Test adding files to workspace
        success = workspace.add_file("/test/module.sv")
        assert success
        
        # Test workspace statistics
        stats = workspace.get_statistics()
        assert stats["total_files"] == 1
    
    def test_lsp_handlers_integration(self):
        """Test integration of LSP handlers with core components."""
        # Create components
        symbol_manager = SymbolManager()
        position_mapper = PositionMapper()
        workspace_manager = WorkspaceManager()
        
        # Create handlers with components
        handlers = LSPHandlers(
            symbol_manager=symbol_manager,
            position_mapper=position_mapper,
            workspace_manager=workspace_manager
        )
        
        # Verify handlers have access to components
        assert handlers.symbol_manager == symbol_manager
        assert handlers.position_mapper == position_mapper
        assert handlers.workspace_manager == workspace_manager
        
        # Test document open handling (should not crash)
        params = {
            "textDocument": {
                "uri": "file:///test/file.sv",
                "text": "module test; endmodule"
            }
        }
        
        # This should not raise an exception
        try:
            handlers.handle_text_document_did_open(params)
        except Exception as e:
            # Expected to fail due to file not existing, but should not crash
            assert "No such file" in str(e) or "cannot resolve" in str(e).lower()
    
    def test_symbol_hierarchy_integration(self):
        """Test symbol hierarchy and relationships."""
        symbol_manager = SymbolManager()
        
        # Create parent module
        parent_location = Location(file_path="/test/file.sv", line=1, column=0)
        parent_module = SymbolInfo(
            name="parent_module",
            kind=SymbolKind.MODULE,
            definition_location=parent_location
        )
        
        # Create child signals
        child1_location = Location(file_path="/test/file.sv", line=5, column=4)
        child1 = SymbolInfo(
            name="signal1",
            kind=SymbolKind.VARIABLE,
            definition_location=child1_location,
            type_info="logic"
        )
        
        child2_location = Location(file_path="/test/file.sv", line=6, column=4)
        child2 = SymbolInfo(
            name="signal2",
            kind=SymbolKind.VARIABLE,
            definition_location=child2_location,
            type_info="logic [7:0]"
        )
        
        # Build hierarchy
        parent_module.add_child(child1)
        parent_module.add_child(child2)
        
        # Add to symbol manager
        symbol_manager._add_symbol_to_indices(parent_module)
        symbol_manager._add_symbol_to_indices(child1)
        symbol_manager._add_symbol_to_indices(child2)
        
        # Test hierarchy
        assert child1.parent == parent_module
        assert child2.parent == parent_module
        assert child1 in parent_module.children
        assert child2 in parent_module.children
        
        # Test qualified names
        assert child1.qualified_name == "parent_module.signal1"
        assert child2.qualified_name == "parent_module.signal2"
        
        # Test scope-based search
        scope_symbols = symbol_manager.find_symbols_in_scope("parent_module")
        assert child1 in scope_symbols
        assert child2 in scope_symbols
    
    def test_symbol_relationships(self):
        """Test symbol relationship tracking."""
        symbol_manager = SymbolManager()
        
        # Add inheritance relationship
        symbol_manager.add_inheritance_relationship("child_class", "parent_class")
        
        # Add instantiation relationship
        symbol_manager.add_instantiation_relationship("cpu_inst", "cpu_module")
        
        # Add reference relationship
        symbol_manager.add_reference_relationship("function1", "variable1")
        
        # Test relationship queries
        assert "child_class" in symbol_manager.inheritance_map
        assert "parent_class" in symbol_manager.inheritance_map["child_class"]
        
        assert "cpu_inst" in symbol_manager.instantiation_map
        assert "cpu_module" in symbol_manager.instantiation_map["cpu_inst"]
        
        assert "function1" in symbol_manager.reference_map
        assert "variable1" in symbol_manager.reference_map["function1"]
    
    def test_search_functionality(self):
        """Test search functionality across components."""
        symbol_manager = SymbolManager()
        
        # Create various symbols
        symbols = [
            SymbolInfo(
                name="test_signal",
                kind=SymbolKind.VARIABLE,
                definition_location=Location("/test/file.sv", 5, 10),
                type_info="logic"
            ),
            SymbolInfo(
                name="test_module",
                kind=SymbolKind.MODULE,
                definition_location=Location("/test/file.sv", 1, 0)
            ),
            SymbolInfo(
                name="signal_test",
                kind=SymbolKind.VARIABLE,
                definition_location=Location("/test/file.sv", 10, 5),
                type_info="logic [7:0]"
            ),
            SymbolInfo(
                name="other_signal",
                kind=SymbolKind.VARIABLE,
                definition_location=Location("/test/file.sv", 15, 8),
                type_info="reg"
            )
        ]
        
        # Add all symbols
        for symbol in symbols:
            symbol_manager._add_symbol_to_indices(symbol)
        
        # Test prefix search
        test_prefix_results = symbol_manager.find_symbols_by_prefix("test")
        assert len(test_prefix_results) >= 2  # test_signal and test_module
        
        # Test kind-based search
        variables = symbol_manager.find_symbols_by_kind(SymbolKind.VARIABLE)
        assert len(variables) == 3  # All variables
        
        modules = symbol_manager.find_symbols_by_kind(SymbolKind.MODULE)
        assert len(modules) == 1  # One module
        
        # Test general search
        search_results = symbol_manager.search_symbols("signal")
        assert len(search_results) >= 2  # Should find multiple signals
    
    def test_lsp_server_initialization(self):
        """Test LSP server initialization and component integration."""
        # Create server
        server = SVLanguageServer(max_workers=2)
        
        # Verify components are initialized
        assert server.symbol_manager is not None
        assert server.position_mapper is not None
        assert server.workspace_manager is not None
        assert server.cache_manager is not None
        assert server.protocol is not None
        assert server.handlers is not None
        
        # Verify handlers have access to components
        assert server.handlers.symbol_manager == server.symbol_manager
        assert server.handlers.position_mapper == server.position_mapper
        assert server.handlers.workspace_manager == server.workspace_manager
        
        # Test capabilities
        capabilities = server.get_capabilities()
        assert "textDocumentSync" in capabilities
        assert "definitionProvider" in capabilities
        assert "referencesProvider" in capabilities
        assert "hoverProvider" in capabilities
        assert "documentSymbolProvider" in capabilities
        assert "workspaceSymbolProvider" in capabilities
        
        # Test cleanup
        server.clear = lambda: None  # Mock clear method to avoid async issues
        # server.clear()  # Would normally call this but it's async
