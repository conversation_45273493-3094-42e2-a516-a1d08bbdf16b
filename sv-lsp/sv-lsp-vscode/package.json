{"name": "systemverilog-lsp", "displayName": "SystemVerilog LSP", "description": "SystemVerilog Language Server Protocol support with advanced features", "version": "0.1.0", "publisher": "sv-lsp", "icon": "images/icon.svg", "engines": {"vscode": "^1.74.0"}, "categories": ["Programming Languages", "Linters", "Formatters", "Other"], "keywords": ["systemverilog", "verilog", "hdl", "hardware", "lsp", "language-server"], "main": "./out/extension.js", "contributes": {"languages": [{"id": "systemverilog", "aliases": ["SystemVerilog", "systemverilog", "SV"], "extensions": [".sv", ".svh", ".v", ".vh"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "systemverilog", "scopeName": "source.systemverilog", "path": "./syntaxes/systemverilog.tmGrammar.json"}], "configuration": {"type": "object", "title": "SystemVerilog LSP", "properties": {"systemverilog.lsp.enabled": {"type": "boolean", "default": true, "description": "Enable SystemVerilog Language Server"}, "systemverilog.lsp.serverPath": {"type": "string", "default": "", "description": "Path to SystemVerilog LSP server executable"}, "systemverilog.lsp.pythonPath": {"type": "string", "default": "python3", "description": "Path to Python interpreter for LSP server"}, "systemverilog.lsp.includePaths": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Include paths for SystemVerilog files"}, "systemverilog.lsp.defines": {"type": "object", "default": {}, "description": "Preprocessor defines"}, "systemverilog.lsp.trace.server": {"type": "string", "enum": ["off", "messages", "verbose"], "default": "off", "description": "Traces the communication between VS Code and the language server"}}}, "commands": [{"command": "systemverilog.restartServer", "title": "Restart SystemVerilog Language Server", "category": "SystemVerilog"}, {"command": "systemverilog.showOutputChannel", "title": "Show SystemVerilog Output", "category": "SystemVerilog"}]}, "activationEvents": ["onLanguage:systemverilog"], "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "vsce": "^2.15.0"}, "dependencies": {"vscode-languageclient": "^8.1.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/sv-lsp-vscode.git"}, "bugs": {"url": "https://github.com/your-username/sv-lsp-vscode/issues"}, "homepage": "https://github.com/your-username/sv-lsp-vscode#readme", "license": "MIT"}