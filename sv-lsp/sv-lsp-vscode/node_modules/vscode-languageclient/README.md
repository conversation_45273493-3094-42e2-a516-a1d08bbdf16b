# VSCode Language Server - Client Module

[![NPM Version](https://img.shields.io/npm/v/vscode-languageclient.svg)](https://npmjs.org/package/vscode-languageclient)
[![NPM Downloads](https://img.shields.io/npm/dm/vscode-languageclient.svg)](https://npmjs.org/package/vscode-languageclient)
[![Build Status](https://travis-ci.org/Microsoft/vscode-languageserver-node.svg?branch=master)](https://travis-ci.org/Microsoft/vscode-languageserver-node)

This npm module allows VSCode extensions to easily integrate language servers adhering to the [language server protocol](https://github.com/Microsoft/vscode-languageserver-protocol)

See [here](https://code.visualstudio.com/docs/extensions/example-language-server) for a detailed documentation on how to 
implement language servers for [VSCode](https://code.visualstudio.com/).

## History

For the history please see the [main repository](https://github.com/Microsoft/vscode-languageserver-node/blob/master/README.md)

## License
[MIT](https://github.com/Microsoft/vscode-languageserver-node/blob/master/License.txt)
