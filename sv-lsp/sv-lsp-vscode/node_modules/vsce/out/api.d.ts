import { IPublishOptions as _IPublishOptions } from './publish';
import { IPackageOptions } from './package';
export type { IPackageOptions } from './package';
/**
 * @deprecated prefer IPackageOptions instead
 */
export declare type IBaseVSIXOptions = Pick<IPackageOptions, 'baseContentUrl' | 'baseImagesUrl' | 'githubBranch' | 'gitlabBranch' | 'useYarn' | 'target' | 'preRelease'>;
/**
 * @deprecated prefer IPackageOptions instead
 */
export declare type ICreateVSIXOptions = Pick<IPackageOptions, 'cwd' | 'packagePath'> & IBaseVSIXOptions;
/**
 * The supported list of package managers.
 */
export declare enum PackageManager {
    Npm = 0,
    Yarn = 1,
    None = 2
}
export interface IListFilesOptions {
    /**
     * The working directory of the extension. Defaults to `process.cwd()`.
     */
    cwd?: string;
    /**
     * The package manager to use. Defaults to `PackageManager.Npm`.
     */
    packageManager?: PackageManager;
    /**
     * A subset of the top level dependencies which should be included. The
     * default is `undefined` which include all dependencies, an empty array means
     * no dependencies will be included.
     */
    packagedDependencies?: string[];
    /**
     * The location of an alternative .vscodeignore file to be used.
     * The `.vscodeignore` file located at the root of the project will be taken
     * instead, if none is specified.
     */
    ignoreFile?: string;
}
export declare type IPublishVSIXOptions = IPublishOptions & Pick<IPackageOptions, 'target'>;
export declare type IPublishOptions = _IPublishOptions;
/**
 * Creates a VSIX from the extension in the current working directory.
 */
export declare function createVSIX(options?: IPackageOptions): Promise<any>;
/**
 * Publishes the extension in the current working directory.
 */
export declare function publish(options?: IPublishOptions): Promise<any>;
/**
 * Lists the files included in the extension's package.
 */
export declare function listFiles(options?: IListFilesOptions): Promise<string[]>;
/**
 * Publishes a pre-build VSIX.
 */
export declare function publishVSIX(packagePath: string | string[], options?: IPublishVSIXOptions): Promise<any>;
