"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = __importDefault(require("commander"));
const leven_1 = __importDefault(require("leven"));
const package_1 = require("./package");
const publish_1 = require("./publish");
const show_1 = require("./show");
const search_1 = require("./search");
const store_1 = require("./store");
const npm_1 = require("./npm");
const util_1 = require("./util");
const semver = __importStar(require("semver"));
const tty_1 = require("tty");
const pkg = require('../package.json');
function fatal(message, ...args) {
    if (message instanceof Error) {
        message = message.message;
        if (/^cancell?ed$/i.test(message)) {
            return;
        }
    }
    util_1.log.error(message, ...args);
    if (/Unauthorized\(401\)/.test(message)) {
        util_1.log.error(`Be sure to use a Personal Access Token which has access to **all accessible accounts**.
See https://code.visualstudio.com/api/working-with-extensions/publishing-extension#publishing-extensions for more information.`);
    }
    process.exit(1);
}
function main(task) {
    let latestVersion = null;
    const token = new util_1.CancellationToken();
    if ((0, tty_1.isatty)(1)) {
        (0, npm_1.getLatestVersion)(pkg.name, token)
            .then(version => (latestVersion = version))
            .catch(_ => {
            /* noop */
        });
    }
    task.catch(fatal).then(() => {
        if (latestVersion && semver.gt(latestVersion, pkg.version)) {
            util_1.log.info(`\nThe latest version of ${pkg.name} is ${latestVersion} and you have ${pkg.version}.\nUpdate it now: npm install -g ${pkg.name}`);
        }
        else {
            token.cancel();
        }
    });
}
const ValidTargets = [...package_1.Targets].join(', ');
module.exports = function (argv) {
    commander_1.default.version(pkg.version).usage('<command>');
    commander_1.default
        .command('ls')
        .description('Lists all the files that will be published')
        .option('--yarn', 'Use yarn instead of npm (default inferred from presence of yarn.lock or .yarnrc)')
        .option('--no-yarn', 'Use npm instead of yarn (default inferred from lack of yarn.lock or .yarnrc)')
        .option('--packagedDependencies <path>', 'Select packages that should be published only (includes dependencies)', (val, all) => (all ? all.concat(val) : [val]), undefined)
        .option('--ignoreFile <path>', 'Indicate alternative .vscodeignore')
        // default must remain undefined for dependencies or we will fail to load defaults from package.json
        .option('--dependencies', 'Enable dependency detection via npm or yarn', undefined)
        .option('--no-dependencies', 'Disable dependency detection via npm or yarn', undefined)
        .action(({ yarn, packagedDependencies, ignoreFile, dependencies }) => main((0, package_1.ls)({ useYarn: yarn, packagedDependencies, ignoreFile, dependencies })));
    commander_1.default
        .command('package [version]')
        .description('Packages an extension')
        .option('-o, --out <path>', 'Output .vsix extension file to <path> location (defaults to <name>-<version>.vsix)')
        .option('-t, --target <target>', `Target architecture. Valid targets: ${ValidTargets}`)
        .option('-m, --message <commit message>', 'Commit message used when calling `npm version`.')
        .option('--no-git-tag-version', 'Do not create a version commit and tag when calling `npm version`. Valid only when [version] is provided.')
        .option('--no-update-package-json', 'Do not update `package.json`. Valid only when [version] is provided.')
        .option('--githubBranch <branch>', 'The GitHub branch used to infer relative links in README.md. Can be overridden by --baseContentUrl and --baseImagesUrl.')
        .option('--gitlabBranch <branch>', 'The GitLab branch used to infer relative links in README.md. Can be overridden by --baseContentUrl and --baseImagesUrl.')
        .option('--no-rewrite-relative-links', 'Skip rewriting relative links.')
        .option('--baseContentUrl <url>', 'Prepend all relative links in README.md with this url.')
        .option('--baseImagesUrl <url>', 'Prepend all relative image links in README.md with this url.')
        .option('--yarn', 'Use yarn instead of npm (default inferred from presence of yarn.lock or .yarnrc)')
        .option('--no-yarn', 'Use npm instead of yarn (default inferred from lack of yarn.lock or .yarnrc)')
        .option('--ignoreFile <path>', 'Indicate alternative .vscodeignore')
        .option('--no-gitHubIssueLinking', 'Disable automatic expansion of GitHub-style issue syntax into links')
        .option('--no-gitLabIssueLinking', 'Disable automatic expansion of GitLab-style issue syntax into links')
        // default must remain undefined for dependencies or we will fail to load defaults from package.json
        .option('--dependencies', 'Enable dependency detection via npm or yarn', undefined)
        .option('--no-dependencies', 'Disable dependency detection via npm or yarn')
        .option('--pre-release', 'Mark this package as a pre-release')
        .option('--allow-star-activation', 'Allow using * in activation events')
        .option('--allow-missing-repository', 'Allow missing a repository URL in package.json')
        .action((version, { out, target, message, gitTagVersion, updatePackageJson, githubBranch, gitlabBranch, rewriteRelativeLinks, baseContentUrl, baseImagesUrl, yarn, ignoreFile, gitHubIssueLinking, gitLabIssueLinking, dependencies, preRelease, allowStarActivation, allowMissingRepository, }) => main((0, package_1.packageCommand)({
        packagePath: out,
        version,
        target,
        commitMessage: message,
        gitTagVersion,
        updatePackageJson,
        githubBranch,
        gitlabBranch,
        rewriteRelativeLinks,
        baseContentUrl,
        baseImagesUrl,
        useYarn: yarn,
        ignoreFile,
        gitHubIssueLinking,
        gitLabIssueLinking,
        dependencies,
        preRelease,
        allowStarActivation,
        allowMissingRepository,
    })));
    commander_1.default
        .command('publish [version]')
        .description('Publishes an extension')
        .option('-p, --pat <token>', 'Personal Access Token (defaults to VSCE_PAT environment variable)', process.env['VSCE_PAT'])
        .option('-t, --target <targets...>', `Target architectures. Valid targets: ${ValidTargets}`)
        .option('-m, --message <commit message>', 'Commit message used when calling `npm version`.')
        .option('--no-git-tag-version', 'Do not create a version commit and tag when calling `npm version`. Valid only when [version] is provided.')
        .option('--no-update-package-json', 'Do not update `package.json`. Valid only when [version] is provided.')
        .option('-i, --packagePath <paths...>', 'Publish the provided VSIX packages.')
        .option('--githubBranch <branch>', 'The GitHub branch used to infer relative links in README.md. Can be overridden by --baseContentUrl and --baseImagesUrl.')
        .option('--gitlabBranch <branch>', 'The GitLab branch used to infer relative links in README.md. Can be overridden by --baseContentUrl and --baseImagesUrl.')
        .option('--baseContentUrl <url>', 'Prepend all relative links in README.md with this url.')
        .option('--baseImagesUrl <url>', 'Prepend all relative image links in README.md with this url.')
        .option('--yarn', 'Use yarn instead of npm (default inferred from presence of yarn.lock or .yarnrc)')
        .option('--no-yarn', 'Use npm instead of yarn (default inferred from lack of yarn.lock or .yarnrc)')
        .option('--noVerify')
        .option('--ignoreFile <path>', 'Indicate alternative .vscodeignore')
        // default must remain undefined for dependencies or we will fail to load defaults from package.json
        .option('--dependencies', 'Enable dependency detection via npm or yarn', undefined)
        .option('--no-dependencies', 'Disable dependency detection via npm or yarn', undefined)
        .option('--pre-release', 'Mark this package as a pre-release')
        .option('--allow-star-activation', 'Allow using * in activation events')
        .option('--allow-missing-repository', 'Allow missing a repository URL in package.json')
        .option('--skip-duplicate', 'Fail silently if version already exists on the marketplace')
        .action((version, { pat, target, message, gitTagVersion, updatePackageJson, packagePath, githubBranch, gitlabBranch, baseContentUrl, baseImagesUrl, yarn, noVerify, ignoreFile, dependencies, preRelease, allowStarActivation, allowMissingRepository, skipDuplicate, }) => main((0, publish_1.publish)({
        pat,
        version,
        targets: target,
        commitMessage: message,
        gitTagVersion,
        updatePackageJson,
        packagePath,
        githubBranch,
        gitlabBranch,
        baseContentUrl,
        baseImagesUrl,
        useYarn: yarn,
        noVerify,
        ignoreFile,
        dependencies,
        preRelease,
        allowStarActivation,
        allowMissingRepository,
        skipDuplicate,
    })));
    commander_1.default
        .command('unpublish [extensionid]')
        .description('Unpublishes an extension. Example extension id: microsoft.csharp.')
        .option('-p, --pat <token>', 'Personal Access Token')
        .option('-f, --force', 'Forces Unpublished Extension')
        .action((id, { pat, force }) => main((0, publish_1.unpublish)({ id, pat, force })));
    commander_1.default
        .command('ls-publishers')
        .description('List all known publishers')
        .action(() => main((0, store_1.listPublishers)()));
    commander_1.default
        .command('delete-publisher <publisher>')
        .description('Deletes a publisher')
        .action(publisher => main((0, store_1.deletePublisher)(publisher)));
    commander_1.default
        .command('login <publisher>')
        .description('Add a publisher to the known publishers list')
        .action(name => main((0, store_1.loginPublisher)(name)));
    commander_1.default
        .command('logout <publisher>')
        .description('Remove a publisher from the known publishers list')
        .action(name => main((0, store_1.logoutPublisher)(name)));
    commander_1.default
        .command('verify-pat [publisher]')
        .option('-p, --pat <token>', 'Personal Access Token (defaults to VSCE_PAT environment variable)', process.env['VSCE_PAT'])
        .description('Verify if the Personal Access Token has publish rights for the publisher.')
        .action((name, { pat }) => main((0, store_1.verifyPat)(pat, name)));
    commander_1.default
        .command('show <extensionid>')
        .option('--json', 'Output data in json format', false)
        .description('Show extension metadata')
        .action((extensionid, { json }) => main((0, show_1.show)(extensionid, json)));
    commander_1.default
        .command('search <text>')
        .option('--json', 'Output result in json format', false)
        .option('--stats', 'Shows the extension rating and download counts', false)
        .option('-p, --pagesize [value]', 'Number of results to return', '100')
        .description('search extension gallery')
        .action((text, { json, pagesize, stats }) => main((0, search_1.search)(text, json, parseInt(pagesize), stats)));
    commander_1.default.on('command:*', ([cmd]) => {
        if (cmd === 'create-publisher') {
            util_1.log.error(`The 'create-publisher' command is no longer available. You can create a publisher directly in the Marketplace: https://aka.ms/vscode-create-publisher`);
            process.exit(1);
        }
        commander_1.default.outputHelp(help => {
            const availableCommands = commander_1.default.commands.map(c => c._name);
            const suggestion = availableCommands.find(c => (0, leven_1.default)(c, cmd) < c.length * 0.4);
            help = `${help}
Unknown command '${cmd}'`;
            return suggestion ? `${help}, did you mean '${suggestion}'?\n` : `${help}.\n`;
        });
        process.exit(1);
    });
    commander_1.default.parse(argv);
};
//# sourceMappingURL=main.js.map