name: Needs More Info Closer
on:
  schedule:
    - cron: 0 11 * * * # 4:00am <PERSON>mond
  repository_dispatch:
    types: [trigger-needs-more-info]

jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Actions
        uses: actions/checkout@v2
        with:
          repository: "microsoft/vscode-github-triage-actions"
          path: ./actions
          ref: stable
      - name: Install Actions
        run: npm install --production --prefix ./actions
      - name: Run Needs More Info Closer
        uses: ./actions/needs-more-info-closer
        with:
          appInsightsKey: ${{secrets.TRIAGE_ACTIONS_APP_INSIGHTS}}
          token: ${{secrets.VSCODE_ISSUE_TRIAGE_BOT_PAT}}
          label: needs more info
          closeDays: 7
          additionalTeam: "cleidigh|usernamehw|gjsjohnmurray|IllusionMH"
          closeComment: "This issue has been closed automatically because it needs more information and has not had recent activity. See also our [issue reporting](https://aka.ms/vscodeissuereporting) guidelines.\n\nHappy Coding!"
          pingDays: 80
          pingComment: "Hey @${assignee}, this issue might need further attention.\n\n@${author}, you can help us out by closing this issue if the problem no longer exists, or adding more information."
