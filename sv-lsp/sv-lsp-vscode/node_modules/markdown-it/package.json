{"name": "markdown-it", "version": "12.3.2", "description": "Markdown-it - modern pluggable markdown parser.", "keywords": ["markdown", "parser", "commonmark", "markdown-it", "markdown-it-plugin"], "repository": "markdown-it/markdown-it", "license": "MIT", "main": "index.js", "bin": {"markdown-it": "bin/markdown-it.js"}, "scripts": {"lint": "eslint .", "test": "npm run lint && nyc mocha && node support/specsplit.js", "coverage": "npm run test && nyc report --reporter html", "report-coveralls": "nyc --reporter=lcov mocha", "doc": "node support/build_doc.js", "gh-doc": "npm run doc && gh-pages -d apidoc -f", "demo": "npm run lint && node support/build_demo.js", "gh-demo": "npm run demo && gh-pages -d demo -f -b master -r **************:markdown-it/markdown-it.github.io.git", "browserify": "rollup -c support/rollup.config.js", "benchmark-deps": "npm install --prefix benchmark/extra/ -g marked@0.3.6 commonmark@0.26.0 markdown-it/markdown-it.git#2.2.1", "specsplit": "support/specsplit.js good -o test/fixtures/commonmark/good.txt && support/specsplit.js bad -o test/fixtures/commonmark/bad.txt && support/specsplit.js", "todo": "grep 'TODO' -n -r ./lib 2>/dev/null", "prepublishOnly": "npm run gh-demo && npm run gh-doc"}, "files": ["index.js", "bin/", "lib/", "dist/"], "dependencies": {"argparse": "^2.0.1", "entities": "~2.1.0", "linkify-it": "^3.0.1", "mdurl": "^1.0.1", "uc.micro": "^1.0.5"}, "devDependencies": {"@rollup/plugin-commonjs": "^16.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^10.0.0", "ansi": "^0.3.0", "autoprefixer-stylus": "^1.0.0", "benchmark": "~2.1.0", "chai": "^4.2.0", "coveralls": "^3.0.4", "eslint": "^8.4.1", "express": "^4.14.0", "gh-pages": "^3.1.0", "highlight.js": "^10.7.2", "jest-worker": "^26.6.2", "markdown-it-abbr": "^1.0.4", "markdown-it-container": "^3.0.0", "markdown-it-deflist": "^2.0.0", "markdown-it-emoji": "^2.0.0", "markdown-it-footnote": "^3.0.1", "markdown-it-for-inline": "^0.1.0", "markdown-it-ins": "^3.0.0", "markdown-it-mark": "^3.0.0", "markdown-it-sub": "^1.0.0", "markdown-it-sup": "^1.0.0", "markdown-it-testgen": "^0.1.3", "mocha": "^9.1.3", "ndoc": "^6.0.0", "needle": "^3.0.0", "nyc": "^15.0.1", "pug-cli": "^1.0.0-alpha6", "rollup": "^2.29.0", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-terser": "^7.0.2", "shelljs": "^0.8.4", "stylus": "^0.55.0", "supertest": "^6.0.1"}, "mocha": {"inline-diffs": true, "timeout": 60000}}