<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: G Pages: 1 -->
<svg width="1779pt" height="983pt"
 viewBox="0.00 0.00 1779.20 982.66" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(21.6 961.06)">
<title>G</title>
<polygon fill="#111111" stroke="transparent" points="-21.6,21.6 -21.6,-961.06 1757.6,-961.06 1757.6,21.6 -21.6,21.6"/>
<!-- base64.js -->
<g id="node1" class="node">
<title>base64.js</title>
<path fill="none" stroke="#cfffac" d="M119.83,-306C119.83,-306 61.17,-306 61.17,-306 57.33,-306 53.5,-302.17 53.5,-298.33 53.5,-298.33 53.5,-290.67 53.5,-290.67 53.5,-286.83 57.33,-283 61.17,-283 61.17,-283 119.83,-283 119.83,-283 123.67,-283 127.5,-286.83 127.5,-290.67 127.5,-290.67 127.5,-298.33 127.5,-298.33 127.5,-302.17 123.67,-306 119.83,-306"/>
<text text-anchor="middle" x="90.5" y="-290.8" font-family="Arial" font-size="14.00" fill="#cfffac">base64.js</text>
</g>
<!-- compressedObject.js -->
<g id="node2" class="node">
<title>compressedObject.js</title>
<path fill="none" stroke="#c6c5fe" d="M1141.33,-296C1141.33,-296 1012.67,-296 1012.67,-296 1008.83,-296 1005,-292.17 1005,-288.33 1005,-288.33 1005,-280.67 1005,-280.67 1005,-276.83 1008.83,-273 1012.67,-273 1012.67,-273 1141.33,-273 1141.33,-273 1145.17,-273 1149,-276.83 1149,-280.67 1149,-280.67 1149,-288.33 1149,-288.33 1149,-292.17 1145.17,-296 1141.33,-296"/>
<text text-anchor="middle" x="1077" y="-280.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">compressedObject.js</text>
</g>
<!-- stream/Crc32Probe.js -->
<g id="node3" class="node">
<title>stream/Crc32Probe.js</title>
<path fill="none" stroke="#c6c5fe" d="M1348.83,-415C1348.83,-415 1216.17,-415 1216.17,-415 1212.33,-415 1208.5,-411.17 1208.5,-407.33 1208.5,-407.33 1208.5,-399.67 1208.5,-399.67 1208.5,-395.83 1212.33,-392 1216.17,-392 1216.17,-392 1348.83,-392 1348.83,-392 1352.67,-392 1356.5,-395.83 1356.5,-399.67 1356.5,-399.67 1356.5,-407.33 1356.5,-407.33 1356.5,-411.17 1352.67,-415 1348.83,-415"/>
<text text-anchor="middle" x="1282.5" y="-399.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">stream/Crc32Probe.js</text>
</g>
<!-- compressedObject.js&#45;&gt;stream/Crc32Probe.js -->
<g id="edge1" class="edge">
<title>compressedObject.js&#45;&gt;stream/Crc32Probe.js</title>
<path fill="none" stroke="#757575" d="M1089.24,-296.19C1109.84,-316.93 1155.86,-360.17 1203,-383.5 1206.76,-385.36 1210.7,-387.06 1214.75,-388.61"/>
<polygon fill="#757575" stroke="#757575" points="1213.71,-391.95 1224.3,-391.95 1216.02,-385.35 1213.71,-391.95"/>
</g>
<!-- stream/DataLengthProbe.js -->
<g id="node4" class="node">
<title>stream/DataLengthProbe.js</title>
<path fill="none" stroke="#c6c5fe" d="M1605.33,-337C1605.33,-337 1438.67,-337 1438.67,-337 1434.83,-337 1431,-333.17 1431,-329.33 1431,-329.33 1431,-321.67 1431,-321.67 1431,-317.83 1434.83,-314 1438.67,-314 1438.67,-314 1605.33,-314 1605.33,-314 1609.17,-314 1613,-317.83 1613,-321.67 1613,-321.67 1613,-329.33 1613,-329.33 1613,-333.17 1609.17,-337 1605.33,-337"/>
<text text-anchor="middle" x="1522" y="-321.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">stream/DataLengthProbe.js</text>
</g>
<!-- compressedObject.js&#45;&gt;stream/DataLengthProbe.js -->
<g id="edge2" class="edge">
<title>compressedObject.js&#45;&gt;stream/DataLengthProbe.js</title>
<path fill="none" stroke="#757575" d="M1146.96,-296.01C1165.15,-298.77 1184.78,-301.49 1203,-303.5 1275.56,-311.51 1357.77,-317.11 1420.42,-320.65"/>
<polygon fill="#757575" stroke="#757575" points="1420.52,-324.16 1430.7,-321.22 1420.91,-317.17 1420.52,-324.16"/>
</g>
<!-- stream/DataWorker.js -->
<g id="node5" class="node">
<title>stream/DataWorker.js</title>
<path fill="none" stroke="#c6c5fe" d="M1348.33,-294C1348.33,-294 1216.67,-294 1216.67,-294 1212.83,-294 1209,-290.17 1209,-286.33 1209,-286.33 1209,-278.67 1209,-278.67 1209,-274.83 1212.83,-271 1216.67,-271 1216.67,-271 1348.33,-271 1348.33,-271 1352.17,-271 1356,-274.83 1356,-278.67 1356,-278.67 1356,-286.33 1356,-286.33 1356,-290.17 1352.17,-294 1348.33,-294"/>
<text text-anchor="middle" x="1282.5" y="-278.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">stream/DataWorker.js</text>
</g>
<!-- compressedObject.js&#45;&gt;stream/DataWorker.js -->
<g id="edge3" class="edge">
<title>compressedObject.js&#45;&gt;stream/DataWorker.js</title>
<path fill="none" stroke="#757575" d="M1149.17,-283.8C1165.22,-283.64 1182.37,-283.47 1198.84,-283.31"/>
<polygon fill="#757575" stroke="#757575" points="1199.02,-286.81 1208.99,-283.21 1198.95,-279.81 1199.02,-286.81"/>
</g>
<!-- crc32.js -->
<g id="node8" class="node">
<title>crc32.js</title>
<path fill="none" stroke="#c6c5fe" d="M1545.83,-495C1545.83,-495 1498.17,-495 1498.17,-495 1494.33,-495 1490.5,-491.17 1490.5,-487.33 1490.5,-487.33 1490.5,-479.67 1490.5,-479.67 1490.5,-475.83 1494.33,-472 1498.17,-472 1498.17,-472 1545.83,-472 1545.83,-472 1549.67,-472 1553.5,-475.83 1553.5,-479.67 1553.5,-479.67 1553.5,-487.33 1553.5,-487.33 1553.5,-491.17 1549.67,-495 1545.83,-495"/>
<text text-anchor="middle" x="1522" y="-479.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">crc32.js</text>
</g>
<!-- stream/Crc32Probe.js&#45;&gt;crc32.js -->
<g id="edge42" class="edge">
<title>stream/Crc32Probe.js&#45;&gt;crc32.js</title>
<path fill="none" stroke="#757575" d="M1317.78,-415.05C1360.98,-429.6 1435.12,-454.57 1480.9,-469.99"/>
<polygon fill="#757575" stroke="#757575" points="1479.91,-473.35 1490.5,-473.23 1482.14,-466.72 1479.91,-473.35"/>
</g>
<!-- utils.js -->
<g id="node9" class="node">
<title>utils.js</title>
<path fill="none" stroke="#cfffac" d="M1728.33,-495C1728.33,-495 1689.67,-495 1689.67,-495 1685.83,-495 1682,-491.17 1682,-487.33 1682,-487.33 1682,-479.67 1682,-479.67 1682,-475.83 1685.83,-472 1689.67,-472 1689.67,-472 1728.33,-472 1728.33,-472 1732.17,-472 1736,-475.83 1736,-479.67 1736,-479.67 1736,-487.33 1736,-487.33 1736,-491.17 1732.17,-495 1728.33,-495"/>
<text text-anchor="middle" x="1709" y="-479.8" font-family="Arial" font-size="14.00" fill="#cfffac">utils.js</text>
</g>
<!-- stream/Crc32Probe.js&#45;&gt;utils.js -->
<g id="edge43" class="edge">
<title>stream/Crc32Probe.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1356.76,-395.52C1432.27,-390.27 1551.91,-390.38 1646,-429.5 1663.95,-436.96 1680.34,-451.77 1691.76,-463.99"/>
<polygon fill="#757575" stroke="#757575" points="1689.24,-466.42 1698.51,-471.54 1694.46,-461.75 1689.24,-466.42"/>
</g>
<!-- stream/DataLengthProbe.js&#45;&gt;utils.js -->
<g id="edge44" class="edge">
<title>stream/DataLengthProbe.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1613.06,-335.12C1624.95,-339.19 1636.34,-344.81 1646,-352.5 1680.91,-380.29 1697.21,-432.45 1703.92,-461.87"/>
<polygon fill="#757575" stroke="#757575" points="1700.56,-462.88 1706.05,-471.94 1707.41,-461.43 1700.56,-462.88"/>
</g>
<!-- stream/DataWorker.js&#45;&gt;utils.js -->
<g id="edge45" class="edge">
<title>stream/DataWorker.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1328.44,-270.94C1402.75,-254.67 1552.34,-234.37 1646,-305.5 1695.57,-343.15 1705.71,-423.13 1707.64,-461.74"/>
<polygon fill="#757575" stroke="#757575" points="1704.15,-462.02 1708.01,-471.89 1711.15,-461.77 1704.15,-462.02"/>
</g>
<!-- compressions.js -->
<g id="node6" class="node">
<title>compressions.js</title>
<path fill="none" stroke="#c6c5fe" d="M1126.33,-608C1126.33,-608 1027.67,-608 1027.67,-608 1023.83,-608 1020,-604.17 1020,-600.33 1020,-600.33 1020,-592.67 1020,-592.67 1020,-588.83 1023.83,-585 1027.67,-585 1027.67,-585 1126.33,-585 1126.33,-585 1130.17,-585 1134,-588.83 1134,-592.67 1134,-592.67 1134,-600.33 1134,-600.33 1134,-604.17 1130.17,-608 1126.33,-608"/>
<text text-anchor="middle" x="1077" y="-592.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">compressions.js</text>
</g>
<!-- flate.js -->
<g id="node7" class="node">
<title>flate.js</title>
<path fill="none" stroke="#c6c5fe" d="M1542.33,-608C1542.33,-608 1501.67,-608 1501.67,-608 1497.83,-608 1494,-604.17 1494,-600.33 1494,-600.33 1494,-592.67 1494,-592.67 1494,-588.83 1497.83,-585 1501.67,-585 1501.67,-585 1542.33,-585 1542.33,-585 1546.17,-585 1550,-588.83 1550,-592.67 1550,-592.67 1550,-600.33 1550,-600.33 1550,-604.17 1546.17,-608 1542.33,-608"/>
<text text-anchor="middle" x="1522" y="-592.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">flate.js</text>
</g>
<!-- compressions.js&#45;&gt;flate.js -->
<g id="edge4" class="edge">
<title>compressions.js&#45;&gt;flate.js</title>
<path fill="none" stroke="#757575" d="M1134.11,-596.5C1225.94,-596.5 1405.91,-596.5 1483.7,-596.5"/>
<polygon fill="#757575" stroke="#757575" points="1483.98,-600 1493.98,-596.5 1483.98,-593 1483.98,-600"/>
</g>
<!-- flate.js&#45;&gt;utils.js -->
<g id="edge6" class="edge">
<title>flate.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1550.09,-596.32C1576.55,-594.94 1616.97,-589.52 1646,-570.5 1670.69,-554.33 1688.64,-524.65 1698.69,-504.5"/>
<polygon fill="#757575" stroke="#757575" points="1701.97,-505.75 1703.1,-495.22 1695.65,-502.75 1701.97,-505.75"/>
</g>
<!-- crc32.js&#45;&gt;utils.js -->
<g id="edge5" class="edge">
<title>crc32.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1553.55,-483.5C1586.01,-483.5 1637.3,-483.5 1671.78,-483.5"/>
<polygon fill="#757575" stroke="#757575" points="1671.9,-487 1681.9,-483.5 1671.9,-480 1671.9,-487"/>
</g>
<!-- defaults.js -->
<g id="node10" class="node">
<title>defaults.js</title>
<path fill="none" stroke="#cfffac" d="M121.33,-388C121.33,-388 59.67,-388 59.67,-388 55.83,-388 52,-384.17 52,-380.33 52,-380.33 52,-372.67 52,-372.67 52,-368.83 55.83,-365 59.67,-365 59.67,-365 121.33,-365 121.33,-365 125.17,-365 129,-368.83 129,-372.67 129,-372.67 129,-380.33 129,-380.33 129,-384.17 125.17,-388 121.33,-388"/>
<text text-anchor="middle" x="90.5" y="-372.8" font-family="Arial" font-size="14.00" fill="#cfffac">defaults.js</text>
</g>
<!-- external.js -->
<g id="node11" class="node">
<title>external.js</title>
<path fill="none" stroke="#cfffac" d="M121.83,-429C121.83,-429 59.17,-429 59.17,-429 55.33,-429 51.5,-425.17 51.5,-421.33 51.5,-421.33 51.5,-413.67 51.5,-413.67 51.5,-409.83 55.33,-406 59.17,-406 59.17,-406 121.83,-406 121.83,-406 125.67,-406 129.5,-409.83 129.5,-413.67 129.5,-413.67 129.5,-421.33 129.5,-421.33 129.5,-425.17 125.67,-429 121.83,-429"/>
<text text-anchor="middle" x="90.5" y="-413.8" font-family="Arial" font-size="14.00" fill="#cfffac">external.js</text>
</g>
<!-- generate/ZipFileWorker.js -->
<g id="node12" class="node">
<title>generate/ZipFileWorker.js</title>
<path fill="none" stroke="#c6c5fe" d="M1154.33,-490C1154.33,-490 999.67,-490 999.67,-490 995.83,-490 992,-486.17 992,-482.33 992,-482.33 992,-474.67 992,-474.67 992,-470.83 995.83,-467 999.67,-467 999.67,-467 1154.33,-467 1154.33,-467 1158.17,-467 1162,-470.83 1162,-474.67 1162,-474.67 1162,-482.33 1162,-482.33 1162,-486.17 1158.17,-490 1154.33,-490"/>
<text text-anchor="middle" x="1077" y="-474.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">generate/ZipFileWorker.js</text>
</g>
<!-- generate/ZipFileWorker.js&#45;&gt;crc32.js -->
<g id="edge7" class="edge">
<title>generate/ZipFileWorker.js&#45;&gt;crc32.js</title>
<path fill="none" stroke="#757575" d="M1162.11,-479.45C1257.3,-480.52 1408.45,-482.23 1480.38,-483.04"/>
<polygon fill="#757575" stroke="#757575" points="1480.36,-486.54 1490.4,-483.15 1480.44,-479.54 1480.36,-486.54"/>
</g>
<!-- generate/ZipFileWorker.js&#45;&gt;utils.js -->
<g id="edge9" class="edge">
<title>generate/ZipFileWorker.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1160.91,-466.95C1272.08,-453.58 1475.22,-436.64 1646,-463.5 1654.67,-464.86 1663.79,-467.24 1672.28,-469.89"/>
<polygon fill="#757575" stroke="#757575" points="1671.3,-473.25 1681.89,-473.08 1673.5,-466.61 1671.3,-473.25"/>
</g>
<!-- utf8.js -->
<g id="node13" class="node">
<title>utf8.js</title>
<path fill="none" stroke="#c6c5fe" d="M1301.83,-374C1301.83,-374 1263.17,-374 1263.17,-374 1259.33,-374 1255.5,-370.17 1255.5,-366.33 1255.5,-366.33 1255.5,-358.67 1255.5,-358.67 1255.5,-354.83 1259.33,-351 1263.17,-351 1263.17,-351 1301.83,-351 1301.83,-351 1305.67,-351 1309.5,-354.83 1309.5,-358.67 1309.5,-358.67 1309.5,-366.33 1309.5,-366.33 1309.5,-370.17 1305.67,-374 1301.83,-374"/>
<text text-anchor="middle" x="1282.5" y="-358.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">utf8.js</text>
</g>
<!-- generate/ZipFileWorker.js&#45;&gt;utf8.js -->
<g id="edge8" class="edge">
<title>generate/ZipFileWorker.js&#45;&gt;utf8.js</title>
<path fill="none" stroke="#757575" d="M1095.05,-466.9C1113.21,-454.39 1142.63,-433.7 1167,-414.5 1183.59,-401.44 1184.33,-393.37 1203,-383.5 1216.02,-376.62 1231.48,-371.86 1245.21,-368.63"/>
<polygon fill="#757575" stroke="#757575" points="1246.28,-371.98 1255.32,-366.45 1244.8,-365.14 1246.28,-371.98"/>
</g>
<!-- utf8.js&#45;&gt;utils.js -->
<g id="edge49" class="edge">
<title>utf8.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1309.66,-361.43C1383.03,-358.9 1589.11,-355.01 1646,-388.5 1674.11,-405.05 1691.8,-439.95 1700.79,-462.53"/>
<polygon fill="#757575" stroke="#757575" points="1697.52,-463.79 1704.31,-471.93 1704.08,-461.34 1697.52,-463.79"/>
</g>
<!-- generate/index.js -->
<g id="node14" class="node">
<title>generate/index.js</title>
<path fill="none" stroke="#c6c5fe" d="M884.83,-490C884.83,-490 781.17,-490 781.17,-490 777.33,-490 773.5,-486.17 773.5,-482.33 773.5,-482.33 773.5,-474.67 773.5,-474.67 773.5,-470.83 777.33,-467 781.17,-467 781.17,-467 884.83,-467 884.83,-467 888.67,-467 892.5,-470.83 892.5,-474.67 892.5,-474.67 892.5,-482.33 892.5,-482.33 892.5,-486.17 888.67,-490 884.83,-490"/>
<text text-anchor="middle" x="833" y="-474.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">generate/index.js</text>
</g>
<!-- generate/index.js&#45;&gt;compressions.js -->
<g id="edge10" class="edge">
<title>generate/index.js&#45;&gt;compressions.js</title>
<path fill="none" stroke="#757575" d="M857.64,-490.03C901.11,-511.22 993.77,-556.4 1043.25,-580.53"/>
<polygon fill="#757575" stroke="#757575" points="1041.74,-583.69 1052.26,-584.92 1044.81,-577.4 1041.74,-583.69"/>
</g>
<!-- generate/index.js&#45;&gt;generate/ZipFileWorker.js -->
<g id="edge11" class="edge">
<title>generate/index.js&#45;&gt;generate/ZipFileWorker.js</title>
<path fill="none" stroke="#757575" d="M892.58,-478.5C919.35,-478.5 951.73,-478.5 981.79,-478.5"/>
<polygon fill="#757575" stroke="#757575" points="981.89,-482 991.89,-478.5 981.89,-475 981.89,-482"/>
</g>
<!-- index.js -->
<g id="node15" class="node">
<title>index.js</title>
<path fill="none" stroke="#c6c5fe" d="M113.83,-347C113.83,-347 67.17,-347 67.17,-347 63.33,-347 59.5,-343.17 59.5,-339.33 59.5,-339.33 59.5,-331.67 59.5,-331.67 59.5,-327.83 63.33,-324 67.17,-324 67.17,-324 113.83,-324 113.83,-324 117.67,-324 121.5,-327.83 121.5,-331.67 121.5,-331.67 121.5,-339.33 121.5,-339.33 121.5,-343.17 117.67,-347 113.83,-347"/>
<text text-anchor="middle" x="90.5" y="-331.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">index.js</text>
</g>
<!-- load.js -->
<g id="node16" class="node">
<title>load.js</title>
<path fill="none" stroke="#c6c5fe" d="M265.33,-416C265.33,-416 224.67,-416 224.67,-416 220.83,-416 217,-412.17 217,-408.33 217,-408.33 217,-400.67 217,-400.67 217,-396.83 220.83,-393 224.67,-393 224.67,-393 265.33,-393 265.33,-393 269.17,-393 273,-396.83 273,-400.67 273,-400.67 273,-408.33 273,-408.33 273,-412.17 269.17,-416 265.33,-416"/>
<text text-anchor="middle" x="245" y="-400.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">load.js</text>
</g>
<!-- index.js&#45;&gt;load.js -->
<g id="edge12" class="edge">
<title>index.js&#45;&gt;load.js</title>
<path fill="none" stroke="#757575" d="M121.56,-339.32C139.45,-342.37 162.23,-347.65 181,-356.5 196.81,-363.96 212.46,-375.85 224.2,-385.93"/>
<polygon fill="#757575" stroke="#757575" points="222.21,-388.84 232.01,-392.86 226.85,-383.6 222.21,-388.84"/>
</g>
<!-- object.js -->
<g id="node17" class="node">
<title>object.js</title>
<path fill="none" stroke="#c6c5fe" d="M638.83,-240C638.83,-240 588.17,-240 588.17,-240 584.33,-240 580.5,-236.17 580.5,-232.33 580.5,-232.33 580.5,-224.67 580.5,-224.67 580.5,-220.83 584.33,-217 588.17,-217 588.17,-217 638.83,-217 638.83,-217 642.67,-217 646.5,-220.83 646.5,-224.67 646.5,-224.67 646.5,-232.33 646.5,-232.33 646.5,-236.17 642.67,-240 638.83,-240"/>
<text text-anchor="middle" x="613.5" y="-224.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">object.js</text>
</g>
<!-- index.js&#45;&gt;object.js -->
<g id="edge13" class="edge">
<title>index.js&#45;&gt;object.js</title>
<path fill="none" stroke="#757575" d="M121.76,-329.28C210.16,-311.13 468.14,-258.15 570.22,-237.18"/>
<polygon fill="#757575" stroke="#757575" points="571.08,-240.58 580.17,-235.14 569.67,-233.72 571.08,-240.58"/>
</g>
<!-- load.js&#45;&gt;stream/Crc32Probe.js -->
<g id="edge14" class="edge">
<title>load.js&#45;&gt;stream/Crc32Probe.js</title>
<path fill="none" stroke="#757575" d="M273.16,-404.47C405.01,-404.35 980.76,-403.79 1198.06,-403.58"/>
<polygon fill="#757575" stroke="#757575" points="1198.14,-407.08 1208.14,-403.57 1198.14,-400.08 1198.14,-407.08"/>
</g>
<!-- load.js&#45;&gt;utils.js -->
<g id="edge16" class="edge">
<title>load.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M273.2,-411.94C344.82,-431.09 544.77,-481.85 715,-499.5 1016.95,-530.8 1094.44,-503.39 1398,-504.5 1508.22,-504.9 1537.24,-522.42 1646,-504.5 1654.7,-503.07 1663.83,-500.57 1672.32,-497.79"/>
<polygon fill="#757575" stroke="#757575" points="1673.64,-501.04 1681.94,-494.44 1671.34,-494.43 1673.64,-501.04"/>
</g>
<!-- load.js&#45;&gt;utf8.js -->
<g id="edge15" class="edge">
<title>load.js&#45;&gt;utf8.js</title>
<path fill="none" stroke="#757575" d="M273.18,-396.1C315.32,-383.84 399.17,-362.5 472,-362.5 472,-362.5 472,-362.5 834,-362.5 986.85,-362.5 1169.08,-362.5 1245.24,-362.5"/>
<polygon fill="#757575" stroke="#757575" points="1245.27,-366 1255.27,-362.5 1245.27,-359 1245.27,-366"/>
</g>
<!-- zipEntries.js -->
<g id="node19" class="node">
<title>zipEntries.js</title>
<path fill="none" stroke="#c6c5fe" d="M390.33,-648C390.33,-648 316.67,-648 316.67,-648 312.83,-648 309,-644.17 309,-640.33 309,-640.33 309,-632.67 309,-632.67 309,-628.83 312.83,-625 316.67,-625 316.67,-625 390.33,-625 390.33,-625 394.17,-625 398,-628.83 398,-632.67 398,-632.67 398,-640.33 398,-640.33 398,-644.17 394.17,-648 390.33,-648"/>
<text text-anchor="middle" x="353.5" y="-632.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">zipEntries.js</text>
</g>
<!-- load.js&#45;&gt;zipEntries.js -->
<g id="edge17" class="edge">
<title>load.js&#45;&gt;zipEntries.js</title>
<path fill="none" stroke="#757575" d="M251.28,-416C268.17,-452.8 321.05,-568 342.9,-615.6"/>
<polygon fill="#757575" stroke="#757575" points="339.83,-617.3 347.19,-624.93 346.2,-614.38 339.83,-617.3"/>
</g>
<!-- object.js&#45;&gt;compressedObject.js -->
<g id="edge20" class="edge">
<title>object.js&#45;&gt;compressedObject.js</title>
<path fill="none" stroke="#757575" d="M646.59,-232.39C717.9,-241.05 891.41,-262.1 994.73,-274.64"/>
<polygon fill="#757575" stroke="#757575" points="994.46,-278.13 1004.81,-275.86 995.31,-271.18 994.46,-278.13"/>
</g>
<!-- object.js&#45;&gt;utils.js -->
<g id="edge25" class="edge">
<title>object.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M637.38,-216.91C657.32,-207.43 687.31,-194.61 715,-188.5 871.92,-153.89 915.31,-168.5 1076,-168.5 1076,-168.5 1076,-168.5 1283.5,-168.5 1448.68,-168.5 1527.02,-135.92 1646,-250.5 1676.62,-279.99 1697.68,-409.34 1705.07,-461.54"/>
<polygon fill="#757575" stroke="#757575" points="1701.64,-462.29 1706.48,-471.71 1708.58,-461.33 1701.64,-462.29"/>
</g>
<!-- object.js&#45;&gt;utf8.js -->
<g id="edge24" class="edge">
<title>object.js&#45;&gt;utf8.js</title>
<path fill="none" stroke="#757575" d="M639.18,-240.05C659.22,-249 688.41,-261.09 715,-268.5 816.54,-296.78 1136.85,-342.48 1245.24,-357.52"/>
<polygon fill="#757575" stroke="#757575" points="1244.96,-361.02 1255.35,-358.92 1245.92,-354.08 1244.96,-361.02"/>
</g>
<!-- object.js&#45;&gt;generate/index.js -->
<g id="edge21" class="edge">
<title>object.js&#45;&gt;generate/index.js</title>
<path fill="none" stroke="#757575" d="M624.7,-240.22C659.23,-279.92 772.01,-409.55 815.08,-459.05"/>
<polygon fill="#757575" stroke="#757575" points="812.62,-461.56 821.82,-466.8 817.9,-456.96 812.62,-461.56"/>
</g>
<!-- nodejs/NodejsStreamInputAdapter.js -->
<g id="node20" class="node">
<title>nodejs/NodejsStreamInputAdapter.js</title>
<path fill="none" stroke="#c6c5fe" d="M943.33,-123C943.33,-123 722.67,-123 722.67,-123 718.83,-123 715,-119.17 715,-115.33 715,-115.33 715,-107.67 715,-107.67 715,-103.83 718.83,-100 722.67,-100 722.67,-100 943.33,-100 943.33,-100 947.17,-100 951,-103.83 951,-107.67 951,-107.67 951,-115.33 951,-115.33 951,-119.17 947.17,-123 943.33,-123"/>
<text text-anchor="middle" x="833" y="-107.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">nodejs/NodejsStreamInputAdapter.js</text>
</g>
<!-- object.js&#45;&gt;nodejs/NodejsStreamInputAdapter.js -->
<g id="edge22" class="edge">
<title>object.js&#45;&gt;nodejs/NodejsStreamInputAdapter.js</title>
<path fill="none" stroke="#757575" d="M627.66,-216.9C646.11,-201.09 681.22,-172.67 715,-154.5 736.24,-143.08 761.16,-133.45 782.53,-126.22"/>
<polygon fill="#757575" stroke="#757575" points="783.67,-129.53 792.07,-123.07 781.47,-122.88 783.67,-129.53"/>
</g>
<!-- stream/StreamHelper.js -->
<g id="node23" class="node">
<title>stream/StreamHelper.js</title>
<path fill="none" stroke="#c6c5fe" d="M1354.33,-66C1354.33,-66 1210.67,-66 1210.67,-66 1206.83,-66 1203,-62.17 1203,-58.33 1203,-58.33 1203,-50.67 1203,-50.67 1203,-46.83 1206.83,-43 1210.67,-43 1210.67,-43 1354.33,-43 1354.33,-43 1358.17,-43 1362,-46.83 1362,-50.67 1362,-50.67 1362,-58.33 1362,-58.33 1362,-62.17 1358.17,-66 1354.33,-66"/>
<text text-anchor="middle" x="1282.5" y="-50.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">stream/StreamHelper.js</text>
</g>
<!-- object.js&#45;&gt;stream/StreamHelper.js -->
<g id="edge23" class="edge">
<title>object.js&#45;&gt;stream/StreamHelper.js</title>
<path fill="none" stroke="#757575" d="M618.86,-216.93C629.97,-189.27 662.27,-119.47 715,-90.5 875.6,-2.26 1101.77,-23.21 1213.53,-41.29"/>
<polygon fill="#757575" stroke="#757575" points="1213.22,-44.78 1223.66,-42.97 1214.37,-37.88 1213.22,-44.78"/>
</g>
<!-- zipObject.js -->
<g id="node24" class="node">
<title>zipObject.js</title>
<path fill="none" stroke="#c6c5fe" d="M868.83,-220C868.83,-220 797.17,-220 797.17,-220 793.33,-220 789.5,-216.17 789.5,-212.33 789.5,-212.33 789.5,-204.67 789.5,-204.67 789.5,-200.83 793.33,-197 797.17,-197 797.17,-197 868.83,-197 868.83,-197 872.67,-197 876.5,-200.83 876.5,-204.67 876.5,-204.67 876.5,-212.33 876.5,-212.33 876.5,-216.17 872.67,-220 868.83,-220"/>
<text text-anchor="middle" x="833" y="-204.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">zipObject.js</text>
</g>
<!-- object.js&#45;&gt;zipObject.js -->
<g id="edge26" class="edge">
<title>object.js&#45;&gt;zipObject.js</title>
<path fill="none" stroke="#757575" d="M646.59,-225.55C681.45,-222.34 737.69,-217.17 779.1,-213.36"/>
<polygon fill="#757575" stroke="#757575" points="779.65,-216.83 789.29,-212.43 779.01,-209.86 779.65,-216.83"/>
</g>
<!-- license_header.js -->
<g id="node18" class="node">
<title>license_header.js</title>
<path fill="none" stroke="#cfffac" d="M143.33,-470C143.33,-470 37.67,-470 37.67,-470 33.83,-470 30,-466.17 30,-462.33 30,-462.33 30,-454.67 30,-454.67 30,-450.83 33.83,-447 37.67,-447 37.67,-447 143.33,-447 143.33,-447 147.17,-447 151,-450.83 151,-454.67 151,-454.67 151,-462.33 151,-462.33 151,-466.17 147.17,-470 143.33,-470"/>
<text text-anchor="middle" x="90.5" y="-454.8" font-family="Arial" font-size="14.00" fill="#cfffac">license_header.js</text>
</g>
<!-- zipEntries.js&#45;&gt;utils.js -->
<g id="edge51" class="edge">
<title>zipEntries.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M358.78,-648.1C378.66,-699.78 467.27,-906.5 612.5,-906.5 612.5,-906.5 612.5,-906.5 1283.5,-906.5 1444.91,-906.5 1526.7,-993.22 1646,-884.5 1702.34,-833.16 1707.73,-582.1 1708.05,-505.64"/>
<polygon fill="#757575" stroke="#757575" points="1711.55,-505.3 1708.07,-495.3 1704.55,-505.29 1711.55,-505.3"/>
</g>
<!-- reader/readerFor.js -->
<g id="node31" class="node">
<title>reader/readerFor.js</title>
<path fill="none" stroke="#c6c5fe" d="M671.33,-728C671.33,-728 555.67,-728 555.67,-728 551.83,-728 548,-724.17 548,-720.33 548,-720.33 548,-712.67 548,-712.67 548,-708.83 551.83,-705 555.67,-705 555.67,-705 671.33,-705 671.33,-705 675.17,-705 679,-708.83 679,-712.67 679,-712.67 679,-720.33 679,-720.33 679,-724.17 675.17,-728 671.33,-728"/>
<text text-anchor="middle" x="613.5" y="-712.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">reader/readerFor.js</text>
</g>
<!-- zipEntries.js&#45;&gt;reader/readerFor.js -->
<g id="edge50" class="edge">
<title>zipEntries.js&#45;&gt;reader/readerFor.js</title>
<path fill="none" stroke="#757575" d="M391.74,-648.05C437.66,-662.29 515.74,-686.5 565.68,-701.98"/>
<polygon fill="#757575" stroke="#757575" points="564.68,-705.34 575.27,-704.96 566.76,-698.65 564.68,-705.34"/>
</g>
<!-- zipEntry.js -->
<g id="node36" class="node">
<title>zipEntry.js</title>
<path fill="none" stroke="#c6c5fe" d="M504.33,-608C504.33,-608 441.67,-608 441.67,-608 437.83,-608 434,-604.17 434,-600.33 434,-600.33 434,-592.67 434,-592.67 434,-588.83 437.83,-585 441.67,-585 441.67,-585 504.33,-585 504.33,-585 508.17,-585 512,-588.83 512,-592.67 512,-592.67 512,-600.33 512,-600.33 512,-604.17 508.17,-608 504.33,-608"/>
<text text-anchor="middle" x="473" y="-592.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">zipEntry.js</text>
</g>
<!-- zipEntries.js&#45;&gt;zipEntry.js -->
<g id="edge52" class="edge">
<title>zipEntries.js&#45;&gt;zipEntry.js</title>
<path fill="none" stroke="#757575" d="M388.49,-624.93C400.91,-620.7 415.14,-615.86 428.29,-611.38"/>
<polygon fill="#757575" stroke="#757575" points="429.75,-614.58 438.09,-608.04 427.5,-607.95 429.75,-614.58"/>
</g>
<!-- nodejs/NodejsStreamInputAdapter.js&#45;&gt;utils.js -->
<g id="edge18" class="edge">
<title>nodejs/NodejsStreamInputAdapter.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M951.31,-109.85C1163.75,-108 1595,-109.88 1646,-154.5 1692.42,-195.11 1704.48,-394.49 1707.25,-461.49"/>
<polygon fill="#757575" stroke="#757575" points="1703.77,-461.87 1707.65,-471.73 1710.76,-461.61 1703.77,-461.87"/>
</g>
<!-- nodejs/NodejsStreamOutputAdapter.js -->
<g id="node21" class="node">
<title>nodejs/NodejsStreamOutputAdapter.js</title>
<path fill="none" stroke="#c6c5fe" d="M1638.33,-64C1638.33,-64 1405.67,-64 1405.67,-64 1401.83,-64 1398,-60.17 1398,-56.33 1398,-56.33 1398,-48.67 1398,-48.67 1398,-44.83 1401.83,-41 1405.67,-41 1405.67,-41 1638.33,-41 1638.33,-41 1642.17,-41 1646,-44.83 1646,-48.67 1646,-48.67 1646,-56.33 1646,-56.33 1646,-60.17 1642.17,-64 1638.33,-64"/>
<text text-anchor="middle" x="1522" y="-48.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">nodejs/NodejsStreamOutputAdapter.js</text>
</g>
<!-- nodejs/NodejsStreamOutputAdapter.js&#45;&gt;utils.js -->
<g id="edge19" class="edge">
<title>nodejs/NodejsStreamOutputAdapter.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1626.86,-64.14C1633.94,-67.92 1640.45,-72.63 1646,-78.5 1698.86,-134.43 1706.66,-385.18 1707.8,-461.43"/>
<polygon fill="#757575" stroke="#757575" points="1704.31,-461.79 1707.93,-471.75 1711.31,-461.7 1704.31,-461.79"/>
</g>
<!-- nodejsUtils.js -->
<g id="node22" class="node">
<title>nodejsUtils.js</title>
<path fill="none" stroke="#cfffac" d="M130.83,-511C130.83,-511 50.17,-511 50.17,-511 46.33,-511 42.5,-507.17 42.5,-503.33 42.5,-503.33 42.5,-495.67 42.5,-495.67 42.5,-491.83 46.33,-488 50.17,-488 50.17,-488 130.83,-488 130.83,-488 134.67,-488 138.5,-491.83 138.5,-495.67 138.5,-495.67 138.5,-503.33 138.5,-503.33 138.5,-507.17 134.67,-511 130.83,-511"/>
<text text-anchor="middle" x="90.5" y="-495.8" font-family="Arial" font-size="14.00" fill="#cfffac">nodejsUtils.js</text>
</g>
<!-- stream/StreamHelper.js&#45;&gt;utils.js -->
<g id="edge48" class="edge">
<title>stream/StreamHelper.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1354.06,-66.05C1458.79,-83.26 1643.42,-113.91 1646,-116.5 1694.96,-165.56 1705.4,-390.11 1707.5,-461.58"/>
<polygon fill="#757575" stroke="#757575" points="1704,-461.79 1707.77,-471.69 1711,-461.6 1704,-461.79"/>
</g>
<!-- stream/StreamHelper.js&#45;&gt;nodejs/NodejsStreamOutputAdapter.js -->
<g id="edge46" class="edge">
<title>stream/StreamHelper.js&#45;&gt;nodejs/NodejsStreamOutputAdapter.js</title>
<path fill="none" stroke="#757575" d="M1362.25,-53.84C1370.5,-53.77 1379.04,-53.7 1387.69,-53.62"/>
<polygon fill="#757575" stroke="#757575" points="1387.91,-57.12 1397.88,-53.54 1387.85,-50.12 1387.91,-57.12"/>
</g>
<!-- stream/ConvertWorker.js -->
<g id="node33" class="node">
<title>stream/ConvertWorker.js</title>
<path fill="none" stroke="#c6c5fe" d="M1597.33,-23C1597.33,-23 1446.67,-23 1446.67,-23 1442.83,-23 1439,-19.17 1439,-15.33 1439,-15.33 1439,-7.67 1439,-7.67 1439,-3.83 1442.83,0 1446.67,0 1446.67,0 1597.33,0 1597.33,0 1601.17,0 1605,-3.83 1605,-7.67 1605,-7.67 1605,-15.33 1605,-15.33 1605,-19.17 1601.17,-23 1597.33,-23"/>
<text text-anchor="middle" x="1522" y="-7.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">stream/ConvertWorker.js</text>
</g>
<!-- stream/StreamHelper.js&#45;&gt;stream/ConvertWorker.js -->
<g id="edge47" class="edge">
<title>stream/StreamHelper.js&#45;&gt;stream/ConvertWorker.js</title>
<path fill="none" stroke="#757575" d="M1338.75,-42.97C1357.52,-39.17 1378.65,-35.02 1398,-31.5 1410.72,-29.19 1424.21,-26.87 1437.41,-24.66"/>
<polygon fill="#757575" stroke="#757575" points="1438.13,-28.09 1447.43,-23.01 1436.99,-21.19 1438.13,-28.09"/>
</g>
<!-- zipObject.js&#45;&gt;compressedObject.js -->
<g id="edge59" class="edge">
<title>zipObject.js&#45;&gt;compressedObject.js</title>
<path fill="none" stroke="#757575" d="M876.59,-214.08C899.3,-218.01 927.36,-224.41 951,-234.5 968.69,-242.05 969.57,-250.38 987,-258.5 996.4,-262.88 1006.7,-266.7 1016.86,-269.97"/>
<polygon fill="#757575" stroke="#757575" points="1015.93,-273.35 1026.52,-272.93 1017.99,-266.65 1015.93,-273.35"/>
</g>
<!-- zipObject.js&#45;&gt;stream/DataWorker.js -->
<g id="edge60" class="edge">
<title>zipObject.js&#45;&gt;stream/DataWorker.js</title>
<path fill="none" stroke="#757575" d="M876.54,-206.49C940.6,-204.53 1065.15,-204.91 1167,-230.5 1197.57,-238.18 1230.08,-253.74 1252.61,-265.84"/>
<polygon fill="#757575" stroke="#757575" points="1251.18,-269.05 1261.63,-270.79 1254.54,-262.91 1251.18,-269.05"/>
</g>
<!-- zipObject.js&#45;&gt;utf8.js -->
<g id="edge62" class="edge">
<title>zipObject.js&#45;&gt;utf8.js</title>
<path fill="none" stroke="#757575" d="M876.56,-214.91C963.47,-228.1 1154.29,-257.62 1167,-264.5 1187.75,-275.73 1185.5,-287.68 1203,-303.5 1219.75,-318.64 1240.19,-333.91 1256.01,-345.12"/>
<polygon fill="#757575" stroke="#757575" points="1254.07,-348.04 1264.27,-350.91 1258.08,-342.3 1254.07,-348.04"/>
</g>
<!-- zipObject.js&#45;&gt;stream/StreamHelper.js -->
<g id="edge61" class="edge">
<title>zipObject.js&#45;&gt;stream/StreamHelper.js</title>
<path fill="none" stroke="#757575" d="M867.64,-196.92C947.67,-169.38 1149.36,-99.97 1238.27,-69.38"/>
<polygon fill="#757575" stroke="#757575" points="1239.46,-72.67 1247.78,-66.1 1237.19,-66.05 1239.46,-72.67"/>
</g>
<!-- readable&#45;stream&#45;browser.js -->
<g id="node25" class="node">
<title>readable&#45;stream&#45;browser.js</title>
<path fill="none" stroke="#cfffac" d="M173.33,-552C173.33,-552 7.67,-552 7.67,-552 3.83,-552 0,-548.17 0,-544.33 0,-544.33 0,-536.67 0,-536.67 0,-532.83 3.83,-529 7.67,-529 7.67,-529 173.33,-529 173.33,-529 177.17,-529 181,-532.83 181,-536.67 181,-536.67 181,-544.33 181,-544.33 181,-548.17 177.17,-552 173.33,-552"/>
<text text-anchor="middle" x="90.5" y="-536.8" font-family="Arial" font-size="14.00" fill="#cfffac">readable&#45;stream&#45;browser.js</text>
</g>
<!-- reader/ArrayReader.js -->
<g id="node26" class="node">
<title>reader/ArrayReader.js</title>
<path fill="none" stroke="#c6c5fe" d="M1349.33,-802C1349.33,-802 1215.67,-802 1215.67,-802 1211.83,-802 1208,-798.17 1208,-794.33 1208,-794.33 1208,-786.67 1208,-786.67 1208,-782.83 1211.83,-779 1215.67,-779 1215.67,-779 1349.33,-779 1349.33,-779 1353.17,-779 1357,-782.83 1357,-786.67 1357,-786.67 1357,-794.33 1357,-794.33 1357,-798.17 1353.17,-802 1349.33,-802"/>
<text text-anchor="middle" x="1282.5" y="-786.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">reader/ArrayReader.js</text>
</g>
<!-- reader/ArrayReader.js&#45;&gt;utils.js -->
<g id="edge28" class="edge">
<title>reader/ArrayReader.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1357.26,-795.2C1450.61,-799.5 1604.29,-800.54 1646,-764.5 1685.42,-730.44 1701.76,-565.37 1706.46,-505.36"/>
<polygon fill="#757575" stroke="#757575" points="1709.97,-505.37 1707.23,-495.13 1702.99,-504.84 1709.97,-505.37"/>
</g>
<!-- reader/DataReader.js -->
<g id="node27" class="node">
<title>reader/DataReader.js</title>
<path fill="none" stroke="#c6c5fe" d="M1586.83,-840C1586.83,-840 1457.17,-840 1457.17,-840 1453.33,-840 1449.5,-836.17 1449.5,-832.33 1449.5,-832.33 1449.5,-824.67 1449.5,-824.67 1449.5,-820.83 1453.33,-817 1457.17,-817 1457.17,-817 1586.83,-817 1586.83,-817 1590.67,-817 1594.5,-820.83 1594.5,-824.67 1594.5,-824.67 1594.5,-832.33 1594.5,-832.33 1594.5,-836.17 1590.67,-840 1586.83,-840"/>
<text text-anchor="middle" x="1522" y="-824.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">reader/DataReader.js</text>
</g>
<!-- reader/ArrayReader.js&#45;&gt;reader/DataReader.js -->
<g id="edge27" class="edge">
<title>reader/ArrayReader.js&#45;&gt;reader/DataReader.js</title>
<path fill="none" stroke="#757575" d="M1355.4,-802C1381.91,-806.25 1412.13,-811.08 1439.43,-815.45"/>
<polygon fill="#757575" stroke="#757575" points="1438.91,-818.91 1449.34,-817.03 1440.02,-812 1438.91,-818.91"/>
</g>
<!-- reader/DataReader.js&#45;&gt;utils.js -->
<g id="edge29" class="edge">
<title>reader/DataReader.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1594.67,-827.36C1613.57,-823.63 1632.45,-816.27 1646,-802.5 1687.8,-760.01 1702.84,-570.31 1706.81,-505.38"/>
<polygon fill="#757575" stroke="#757575" points="1710.33,-505.28 1707.41,-495.1 1703.34,-504.87 1710.33,-505.28"/>
</g>
<!-- reader/NodeBufferReader.js -->
<g id="node28" class="node">
<title>reader/NodeBufferReader.js</title>
<path fill="none" stroke="#c6c5fe" d="M917.83,-688C917.83,-688 748.17,-688 748.17,-688 744.33,-688 740.5,-684.17 740.5,-680.33 740.5,-680.33 740.5,-672.67 740.5,-672.67 740.5,-668.83 744.33,-665 748.17,-665 748.17,-665 917.83,-665 917.83,-665 921.67,-665 925.5,-668.83 925.5,-672.67 925.5,-672.67 925.5,-680.33 925.5,-680.33 925.5,-684.17 921.67,-688 917.83,-688"/>
<text text-anchor="middle" x="833" y="-672.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">reader/NodeBufferReader.js</text>
</g>
<!-- reader/NodeBufferReader.js&#45;&gt;utils.js -->
<g id="edge31" class="edge">
<title>reader/NodeBufferReader.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M925.72,-679.23C1125.93,-684.4 1588.79,-691.66 1646,-650.5 1693.09,-616.62 1704.46,-542.72 1707.17,-505.64"/>
<polygon fill="#757575" stroke="#757575" points="1710.68,-505.59 1707.77,-495.4 1703.69,-505.18 1710.68,-505.59"/>
</g>
<!-- reader/Uint8ArrayReader.js -->
<g id="node29" class="node">
<title>reader/Uint8ArrayReader.js</title>
<path fill="none" stroke="#c6c5fe" d="M1159.33,-726C1159.33,-726 994.67,-726 994.67,-726 990.83,-726 987,-722.17 987,-718.33 987,-718.33 987,-710.67 987,-710.67 987,-706.83 990.83,-703 994.67,-703 994.67,-703 1159.33,-703 1159.33,-703 1163.17,-703 1167,-706.83 1167,-710.67 1167,-710.67 1167,-718.33 1167,-718.33 1167,-722.17 1163.17,-726 1159.33,-726"/>
<text text-anchor="middle" x="1077" y="-710.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">reader/Uint8ArrayReader.js</text>
</g>
<!-- reader/NodeBufferReader.js&#45;&gt;reader/Uint8ArrayReader.js -->
<g id="edge30" class="edge">
<title>reader/NodeBufferReader.js&#45;&gt;reader/Uint8ArrayReader.js</title>
<path fill="none" stroke="#757575" d="M907.26,-688C934.15,-692.23 964.78,-697.04 992.5,-701.39"/>
<polygon fill="#757575" stroke="#757575" points="992.15,-704.88 1002.57,-702.97 993.23,-697.96 992.15,-704.88"/>
</g>
<!-- reader/Uint8ArrayReader.js&#45;&gt;utils.js -->
<g id="edge35" class="edge">
<title>reader/Uint8ArrayReader.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1167.07,-722.47C1307.97,-733.13 1573.75,-744.74 1646,-688.5 1703.1,-644.05 1709.22,-548.71 1708.86,-505.54"/>
<polygon fill="#757575" stroke="#757575" points="1712.36,-505.22 1708.65,-495.3 1705.36,-505.37 1712.36,-505.22"/>
</g>
<!-- reader/Uint8ArrayReader.js&#45;&gt;reader/ArrayReader.js -->
<g id="edge34" class="edge">
<title>reader/Uint8ArrayReader.js&#45;&gt;reader/ArrayReader.js</title>
<path fill="none" stroke="#757575" d="M1108.91,-726.04C1144.27,-739.25 1202.3,-760.92 1241.19,-775.44"/>
<polygon fill="#757575" stroke="#757575" points="1240.04,-778.75 1250.64,-778.97 1242.49,-772.2 1240.04,-778.75"/>
</g>
<!-- reader/StringReader.js -->
<g id="node30" class="node">
<title>reader/StringReader.js</title>
<path fill="none" stroke="#c6c5fe" d="M1350.83,-843C1350.83,-843 1214.17,-843 1214.17,-843 1210.33,-843 1206.5,-839.17 1206.5,-835.33 1206.5,-835.33 1206.5,-827.67 1206.5,-827.67 1206.5,-823.83 1210.33,-820 1214.17,-820 1214.17,-820 1350.83,-820 1350.83,-820 1354.67,-820 1358.5,-823.83 1358.5,-827.67 1358.5,-827.67 1358.5,-835.33 1358.5,-835.33 1358.5,-839.17 1354.67,-843 1350.83,-843"/>
<text text-anchor="middle" x="1282.5" y="-827.8" font-family="Arial" font-size="14.00" fill="#c6c5fe">reader/StringReader.js</text>
</g>
<!-- reader/StringReader.js&#45;&gt;utils.js -->
<g id="edge33" class="edge">
<title>reader/StringReader.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1308.71,-843.08C1373.39,-870.87 1547.09,-933.07 1646,-849.5 1698.92,-804.79 1706.68,-577.03 1707.81,-505.22"/>
<polygon fill="#757575" stroke="#757575" points="1711.31,-505.13 1707.94,-495.08 1704.31,-505.04 1711.31,-505.13"/>
</g>
<!-- reader/StringReader.js&#45;&gt;reader/DataReader.js -->
<g id="edge32" class="edge">
<title>reader/StringReader.js&#45;&gt;reader/DataReader.js</title>
<path fill="none" stroke="#757575" d="M1358.65,-830.55C1384.2,-830.23 1412.88,-829.87 1438.95,-829.54"/>
<polygon fill="#757575" stroke="#757575" points="1439.22,-833.03 1449.18,-829.41 1439.13,-826.03 1439.22,-833.03"/>
</g>
<!-- reader/readerFor.js&#45;&gt;utils.js -->
<g id="edge40" class="edge">
<title>reader/readerFor.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M679,-725.88C690.95,-727.32 703.33,-728.62 715,-729.5 835.58,-738.6 866.09,-733.89 987,-735.5 1060.23,-736.48 1589.14,-769.66 1646,-723.5 1680.16,-695.77 1699.3,-559.25 1705.63,-505.4"/>
<polygon fill="#757575" stroke="#757575" points="1709.13,-505.56 1706.78,-495.23 1702.17,-504.77 1709.13,-505.56"/>
</g>
<!-- reader/readerFor.js&#45;&gt;reader/ArrayReader.js -->
<g id="edge36" class="edge">
<title>reader/readerFor.js&#45;&gt;reader/ArrayReader.js</title>
<path fill="none" stroke="#757575" d="M631.34,-728.18C650.57,-740.78 683.47,-760.17 715,-768.5 881.57,-812.48 1085.19,-806.6 1197.33,-798.49"/>
<polygon fill="#757575" stroke="#757575" points="1197.85,-801.96 1207.56,-797.72 1197.33,-794.98 1197.85,-801.96"/>
</g>
<!-- reader/readerFor.js&#45;&gt;reader/NodeBufferReader.js -->
<g id="edge37" class="edge">
<title>reader/readerFor.js&#45;&gt;reader/NodeBufferReader.js</title>
<path fill="none" stroke="#757575" d="M677.12,-704.98C702.82,-700.26 732.75,-694.75 759.41,-689.85"/>
<polygon fill="#757575" stroke="#757575" points="760.25,-693.26 769.45,-688 758.98,-686.37 760.25,-693.26"/>
</g>
<!-- reader/readerFor.js&#45;&gt;reader/Uint8ArrayReader.js -->
<g id="edge39" class="edge">
<title>reader/readerFor.js&#45;&gt;reader/Uint8ArrayReader.js</title>
<path fill="none" stroke="#757575" d="M679.16,-716.22C756.43,-715.88 886.56,-715.32 976.64,-714.93"/>
<polygon fill="#757575" stroke="#757575" points="976.88,-718.43 986.87,-714.89 976.85,-711.43 976.88,-718.43"/>
</g>
<!-- reader/readerFor.js&#45;&gt;reader/StringReader.js -->
<g id="edge38" class="edge">
<title>reader/readerFor.js&#45;&gt;reader/StringReader.js</title>
<path fill="none" stroke="#757575" d="M624.19,-728.1C640.63,-746.82 676.03,-783.15 715,-798.5 875.83,-861.85 1082.3,-853.86 1196.31,-842.57"/>
<polygon fill="#757575" stroke="#757575" points="1196.79,-846.04 1206.38,-841.54 1196.07,-839.07 1196.79,-846.04"/>
</g>
<!-- signature.js -->
<g id="node32" class="node">
<title>signature.js</title>
<path fill="none" stroke="#cfffac" d="M125.83,-593C125.83,-593 55.17,-593 55.17,-593 51.33,-593 47.5,-589.17 47.5,-585.33 47.5,-585.33 47.5,-577.67 47.5,-577.67 47.5,-573.83 51.33,-570 55.17,-570 55.17,-570 125.83,-570 125.83,-570 129.67,-570 133.5,-573.83 133.5,-577.67 133.5,-577.67 133.5,-585.33 133.5,-585.33 133.5,-589.17 129.67,-593 125.83,-593"/>
<text text-anchor="middle" x="90.5" y="-577.8" font-family="Arial" font-size="14.00" fill="#cfffac">signature.js</text>
</g>
<!-- stream/ConvertWorker.js&#45;&gt;utils.js -->
<g id="edge41" class="edge">
<title>stream/ConvertWorker.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M1605.11,-10.29C1620.43,-13.8 1635.05,-20.27 1646,-31.5 1706.48,-93.51 1708.87,-379.48 1708.29,-461.47"/>
<polygon fill="#757575" stroke="#757575" points="1704.79,-461.61 1708.19,-471.64 1711.78,-461.68 1704.79,-461.61"/>
</g>
<!-- stream/GenericWorker.js -->
<g id="node34" class="node">
<title>stream/GenericWorker.js</title>
<path fill="none" stroke="#cfffac" d="M166.33,-634C166.33,-634 14.67,-634 14.67,-634 10.83,-634 7,-630.17 7,-626.33 7,-626.33 7,-618.67 7,-618.67 7,-614.83 10.83,-611 14.67,-611 14.67,-611 166.33,-611 166.33,-611 170.17,-611 174,-614.83 174,-618.67 174,-618.67 174,-626.33 174,-626.33 174,-630.17 170.17,-634 166.33,-634"/>
<text text-anchor="middle" x="90.5" y="-618.8" font-family="Arial" font-size="14.00" fill="#cfffac">stream/GenericWorker.js</text>
</g>
<!-- support.js -->
<g id="node35" class="node">
<title>support.js</title>
<path fill="none" stroke="#cfffac" d="M120.33,-675C120.33,-675 60.67,-675 60.67,-675 56.83,-675 53,-671.17 53,-667.33 53,-667.33 53,-659.67 53,-659.67 53,-655.83 56.83,-652 60.67,-652 60.67,-652 120.33,-652 120.33,-652 124.17,-652 128,-655.83 128,-659.67 128,-659.67 128,-667.33 128,-667.33 128,-671.17 124.17,-675 120.33,-675"/>
<text text-anchor="middle" x="90.5" y="-659.8" font-family="Arial" font-size="14.00" fill="#cfffac">support.js</text>
</g>
<!-- zipEntry.js&#45;&gt;compressedObject.js -->
<g id="edge53" class="edge">
<title>zipEntry.js&#45;&gt;compressedObject.js</title>
<path fill="none" stroke="#757575" d="M481.75,-584.89C494.31,-566.55 520.51,-530.33 548,-504.5 614.23,-442.27 632.98,-425.64 715,-386.5 808.66,-341.8 925.59,-313.23 1000.53,-298.03"/>
<polygon fill="#757575" stroke="#757575" points="1001.25,-301.45 1010.37,-296.06 999.87,-294.59 1001.25,-301.45"/>
</g>
<!-- zipEntry.js&#45;&gt;compressions.js -->
<g id="edge54" class="edge">
<title>zipEntry.js&#45;&gt;compressions.js</title>
<path fill="none" stroke="#757575" d="M512.11,-596.5C612.17,-596.5 883.28,-596.5 1009.58,-596.5"/>
<polygon fill="#757575" stroke="#757575" points="1009.88,-600 1019.88,-596.5 1009.88,-593 1009.88,-600"/>
</g>
<!-- zipEntry.js&#45;&gt;crc32.js -->
<g id="edge55" class="edge">
<title>zipEntry.js&#45;&gt;crc32.js</title>
<path fill="none" stroke="#757575" d="M512.19,-592.38C675.12,-574.79 1311.33,-506.13 1479.85,-487.94"/>
<polygon fill="#757575" stroke="#757575" points="1480.47,-491.39 1490.04,-486.84 1479.72,-484.44 1480.47,-491.39"/>
</g>
<!-- zipEntry.js&#45;&gt;utils.js -->
<g id="edge58" class="edge">
<title>zipEntry.js&#45;&gt;utils.js</title>
<path fill="none" stroke="#757575" d="M512.29,-604.47C523.74,-606.65 536.35,-608.87 548,-610.5 673.53,-628.08 705.25,-636.5 832,-636.5 832,-636.5 832,-636.5 1283.5,-636.5 1364.17,-636.5 1579.19,-662.71 1646,-617.5 1684,-591.78 1699.17,-536.51 1704.86,-505.59"/>
<polygon fill="#757575" stroke="#757575" points="1708.37,-505.84 1706.55,-495.4 1701.46,-504.69 1708.37,-505.84"/>
</g>
<!-- zipEntry.js&#45;&gt;utf8.js -->
<g id="edge57" class="edge">
<title>zipEntry.js&#45;&gt;utf8.js</title>
<path fill="none" stroke="#757575" d="M488.53,-584.79C503.1,-573.32 526.42,-555.68 548,-542.5 618.91,-499.2 636.06,-484.39 715,-458.5 815.21,-425.63 1136.39,-381.56 1245.12,-367.22"/>
<polygon fill="#757575" stroke="#757575" points="1245.8,-370.66 1255.26,-365.89 1244.89,-363.72 1245.8,-370.66"/>
</g>
<!-- zipEntry.js&#45;&gt;reader/readerFor.js -->
<g id="edge56" class="edge">
<title>zipEntry.js&#45;&gt;reader/readerFor.js</title>
<path fill="none" stroke="#757575" d="M487.28,-608.01C511.31,-628.83 562.21,-672.93 591.17,-698.02"/>
<polygon fill="#757575" stroke="#757575" points="589.21,-700.95 599.06,-704.85 593.79,-695.66 589.21,-700.95"/>
</g>
</g>
</svg>
