# Change Log

All notable changes to the "SystemVerilog LSP" extension will be documented in this file.

## [0.1.0] - 2024-07-12

### Added
- Initial release of SystemVerilog LSP extension
- Basic SystemVerilog language support through LSP
- Syntax highlighting for .sv, .svh, .v, .vh files
- Language server integration with sv-lsp
- Auto-completion support
- Go to definition functionality
- Find references capability
- Hover information display
- Document and workspace symbol navigation
- Configurable language server settings
- Status bar integration
- Command palette commands for server management

### Features
- **Language Support**: Full SystemVerilog and Verilog syntax support
- **IntelliSense**: Context-aware auto-completion
- **Navigation**: Go to definition and find references
- **Symbols**: Document and workspace symbol browsing
- **Configuration**: Flexible LSP server configuration
- **Error Detection**: Real-time syntax and semantic analysis

### Configuration Options
- Enable/disable language server
- Custom server path configuration
- Python interpreter path setting
- Include paths for project files
- Preprocessor defines support
- Debug tracing options

### Commands
- `systemverilog.restartServer`: Restart the language server
- `systemverilog.showOutputChannel`: Show server output

### File Associations
- `.sv` - SystemVerilog source files
- `.svh` - SystemVerilog header files
- `.v` - Verilog source files
- `.vh` - Verilog header files

## [Unreleased]

### Planned Features
- Enhanced debugging support
- Code formatting and refactoring
- Integration with simulation tools
- Advanced project management
- Custom code snippets
- Improved error diagnostics
- Performance optimizations

---

## Release Notes

### 0.1.0

This is the initial release of the SystemVerilog LSP extension for VSCode. It provides basic language support through the Language Server Protocol, enabling modern IDE features for SystemVerilog development.

**Key Features:**
- Complete LSP integration with sv-lsp server
- Rich syntax highlighting
- IntelliSense and navigation features
- Configurable server settings
- Cross-platform support

**Getting Started:**
1. Install the extension
2. Open a SystemVerilog project
3. Configure include paths and defines as needed
4. Start coding with enhanced language support!

For detailed usage instructions, see the [README](README.md).
