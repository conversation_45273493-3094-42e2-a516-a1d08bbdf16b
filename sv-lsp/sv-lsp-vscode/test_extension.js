#!/usr/bin/env node

/**
 * Test script for SystemVerilog LSP VSCode extension
 * This script helps verify the extension functionality
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 SystemVerilog LSP Extension Test');
console.log('=====================================');

// Check if required files exist
const requiredFiles = [
    'package.json',
    'out/extension.js',
    'syntaxes/systemverilog.tmGrammar.json',
    'language-configuration.json',
    'README.md',
    'CHANGELOG.md'
];

console.log('\n📁 Checking required files:');
let allFilesExist = true;

requiredFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
    if (!exists) allFilesExist = false;
});

// Check package.json structure
console.log('\n📦 Checking package.json:');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    const requiredFields = [
        'name', 'displayName', 'description', 'version', 'publisher',
        'engines', 'categories', 'main', 'contributes', 'activationEvents'
    ];
    
    requiredFields.forEach(field => {
        const exists = packageJson.hasOwnProperty(field);
        console.log(`  ${exists ? '✅' : '❌'} ${field}`);
    });
    
    // Check contributes section
    if (packageJson.contributes) {
        console.log('\n🔧 Checking contributes section:');
        const contributeFields = ['languages', 'grammars', 'configuration', 'commands'];
        contributeFields.forEach(field => {
            const exists = packageJson.contributes.hasOwnProperty(field);
            console.log(`  ${exists ? '✅' : '❌'} ${field}`);
        });
    }
    
} catch (error) {
    console.log('  ❌ Error reading package.json:', error.message);
    allFilesExist = false;
}

// Check syntax grammar
console.log('\n🎨 Checking syntax grammar:');
try {
    const grammar = JSON.parse(fs.readFileSync('syntaxes/systemverilog.tmGrammar.json', 'utf8'));
    
    const requiredGrammarFields = ['name', 'scopeName', 'patterns', 'repository'];
    requiredGrammarFields.forEach(field => {
        const exists = grammar.hasOwnProperty(field);
        console.log(`  ${exists ? '✅' : '❌'} ${field}`);
    });
    
    if (grammar.repository) {
        const repositoryFields = ['comments', 'keywords', 'strings', 'numbers'];
        repositoryFields.forEach(field => {
            const exists = grammar.repository.hasOwnProperty(field);
            console.log(`  ${exists ? '✅' : '❌'} repository.${field}`);
        });
    }
    
} catch (error) {
    console.log('  ❌ Error reading syntax grammar:', error.message);
    allFilesExist = false;
}

// Check compiled output
console.log('\n🔨 Checking compiled output:');
const outFiles = ['out/extension.js', 'out/extension.js.map'];
outFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
    if (!exists) allFilesExist = false;
});

// Check test SystemVerilog file
console.log('\n📄 Checking test SystemVerilog file:');
const testFile = 'test_example.sv';
if (fs.existsSync(testFile)) {
    console.log(`  ✅ ${testFile} exists`);
    
    const content = fs.readFileSync(testFile, 'utf8');
    const features = [
        { name: 'module declaration', pattern: /module\s+\w+/ },
        { name: 'interface declaration', pattern: /interface\s+\w+/ },
        { name: 'package declaration', pattern: /package\s+\w+/ },
        { name: 'function definition', pattern: /function\s+\w+/ },
        { name: 'task definition', pattern: /task\s+\w+/ },
        { name: 'always blocks', pattern: /always_\w+/ },
        { name: 'typedef', pattern: /typedef\s+/ },
        { name: 'enum', pattern: /enum\s+/ },
        { name: 'struct', pattern: /struct\s+/ }
    ];
    
    console.log('  📋 SystemVerilog features in test file:');
    features.forEach(feature => {
        const found = feature.pattern.test(content);
        console.log(`    ${found ? '✅' : '❌'} ${feature.name}`);
    });
} else {
    console.log(`  ❌ ${testFile} not found`);
    allFilesExist = false;
}

// Final result
console.log('\n🎯 Test Results:');
if (allFilesExist) {
    console.log('✅ All checks passed! Extension is ready for testing.');
    console.log('\n📝 Next steps:');
    console.log('1. Open VSCode');
    console.log('2. Press F5 to launch Extension Development Host');
    console.log('3. Open a .sv file to test the extension');
    console.log('4. Check that syntax highlighting works');
    console.log('5. Verify LSP features (if sv-lsp server is available)');
} else {
    console.log('❌ Some checks failed. Please fix the issues above.');
    process.exit(1);
}

console.log('\n🚀 Happy coding with SystemVerilog LSP!');
