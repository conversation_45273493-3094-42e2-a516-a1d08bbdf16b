# SystemVerilog LSP for VSCode

A powerful VSCode extension that provides SystemVerilog language support through the Language Server Protocol (LSP).

## Features

- **Syntax Highlighting**: Rich syntax highlighting for SystemVerilog files
- **IntelliSense**: Auto-completion for SystemVerilog keywords, modules, and symbols
- **Go to Definition**: Navigate to symbol definitions across your project
- **Find References**: Find all references to symbols in your codebase
- **Hover Information**: Get detailed information about symbols on hover
- **Document Symbols**: Navigate through symbols in the current document
- **Workspace Symbols**: Search for symbols across the entire workspace
- **Error Detection**: Real-time syntax and semantic error detection
- **Code Formatting**: Format your SystemVerilog code automatically

## Requirements

- VSCode 1.74.0 or higher
- Python 3.8 or higher
- SystemVerilog LSP server (sv-lsp)

## Installation

### From VSCode Marketplace (Coming Soon)

1. Open VSCode
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "SystemVerilog LSP"
4. Click Install

### Manual Installation

1. Clone this repository
2. Install dependencies: `npm install`
3. Compile the extension: `npm run compile`
4. Package the extension: `npm run package`
5. Install the generated .vsix file in VSCode

## Configuration

The extension can be configured through VSCode settings:

```json
{
  "systemverilog.lsp.enabled": true,
  "systemverilog.lsp.serverPath": "",
  "systemverilog.lsp.pythonPath": "python3",
  "systemverilog.lsp.includePaths": [],
  "systemverilog.lsp.defines": {},
  "systemverilog.lsp.trace.server": "off"
}
```

### Settings Description

- `systemverilog.lsp.enabled`: Enable/disable the SystemVerilog Language Server
- `systemverilog.lsp.serverPath`: Custom path to the LSP server executable
- `systemverilog.lsp.pythonPath`: Path to Python interpreter
- `systemverilog.lsp.includePaths`: Array of include paths for SystemVerilog files
- `systemverilog.lsp.defines`: Object containing preprocessor defines
- `systemverilog.lsp.trace.server`: Trace communication with the language server

## Usage

1. Open a SystemVerilog file (.sv, .svh, .v, .vh)
2. The extension will automatically activate and start the language server
3. Enjoy enhanced SystemVerilog development experience!

## Commands

- `SystemVerilog: Restart Language Server` - Restart the LSP server
- `SystemVerilog: Show Output` - Show the language server output channel

## Supported File Extensions

- `.sv` - SystemVerilog files
- `.svh` - SystemVerilog header files
- `.v` - Verilog files
- `.vh` - Verilog header files

## Development

### Building from Source

```bash
# Clone the repository
git clone https://github.com/your-username/sv-lsp-vscode.git
cd sv-lsp-vscode

# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Watch for changes during development
npm run watch
```

### Testing

```bash
# Run tests
npm test

# Run linting
npm run lint
```

### Packaging

```bash
# Create .vsix package
npm run package
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built on top of the [sv-lsp](https://github.com/your-username/sv-lsp) language server
- Uses the VSCode Language Client for LSP integration
- SystemVerilog syntax highlighting based on community contributions

## Support

If you encounter any issues or have feature requests, please:

1. Check the [Issues](https://github.com/your-username/sv-lsp-vscode/issues) page
2. Create a new issue with detailed information
3. Include your VSCode version, extension version, and sample code if applicable

## Roadmap

- [ ] Enhanced debugging support
- [ ] Code refactoring capabilities
- [ ] Integration with simulation tools
- [ ] Advanced project management features
- [ ] Custom snippet support
