"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const node_1 = require("vscode-languageclient/node");
let client;
function activate(context) {
    console.log('SystemVerilog LSP extension is now active!');
    // Register commands
    registerCommands(context);
    // Start the language server
    startLanguageServer(context);
}
exports.activate = activate;
function deactivate() {
    if (!client) {
        return undefined;
    }
    return client.stop();
}
exports.deactivate = deactivate;
function registerCommands(context) {
    // Restart server command
    const restartCommand = vscode.commands.registerCommand('systemverilog.restartServer', async () => {
        if (client) {
            await client.stop();
            startLanguageServer(context);
            vscode.window.showInformationMessage('SystemVerilog Language Server restarted');
        }
    });
    // Show output channel command
    const showOutputCommand = vscode.commands.registerCommand('systemverilog.showOutputChannel', () => {
        if (client) {
            client.outputChannel.show();
        }
    });
    context.subscriptions.push(restartCommand, showOutputCommand);
}
function startLanguageServer(context) {
    const config = vscode.workspace.getConfiguration('systemverilog.lsp');
    if (!config.get('enabled', true)) {
        console.log('SystemVerilog LSP is disabled');
        return;
    }
    // Server options
    const serverOptions = getServerOptions(config);
    // Client options
    const clientOptions = {
        documentSelector: [
            { scheme: 'file', language: 'systemverilog' },
            { scheme: 'file', pattern: '**/*.{sv,svh,v,vh}' }
        ],
        synchronize: {
            fileEvents: [
                vscode.workspace.createFileSystemWatcher('**/*.{sv,svh,v,vh}'),
                vscode.workspace.createFileSystemWatcher('**/*.{json,toml,yaml,yml}') // Config files
            ]
        },
        outputChannelName: 'SystemVerilog LSP',
        traceOutputChannel: vscode.window.createOutputChannel('SystemVerilog LSP Trace'),
        revealOutputChannelOn: 4,
        initializationOptions: {
            includePaths: config.get('includePaths', []),
            defines: config.get('defines', {}),
            workspaceRoot: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
        }
    };
    // Create the language client
    client = new node_1.LanguageClient('systemverilog-lsp', 'SystemVerilog Language Server', serverOptions, clientOptions);
    // Start the client
    client.start().then(() => {
        console.log('SystemVerilog Language Server started successfully');
        // Show status in status bar
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
        statusBarItem.text = "$(check) SystemVerilog LSP";
        statusBarItem.tooltip = "SystemVerilog Language Server is running";
        statusBarItem.command = 'systemverilog.showOutputChannel';
        statusBarItem.show();
        context.subscriptions.push(statusBarItem);
    }).catch((error) => {
        console.error('Failed to start SystemVerilog Language Server:', error);
        vscode.window.showErrorMessage(`Failed to start SystemVerilog Language Server: ${error.message}`);
    });
    context.subscriptions.push(client);
}
function getServerOptions(config) {
    const serverPath = config.get('serverPath');
    const pythonPath = config.get('pythonPath', 'python3');
    if (serverPath && path.isAbsolute(serverPath)) {
        // Use custom server path
        return {
            command: pythonPath,
            args: [serverPath],
            transport: node_1.TransportKind.stdio
        };
    }
    else {
        // Try to find sv-lsp in the workspace or use bundled version
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (workspaceRoot) {
            // Look for sv-lsp in workspace
            const workspaceSvLsp = path.join(workspaceRoot, 'sv-lsp', 'src', 'sv_lsp', 'main.py');
            return {
                command: pythonPath,
                args: ['-m', 'sv_lsp.main'],
                transport: node_1.TransportKind.stdio,
                options: {
                    cwd: workspaceRoot,
                    env: {
                        ...process.env,
                        PYTHONPATH: path.join(workspaceRoot, 'sv-lsp', 'src')
                    }
                }
            };
        }
    }
    // Fallback: try to run sv-lsp as a module
    return {
        command: pythonPath,
        args: ['-m', 'sv_lsp.main'],
        transport: node_1.TransportKind.stdio
    };
}
//# sourceMappingURL=extension.js.map