{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,qDAAiH;AAEjH,IAAI,MAAsB,CAAC;AAE3B,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,oBAAoB;IACpB,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE1B,4BAA4B;IAC5B,mBAAmB,CAAC,OAAO,CAAC,CAAC;AACjC,CAAC;AARD,4BAQC;AAED,SAAgB,UAAU;IACtB,IAAI,CAAC,MAAM,EAAE;QACT,OAAO,SAAS,CAAC;KACpB;IACD,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;AACzB,CAAC;AALD,gCAKC;AAED,SAAS,gBAAgB,CAAC,OAAgC;IACtD,yBAAyB;IACzB,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;QAC7F,IAAI,MAAM,EAAE;YACR,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yCAAyC,CAAC,CAAC;SACnF;IACL,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC9F,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;SAC/B;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,mBAAmB,CAAC,OAAgC;IACzD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;IAEtE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;QAC9B,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,OAAO;KACV;IAED,iBAAiB;IACjB,MAAM,aAAa,GAAkB,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAE9D,iBAAiB;IACjB,MAAM,aAAa,GAA0B;QACzC,gBAAgB,EAAE;YACd,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE;YAC7C,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE;SACpD;QACD,WAAW,EAAE;YACT,UAAU,EAAE;gBACR,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,oBAAoB,CAAC;gBAC9D,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,2BAA2B,CAAC,CAAC,eAAe;aACxF;SACJ;QACD,iBAAiB,EAAE,mBAAmB;QACtC,kBAAkB,EAAE,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,yBAAyB,CAAC;QAChF,qBAAqB,EAAE,CAAC;QACxB,qBAAqB,EAAE;YACnB,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;YAC5C,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC;YAClC,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM;SACpE;KACJ,CAAC;IAEF,6BAA6B;IAC7B,MAAM,GAAG,IAAI,qBAAc,CACvB,mBAAmB,EACnB,+BAA+B,EAC/B,aAAa,EACb,aAAa,CAChB,CAAC;IAEF,mBAAmB;IACnB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;QACrB,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAElE,4BAA4B;QAC5B,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC7F,aAAa,CAAC,IAAI,GAAG,4BAA4B,CAAC;QAClD,aAAa,CAAC,OAAO,GAAG,0CAA0C,CAAC;QACnE,aAAa,CAAC,OAAO,GAAG,iCAAiC,CAAC;QAC1D,aAAa,CAAC,IAAI,EAAE,CAAC;QAErB,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QACvE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kDAAkD,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IACtG,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAqC;IAC3D,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAS,YAAY,CAAC,CAAC;IACpD,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAS,YAAY,EAAE,SAAS,CAAC,CAAC;IAE/D,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;QAC3C,yBAAyB;QACzB,OAAO;YACH,OAAO,EAAE,UAAU;YACnB,IAAI,EAAE,CAAC,UAAU,CAAC;YAClB,SAAS,EAAE,oBAAa,CAAC,KAAK;SACjC,CAAC;KACL;SAAM;QACH,6DAA6D;QAC7D,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAEzE,IAAI,aAAa,EAAE;YACf,+BAA+B;YAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;YAEtF,OAAO;gBACH,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC;gBAC3B,SAAS,EAAE,oBAAa,CAAC,KAAK;gBAC9B,OAAO,EAAE;oBACL,GAAG,EAAE,aAAa;oBAClB,GAAG,EAAE;wBACD,GAAG,OAAO,CAAC,GAAG;wBACd,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC;qBACxD;iBACJ;aACJ,CAAC;SACL;KACJ;IAED,0CAA0C;IAC1C,OAAO;QACH,OAAO,EAAE,UAAU;QACnB,IAAI,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC;QAC3B,SAAS,EAAE,oBAAa,CAAC,KAAK;KACjC,CAAC;AACN,CAAC"}