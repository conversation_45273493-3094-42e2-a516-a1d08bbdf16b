// SystemVerilog test file for VSCode extension
module test_cpu #(
    parameter int DATA_WIDTH = 32,
    parameter int ADDR_WIDTH = 16
) (
    input  logic                    clk,
    input  logic                    rst_n,
    input  logic [DATA_WIDTH-1:0]   data_in,
    output logic [DATA_WIDTH-1:0]   data_out,
    output logic                    valid_out
);

    // Internal signals
    logic [DATA_WIDTH-1:0] register_file [0:31];
    logic [ADDR_WIDTH-1:0] pc;
    logic [DATA_WIDTH-1:0] instruction;
    
    // State machine
    typedef enum logic [2:0] {
        IDLE    = 3'b000,
        FETCH   = 3'b001,
        DECODE  = 3'b010,
        EXECUTE = 3'b011,
        WRITEBACK = 3'b100
    } cpu_state_t;
    
    cpu_state_t current_state, next_state;
    
    // Function definition
    function automatic logic [DATA_WIDTH-1:0] alu_add(
        input logic [DATA_WIDTH-1:0] a,
        input logic [DATA_WIDTH-1:0] b
    );
        return a + b;
    endfunction
    
    // Task definition
    task automatic reset_cpu();
        pc <= 0;
        current_state <= IDLE;
        for (int i = 0; i < 32; i++) begin
            register_file[i] <= 0;
        end
    endtask
    
    // Sequential logic
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            reset_cpu();
        end else begin
            current_state <= next_state;
            case (current_state)
                FETCH: begin
                    instruction <= data_in;
                    pc <= pc + 1;
                end
                EXECUTE: begin
                    data_out <= alu_add(register_file[0], register_file[1]);
                end
                default: begin
                    // Default case
                end
            endcase
        end
    end
    
    // Combinational logic
    always_comb begin
        next_state = current_state;
        valid_out = 1'b0;
        
        case (current_state)
            IDLE: begin
                next_state = FETCH;
            end
            FETCH: begin
                next_state = DECODE;
            end
            DECODE: begin
                next_state = EXECUTE;
            end
            EXECUTE: begin
                next_state = WRITEBACK;
            end
            WRITEBACK: begin
                next_state = IDLE;
                valid_out = 1'b1;
            end
        endcase
    end
    
    // Assertions
    property valid_pc;
        @(posedge clk) disable iff (!rst_n)
        pc < 2**ADDR_WIDTH;
    endproperty
    
    assert property (valid_pc) else $error("PC out of range");
    
endmodule

// Interface definition
interface cpu_bus_if #(
    parameter int DATA_WIDTH = 32,
    parameter int ADDR_WIDTH = 16
) (
    input logic clk,
    input logic rst_n
);
    
    logic [ADDR_WIDTH-1:0] addr;
    logic [DATA_WIDTH-1:0] data;
    logic                  valid;
    logic                  ready;
    
    modport master (
        output addr, data, valid,
        input  ready
    );
    
    modport slave (
        input  addr, data, valid,
        output ready
    );
    
endinterface

// Package definition
package cpu_pkg;
    
    typedef struct packed {
        logic [7:0]  opcode;
        logic [4:0]  rs1;
        logic [4:0]  rs2;
        logic [4:0]  rd;
        logic [11:0] immediate;
    } instruction_t;
    
    typedef enum logic [3:0] {
        OP_ADD  = 4'h0,
        OP_SUB  = 4'h1,
        OP_AND  = 4'h2,
        OP_OR   = 4'h3,
        OP_XOR  = 4'h4,
        OP_LOAD = 4'h8,
        OP_STORE = 4'h9
    } opcode_t;
    
    parameter int REGISTER_COUNT = 32;
    parameter int INSTRUCTION_WIDTH = 32;
    
endpackage
