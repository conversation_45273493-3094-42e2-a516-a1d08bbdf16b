{"comments": {"lineComment": "//", "blockComment": ["/*", "*/"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"], ["begin", "end"], ["case", "endcase"], ["function", "endfunction"], ["task", "endtask"], ["module", "endmodule"], ["interface", "endinterface"], ["package", "endpackage"], ["class", "endclass"], ["generate", "endgenerate"], ["property", "endproperty"], ["sequence", "endsequence"], ["clocking", "endclocking"], ["covergroup", "endcovergroup"]], "autoClosingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"], ["`", "`"]], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"], ["`", "`"]], "folding": {"markers": {"start": "^\\s*//\\s*#?region\\b", "end": "^\\s*//\\s*#?endregion\\b"}}, "wordPattern": "(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)", "indentationRules": {"increaseIndentPattern": "^\\s*(begin|case|function|task|module|interface|package|class|generate|property|sequence|clocking|covergroup|if|else|for|while|repeat|forever|fork)\\b.*$", "decreaseIndentPattern": "^\\s*(end|endcase|endfunction|endtask|endmodule|endinterface|endpackage|endclass|endgenerate|endproperty|endsequence|endclocking|endcovergroup|join|join_any|join_none)\\b.*$"}}