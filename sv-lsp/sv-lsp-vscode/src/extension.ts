import * as vscode from 'vscode';
import * as path from 'path';
import { LanguageClient, LanguageClientOptions, ServerOptions, TransportKind } from 'vscode-languageclient/node';
import { SystemVerilogConfigurationProvider } from './configurationProvider';
import { StatusBarManager } from './statusBarManager';

let client: LanguageClient;

export function activate(context: vscode.ExtensionContext) {
    console.log('SystemVerilog LSP extension is now active!');

    // Register commands
    registerCommands(context);

    // Start the language server
    startLanguageServer(context);
}

export function deactivate(): Thenable<void> | undefined {
    if (!client) {
        return undefined;
    }
    return client.stop();
}

function registerCommands(context: vscode.ExtensionContext) {
    // Restart server command
    const restartCommand = vscode.commands.registerCommand('systemverilog.restartServer', async () => {
        if (client) {
            await client.stop();
            startLanguageServer(context);
            vscode.window.showInformationMessage('SystemVerilog Language Server restarted');
        }
    });

    // Show output channel command
    const showOutputCommand = vscode.commands.registerCommand('systemverilog.showOutputChannel', () => {
        if (client) {
            client.outputChannel.show();
        }
    });

    context.subscriptions.push(restartCommand, showOutputCommand);
}

function startLanguageServer(context: vscode.ExtensionContext) {
    const config = vscode.workspace.getConfiguration('systemverilog.lsp');

    if (!config.get('enabled', true)) {
        console.log('SystemVerilog LSP is disabled');
        return;
    }

    // Server options
    const serverOptions: ServerOptions = getServerOptions(config);

    // Client options
    const clientOptions: LanguageClientOptions = {
        documentSelector: [
            { scheme: 'file', language: 'systemverilog' },
            { scheme: 'file', pattern: '**/*.{sv,svh,v,vh}' }
        ],
        synchronize: {
            fileEvents: [
                vscode.workspace.createFileSystemWatcher('**/*.{sv,svh,v,vh}'),
                vscode.workspace.createFileSystemWatcher('**/*.{json,toml,yaml,yml}') // Config files
            ]
        },
        outputChannelName: 'SystemVerilog LSP',
        traceOutputChannel: vscode.window.createOutputChannel('SystemVerilog LSP Trace'),
        revealOutputChannelOn: 4, // Never
        initializationOptions: {
            includePaths: config.get('includePaths', []),
            defines: config.get('defines', {}),
            workspaceRoot: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
        }
    };

    // Create the language client
    client = new LanguageClient(
        'systemverilog-lsp',
        'SystemVerilog Language Server',
        serverOptions,
        clientOptions
    );

    // Start the client
    client.start().then(() => {
        console.log('SystemVerilog Language Server started successfully');

        // Show status in status bar
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
        statusBarItem.text = "$(check) SystemVerilog LSP";
        statusBarItem.tooltip = "SystemVerilog Language Server is running";
        statusBarItem.command = 'systemverilog.showOutputChannel';
        statusBarItem.show();

        context.subscriptions.push(statusBarItem);
    }).catch((error) => {
        console.error('Failed to start SystemVerilog Language Server:', error);
        vscode.window.showErrorMessage(`Failed to start SystemVerilog Language Server: ${error.message}`);
    });

    context.subscriptions.push(client);
}

function getServerOptions(config: vscode.WorkspaceConfiguration): ServerOptions {
    const serverPath = config.get<string>('serverPath');
    const pythonPath = config.get<string>('pythonPath', 'python3');

    if (serverPath && path.isAbsolute(serverPath)) {
        // Use custom server path
        return {
            command: pythonPath,
            args: [serverPath],
            transport: TransportKind.stdio
        };
    } else {
        // Try to find sv-lsp in the workspace or use bundled version
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;

        if (workspaceRoot) {
            // Look for sv-lsp in workspace
            const workspaceSvLsp = path.join(workspaceRoot, 'sv-lsp', 'src', 'sv_lsp', 'main.py');

            return {
                command: pythonPath,
                args: ['-m', 'sv_lsp.main'],
                transport: TransportKind.stdio,
                options: {
                    cwd: workspaceRoot,
                    env: {
                        ...process.env,
                        PYTHONPATH: path.join(workspaceRoot, 'sv-lsp', 'src')
                    }
                }
            };
        }
    }

    // Fallback: try to run sv-lsp as a module
    return {
        command: pythonPath,
        args: ['-m', 'sv_lsp.main'],
        transport: TransportKind.stdio
    };
}
