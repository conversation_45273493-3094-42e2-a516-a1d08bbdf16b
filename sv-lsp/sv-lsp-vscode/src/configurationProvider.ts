import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

export class SystemVerilogConfigurationProvider {
    private static readonly CONFIG_SECTION = 'systemverilog.lsp';
    
    /**
     * Get the current configuration for SystemVerilog LSP
     */
    public static getConfiguration(): vscode.WorkspaceConfiguration {
        return vscode.workspace.getConfiguration(this.CONFIG_SECTION);
    }
    
    /**
     * Check if LSP is enabled
     */
    public static isLspEnabled(): boolean {
        return this.getConfiguration().get('enabled', true);
    }
    
    /**
     * Get the Python path for running the LSP server
     */
    public static getPythonPath(): string {
        return this.getConfiguration().get('pythonPath', 'python3');
    }
    
    /**
     * Get custom server path if specified
     */
    public static getServerPath(): string | undefined {
        const serverPath = this.getConfiguration().get<string>('serverPath');
        return serverPath && serverPath.trim() !== '' ? serverPath : undefined;
    }
    
    /**
     * Get include paths for SystemVerilog files
     */
    public static getIncludePaths(): string[] {
        return this.getConfiguration().get('includePaths', []);
    }
    
    /**
     * Get preprocessor defines
     */
    public static getDefines(): Record<string, string> {
        return this.getConfiguration().get('defines', {});
    }
    
    /**
     * Get trace level for server communication
     */
    public static getTraceLevel(): string {
        return this.getConfiguration().get('trace.server', 'off');
    }
    
    /**
     * Auto-detect sv-lsp server in workspace
     */
    public static findServerInWorkspace(): string | undefined {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            return undefined;
        }
        
        // Common locations for sv-lsp
        const possiblePaths = [
            path.join(workspaceRoot, 'sv-lsp', 'src', 'sv_lsp', 'main.py'),
            path.join(workspaceRoot, 'sv-lsp', 'main.py'),
            path.join(workspaceRoot, 'tools', 'sv-lsp', 'main.py'),
            path.join(workspaceRoot, 'scripts', 'sv-lsp', 'main.py')
        ];
        
        for (const serverPath of possiblePaths) {
            if (fs.existsSync(serverPath)) {
                return serverPath;
            }
        }
        
        return undefined;
    }
    
    /**
     * Validate configuration and show warnings if needed
     */
    public static async validateConfiguration(): Promise<boolean> {
        const config = this.getConfiguration();
        const issues: string[] = [];
        
        // Check Python path
        const pythonPath = this.getPythonPath();
        if (!await this.checkPythonAvailable(pythonPath)) {
            issues.push(`Python interpreter not found: ${pythonPath}`);
        }
        
        // Check server path if specified
        const serverPath = this.getServerPath();
        if (serverPath && !fs.existsSync(serverPath)) {
            issues.push(`Server path not found: ${serverPath}`);
        }
        
        // Check include paths
        const includePaths = this.getIncludePaths();
        for (const includePath of includePaths) {
            if (!fs.existsSync(includePath)) {
                issues.push(`Include path not found: ${includePath}`);
            }
        }
        
        if (issues.length > 0) {
            const message = `SystemVerilog LSP configuration issues:\n${issues.join('\n')}`;
            const action = await vscode.window.showWarningMessage(
                message,
                'Open Settings',
                'Ignore'
            );
            
            if (action === 'Open Settings') {
                vscode.commands.executeCommand('workbench.action.openSettings', this.CONFIG_SECTION);
            }
            
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if Python is available
     */
    private static async checkPythonAvailable(pythonPath: string): Promise<boolean> {
        try {
            const { exec } = require('child_process');
            return new Promise((resolve) => {
                exec(`${pythonPath} --version`, (error: any) => {
                    resolve(!error);
                });
            });
        } catch {
            return false;
        }
    }
    
    /**
     * Show configuration quick pick
     */
    public static async showConfigurationQuickPick(): Promise<void> {
        const items: vscode.QuickPickItem[] = [
            {
                label: '$(gear) Open Settings',
                description: 'Open SystemVerilog LSP settings',
                detail: 'Configure server path, include paths, and other options'
            },
            {
                label: '$(search) Auto-detect Server',
                description: 'Search for sv-lsp server in workspace',
                detail: 'Automatically find and configure the server path'
            },
            {
                label: '$(file-directory) Set Include Paths',
                description: 'Configure include directories',
                detail: 'Add directories to search for SystemVerilog files'
            },
            {
                label: '$(symbol-parameter) Set Defines',
                description: 'Configure preprocessor defines',
                detail: 'Set macros and defines for compilation'
            },
            {
                label: '$(debug) Toggle Debug Mode',
                description: 'Enable/disable debug tracing',
                detail: 'Control server communication logging'
            }
        ];
        
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select configuration option'
        });
        
        if (!selected) {
            return;
        }
        
        switch (selected.label) {
            case '$(gear) Open Settings':
                vscode.commands.executeCommand('workbench.action.openSettings', this.CONFIG_SECTION);
                break;
                
            case '$(search) Auto-detect Server':
                await this.autoDetectServer();
                break;
                
            case '$(file-directory) Set Include Paths':
                await this.configureIncludePaths();
                break;
                
            case '$(symbol-parameter) Set Defines':
                await this.configureDefines();
                break;
                
            case '$(debug) Toggle Debug Mode':
                await this.toggleDebugMode();
                break;
        }
    }
    
    /**
     * Auto-detect and configure server path
     */
    private static async autoDetectServer(): Promise<void> {
        const serverPath = this.findServerInWorkspace();
        
        if (serverPath) {
            const config = this.getConfiguration();
            await config.update('serverPath', serverPath, vscode.ConfigurationTarget.Workspace);
            vscode.window.showInformationMessage(`Server found and configured: ${serverPath}`);
        } else {
            vscode.window.showWarningMessage('No sv-lsp server found in workspace');
        }
    }
    
    /**
     * Configure include paths
     */
    private static async configureIncludePaths(): Promise<void> {
        const currentPaths = this.getIncludePaths();
        const pathsString = currentPaths.join(';');
        
        const input = await vscode.window.showInputBox({
            prompt: 'Enter include paths (separated by semicolons)',
            value: pathsString,
            placeHolder: '/path/to/includes;/another/path'
        });
        
        if (input !== undefined) {
            const newPaths = input.split(';').map(p => p.trim()).filter(p => p.length > 0);
            const config = this.getConfiguration();
            await config.update('includePaths', newPaths, vscode.ConfigurationTarget.Workspace);
            vscode.window.showInformationMessage(`Include paths updated: ${newPaths.length} paths`);
        }
    }
    
    /**
     * Configure preprocessor defines
     */
    private static async configureDefines(): Promise<void> {
        const currentDefines = this.getDefines();
        const definesString = Object.entries(currentDefines)
            .map(([key, value]) => `${key}=${value}`)
            .join(';');
        
        const input = await vscode.window.showInputBox({
            prompt: 'Enter defines (format: NAME=value;NAME2=value2)',
            value: definesString,
            placeHolder: 'DEBUG=1;WIDTH=32'
        });
        
        if (input !== undefined) {
            const newDefines: Record<string, string> = {};
            
            if (input.trim()) {
                const pairs = input.split(';');
                for (const pair of pairs) {
                    const [key, value] = pair.split('=').map(s => s.trim());
                    if (key) {
                        newDefines[key] = value || '1';
                    }
                }
            }
            
            const config = this.getConfiguration();
            await config.update('defines', newDefines, vscode.ConfigurationTarget.Workspace);
            vscode.window.showInformationMessage(`Defines updated: ${Object.keys(newDefines).length} defines`);
        }
    }
    
    /**
     * Toggle debug mode
     */
    private static async toggleDebugMode(): Promise<void> {
        const config = this.getConfiguration();
        const currentLevel = this.getTraceLevel();
        const newLevel = currentLevel === 'off' ? 'verbose' : 'off';
        
        await config.update('trace.server', newLevel, vscode.ConfigurationTarget.Workspace);
        vscode.window.showInformationMessage(`Debug mode ${newLevel === 'off' ? 'disabled' : 'enabled'}`);
    }
}
