import pyslang

def find_node_at_offset(node, offset):
    """
    Recursively finds the syntax node at a given offset.
    """
    for i in range(len(node)):
        child = node[i]
        if hasattr(child, 'sourceRange'):
            if child.sourceRange.start.offset <= offset and \
               child.sourceRange.end.offset >= offset:
                # This is a token, not a node, so we can't recurse
                if not hasattr(child, '__len__'):
                    return child
                return find_node_at_offset(child, offset)
    return node

# Create a simple SystemVerilog file
source = """
module m;
    int i;
    always @* i = 1;
endmodule
"""

# Create a syntax tree
tree = pyslang.SyntaxTree.fromText(source)

# Create a compilation
compilation = pyslang.Compilation()
compilation.addSyntaxTree(tree)

# Find the offset of the usage of 'i'
offset = source.find("i = 1")

# Find the syntax node at the offset
node = find_node_at_offset(tree.root, offset)

# Get the scope
scope = compilation.getRoot()

# Find the symbol in the root scope
symbol = scope.find(node.identifier.valueText)

if not symbol:
    # If the symbol is not in the root scope, try to find it in the module scope
    module_symbol = scope.find("m")
    if module_symbol:
        symbol = module_symbol.body.find(node.identifier.valueText)


# Get the definition location
if symbol:
    print(f"Found symbol: {symbol.name}")
    print(f"Definition location: {symbol.location}")
else:
    print("Could not find symbol.")