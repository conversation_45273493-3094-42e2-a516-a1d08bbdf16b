#
# lcovrc - settings for LCOV
#

# Specify if genin<PERSON> should try to automatically determine the base-directory
# when collecting coverage data.
geninfo_auto_base = 1

# Use gcov intermediate format? Valid values are 0, 1, auto
geninfo_intermediate = auto

# Specify if exception branches should be excluded from branch coverage.
no_exception_branch = 1

# Location of the insmod tool
lcov_insmod_tool = /sbin/insmod

# Location of the modprobe tool
lcov_modprobe_tool = /sbin/modprobe

# Location of the rmmod tool
lcov_rmmod_tool = /sbin/rmmod

# Location for temporary directories
lcov_tmp_dir = /tmp

# Show full paths during list operation if non-zero (same as --list-full-path
# option of lcov)
lcov_list_full_path = 0

# Specify the maximum width for list output. This value is ignored when
# lcov_list_full_path is non-zero.
lcov_list_width = 80

# Specify the maximum percentage of file names which may be truncated when
# choosing a directory prefix in list output. This value is ignored when
# lcov_list_full_path is non-zero.
lcov_list_truncate_max = 20

# Specify if function coverage data should be collected and processed.
function_coverage = 1

# Specify if branch coverage data should be collected and processed.
branch_coverage = 0

geninfo_unexecuted_blocks = 1

# Add project-specific exclusions for the SLANG_UNREACHABLE macro
# since that branch is by design never reachable in real code,
# and for a 'default' case block that is empty since it's very
# irritating to see missed coverage on that line -- either the remaining
# block is also missed or it's an empty case and we don't care.
omit_lines = SLANG_UNREACHABLE
omit_lines = default:$
