/** @page language-support Language Support
@brief Overview of supported language features

@tableofcontents

This is an overview of which SystemVerilog features slang supports, and those which
it yet does not. This is not a promise that features marked "supported" are bug-free,
but should imply that any such bugs are as of yet unknown and should be reported if discovered.

Features are expressed via references to chapters and sections within the
IEEE 1800-2023 SystemVerilog Language Reference Manual (LRM).

@section elaboration Elaboration

## Chapters 1-4

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-dim}} 1 | Overview | N/A
{{m-dim}} 2 | Normative references | N/A
{{m-dim}} 3 | Design and verification building blocks | N/A
{{m-dim}} 4 | Scheduling semantics | N/A

## 5. Lexical conventions

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 5.2 | Lexical tokens | v0.2
{{m-success}} 5.3 | White space | v0.2
{{m-success}} 5.4 | Comments | v0.2
{{m-success}} 5.5 | Operators | v0.2
{{m-success}} 5.6 | Identifiers and keywords | v0.2
{{m-success}} 5.7 | Numbers | v0.2
{{m-success}} 5.8 | Time literals | v0.3
{{m-success}} 5.9 | String literals | v0.2
{{m-success}} 5.10 | Structure literals | v0.2
{{m-success}} 5.11 | Array literals | v0.2
{{m-success}} 5.12 | Attributes | v0.2
{{m-success}} 5.13 | Built-in methods | v0.2

## 6. Data types

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 6.2 | Data types | v0.2
{{m-success}} 6.3 | Value set | v0.2
{{m-success}} 6.4 | Singular and aggregate types | v0.2
{{m-success}} 6.5 | Nets and variables | v0.2
{{m-success}} 6.6 | Net types | v0.2
{{m-success}} 6.6.7 | User-defined nettypes | v1.0
{{m-success}} 6.6.8 | Generic interconnect | v1.0
{{m-success}} 6.7 | Net declarations | v0.6
{{m-success}} 6.8 | Variable declarations | v0.2
{{m-success}} 6.9 | Vector declarations | v0.2
{{m-success}} 6.10 | Implicit declarations | v0.3
{{m-success}} 6.11 | Integer types | v0.2
{{m-success}} 6.12 | Real types | v0.2
{{m-success}} 6.13 | Void type | v0.2
{{m-success}} 6.14 | Chandle types | v0.5
{{m-success}} 6.15 | Classes | v0.5
{{m-success}} 6.16 | Strings | v0.2
{{m-success}} 6.17 | Events | v0.5
{{m-success}} 6.18 | User-defined types | v0.2
{{m-success}} 6.19 | Enumerations | v0.2
{{m-success}} 6.20.2 | Value parameters | v0.2
{{m-success}} 6.20.3 | Type parameters | v0.2
{{m-success}} 6.20.4 | Local parameters | v0.2
{{m-success}} 6.20.5 | Specify parameters | v0.7
{{m-success}} 6.20.6 | Const constants | v0.3
{{m-success}} 6.20.7 | $ as a constant | v0.8
{{m-success}} 6.21 | Scope and lifetime | v0.3
{{m-success}} 6.22 | Type compatibility | v0.2
{{m-success}} 6.23 | Type operator | v0.7
{{m-success}} 6.24.1 | Static casting | v0.2
{{m-success}} 6.24.2 | Dynamic casting | v0.5
{{m-success}} 6.24.3 | Bit-stream casting | v0.5
{{m-success}} 6.25 | Parameterized data types | v0.5

## 7. Aggregate data types

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 7.2 | Structures | v0.2
{{m-success}} 7.3 | Unions | v0.9
{{m-success}} 7.4 | Packed and unpacked arrays | v0.2
{{m-success}} 7.5 | Dynamic arrays | v0.4
{{m-success}} 7.6 | Array assignments | v0.2
{{m-success}} 7.7 | Arrays as arguments to subroutines | v0.2
{{m-success}} 7.8 | Associative arrays | v0.4
{{m-success}} 7.9 | Associative array methods | v0.6
{{m-success}} 7.10 | Queues | v0.7
{{m-success}} 7.11 | Array querying functions | v0.6
{{m-success}} 7.12 | Array manipulation methods | v0.6
{{m-success}} 7.12.5 | Array mapping method | v6.0

## 8. Classes

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 8.3 | Syntax | v0.2
{{m-success}} 8.4 | Objects | v0.5
{{m-success}} 8.5 | Object properties | v0.5
{{m-success}} 8.6 | Object methods | v0.5
{{m-success}} 8.7 | Constructors | v0.5
{{m-success}} 8.8 | Typed constructor calls | v0.5
{{m-success}} 8.9 | Static class properties | v0.5
{{m-success}} 8.10 | Static methods | v0.5
{{m-success}} 8.11 | This | v0.5
{{m-success}} 8.12 | Assignment, renaming, and copying | v0.5
{{m-success}} 8.13 | Inheritance and subclasses | v0.5
{{m-success}} 8.14 | Overriden members | v0.5
{{m-success}} 8.15 | Super | v0.5
{{m-success}} 8.16 | Casting | v0.5
{{m-success}} 8.17 | Chaining constructors | v0.5
{{m-success}} 8.18 | Data hiding and encapsulation | v0.5
{{m-success}} 8.19 | Constant class properties | v0.5
{{m-success}} 8.20 | Virtual methods | v0.5
{{m-success}} 8.21 | Abstract classes and pure virtual methods | v0.5
{{m-success}} 8.22 | Polymorphism: dynamic method lookup | v0.5
{{m-success}} 8.23 | Class scope resolution operator | v0.5
{{m-success}} 8.24 | Out-of-block declarations | v0.5
{{m-success}} 8.25 | Parameterized classes | v0.5
{{m-success}} 8.26 | Interface classes | v0.5
{{m-success}} 8.27 | Typedef class | v0.5
{{m-dim}}     8.28 | Classes and structures | N/A
{{m-dim}}     8.29 | Memory management | N/A
{{m-success}} 8.30 | Weak references | v6.0

## 9. Processes

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 9.2.1 | Initial procedures | v0.2
{{m-success}} 9.2.2 | Always procedures | v0.2
{{m-success}} 9.2.3 | Final procedures | v0.2
{{m-success}} 9.3.1 | Sequential blocks | v0.2
{{m-success}} 9.3.2 | Parallel blocks | v0.2
{{m-success}} 9.3.3 | Statement block start and finish times | v0.2
{{m-success}} 9.3.4 | Block names | v0.2
{{m-success}} 9.3.5 | Statement labels | v0.2
{{m-success}} 9.4.1 | Delay control | v0.2
{{m-success}} 9.4.2 | Event control | v0.2
{{m-success}} 9.4.2.3 | Conditional event controls | v0.6
{{m-success}} 9.4.2.4 | Sequence events | v0.8
{{m-success}} 9.4.3 | Level-sensitive event control | v0.6
{{m-success}} 9.4.4 | Level-sensitive sequence controls | v0.8
{{m-success}} 9.4.5 | Intra-assignment timing controls | v0.6
{{m-dim}}     9.5 | Process execution threads | N/A
{{m-success}} 9.6.1 | Wait fork statement | v0.4
{{m-success}} 9.6.2 | Disable statement | v0.4
{{m-success}} 9.6.3 | Disable fork statement | v0.4
{{m-success}} 9.7 | Fine-grain process control | v0.6

## 10. Assignment statements

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 10.3 | Continuous assignments | v0.7
{{m-success}} 10.4 | Procedural assignments | v0.2
{{m-success}} 10.5 | Variable declarations assignment | v0.2
{{m-success}} 10.6 | Procedural continuous assignments | v0.7
{{m-success}} 10.7 | Assignment extension and truncation | v0.2
{{m-success}} 10.8 | Assignment-like contexts | v0.2
{{m-success}} 10.9 | Assignment patterns | v5.0
{{m-success}} 10.10 | Unpacked array concatenation | v0.4
{{m-success}} 10.11 | Net aliasing | v4.0

## 11. Operators and expressions

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 11.3 | Operators | v0.2
{{m-success}} 11.4 | Operator descriptions | v0.2
{{m-success}} 11.4.14 | Streaming operators | v0.5
{{m-success}} 11.5 | Operands | v0.2
{{m-success}} 11.6 | Expression bit lengths | v0.2
{{m-success}} 11.7 | Signed expressions | v0.2
{{m-success}} 11.8 | Expression evaluation rules | v0.2
{{m-success}} 11.9 | Tagged union expressions | v0.9
{{m-success}} 11.10 | String literal expressions | v0.5
{{m-success}} 11.11 | Min/typ/max expressions | v0.4
{{m-success}} 11.12 | Let construct | v0.9

## 12. Procedural programming statements

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 12.4 | Conditional if-else statement | v0.2
{{m-success}} 12.5 | Case statement | v0.2
{{m-success}} 12.6 | Pattern matching conditional statements | v2.0
{{m-success}} 12.7.1 | For-loop statements | v0.2
{{m-success}} 12.7.2 | Repeat-loop statements | v0.2
{{m-success}} 12.7.3 | Foreach-loop statements | v0.2
{{m-success}} 12.7.4 | While-loop statements | v0.2
{{m-success}} 12.7.5 | Do...while-loop statements | v0.2
{{m-success}} 12.7.6 | Forever-loop statements | v0.2
{{m-success}} 12.8 | Jump statements | v0.2

## 13. Tasks and functions

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 13.3 | Tasks | v0.2
{{m-success}} 13.3.2 | Task memory usage and concurrent activation | v0.7
{{m-success}} 13.4 | Functions | v0.2
{{m-success}} 13.4.4 | Background processes spawned by function calls | v0.7
{{m-success}} 13.5 | Subroutine calls and argument passing | v0.7
{{m-success}} 13.6 | Import and export functions | v0.6
{{m-success}} 13.7 | Task and function names | v0.2
{{m-dim}}     13.8 | Parameterized tasks and functions | N/A

## 14. Clocking blocks

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 14.3 | Clocking block declaration | v0.8
{{m-success}} 14.4 | Input and output skews | v0.8
{{m-success}} 14.5 | Hierarchical expressions | v0.8
{{m-success}} 14.6 | Signals in multiple clocking blocks | v0.8
{{m-success}} 14.7 | Clocking block scope and lifetime | v0.8
{{m-success}} 14.8 | Multiple clocking blocks example | v0.8
{{m-success}} 14.9 | Interfaces and clocking blocks | v0.8
{{m-success}} 14.10 | Clocking block events | v0.8
{{m-success}} 14.11 | Cycle delay | v0.8
{{m-success}} 14.12 | Default clocking | v0.8
{{m-dim}}     14.13 | Input sampling | N/A
{{m-success}} 14.14 | Global clocking | v0.8
{{m-success}} 14.15 | Synchronous events | v0.8
{{m-success}} 14.16 | Synchronous drives | v0.8

## 15. Interprocess synchronization and communication

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 15.3 | Semaphores | v0.6
{{m-success}} 15.4 | Mailboxes | v1.0
{{m-success}} 15.5 | Named events | v0.5

## 16. Assertions

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 16.3 | Immediate assertions | v0.2
{{m-success}} 16.4 | Deferred assertions | v0.8
{{m-success}} 16.5 | Concurrent assertions overview | v0.8
{{m-success}} 16.6 | Boolean expressions | v0.8
{{m-success}} 16.7 | Sequences | v0.8
{{m-success}} 16.8 | Declaring sequences | v0.8
{{m-success}} 16.9 | Sequence operations | v0.8
{{m-warning}} 16.10 | Local variables | partial
{{m-success}} 16.11 | Calling subroutines on match of sequence | v0.8
{{m-success}} 16.12 | Declaring properties | v0.8
{{m-success}} 16.12.22 | Nondegeneracy | v7.0
{{m-success}} 16.13 | Multiclock support | v9.0
{{m-success}} 16.14 | Concurrent assertions | v0.8
{{m-success}} 16.14.7 | Inferred value functions | v9.0
{{m-success}} 16.15 | Disable iff resolution | v0.8
{{m-success}} 16.16 | Clock resolution | v9.0
{{m-success}} 16.17 | Expect statement | v0.8
{{m-dim}}     16.18 | Clocking blocks and concurrent assertions | N/A

## 17. Checkers

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 17.2 | Checker declaration | v4.0
{{m-success}} 17.3 | Checker instantiation | v4.0
{{m-success}} 17.4 | Context inference | v4.0
{{m-success}} 17.5 | Checker procedures | v4.0
{{m-success}} 17.6 | Covergroups in checkers | v4.0
{{m-success}} 17.7 | Checker variables | v4.0
{{m-success}} 17.8 | Functions in checkers | v4.0
{{m-success}} 17.9 | Complex checker example | v4.0

## 18. Constrained random value generation

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 18.3 | Concepts and usage | v0.6
{{m-success}} 18.4 | Random variables | v0.6
{{m-success}} 18.5 | Constraint blocks | v0.6
{{m-success}} 18.6 | Randomization methods | v0.6
{{m-success}} 18.7 | In-line constraints | v0.6
{{m-success}} 18.8 | Disabling random variables | v0.6
{{m-success}} 18.9 | Controlling constraints | v0.6
{{m-dim}}     18.10 | Dynamic constraint modification | N/A
{{m-success}} 18.11 | In-line random variable control | v0.6
{{m-success}} 18.12 | Randomization of scope variables | v0.6
{{m-success}} 18.13 | Random number system functions | v0.6
{{m-dim}}     18.14 | Random stability | N/A
{{m-dim}}     18.15 | Manually seeding randomize | N/A
{{m-success}} 18.16 | Random weighted case | v0.9
{{m-success}} 18.17 | Random sequence generation | v0.9

## 19. Functional coverage

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 19.3 | Defining the coverage model | v1.0
{{m-success}} 19.4 | Using covergroup in classes | v1.0
{{m-success}} 19.5 | Defining coverage points | v1.0
{{m-success}} 19.6 | Defining cross coverage | v1.0
{{m-success}} 19.7 | Specifying coverage options | v1.0
{{m-success}} 19.8 | Predefined coverage methods | v1.0
{{m-success}} 19.9 | Predefined coverage system tasks | v0.9
{{m-success}} 19.10 | Organization of option members | v1.0
{{m-dim}}     19.11 | Coverage computation | N/A

## 20. Utility system tasks and system functions

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 20.2 | Simulation control tasks | v0.2
{{m-success}} 20.3 | Simulation time functions | v0.2
{{m-success}} 20.4 | Timescale tasks | v0.4
{{m-success}} 20.5 | Conversion functions | v0.2
{{m-success}} 20.6 | Data query functions | v0.3
{{m-success}} 20.6.1 | Range system function | v0.8
{{m-success}} 20.7 | Array query functions | v0.4
{{m-success}} 20.8 | Math functions | v0.2
{{m-success}} 20.9 | Bit vector system functions | v0.2
{{m-success}} 20.10 | Severity tasks | v0.2
{{m-success}} 20.10.1 | Elaboration tasks | v0.5
{{m-success}} 20.11 | Assertion control tasks | v0.6
{{m-success}} 20.12 | Sampled value system functions | v0.8
{{m-success}} 20.13 | Coverage control functions | v0.9
{{m-success}} 20.14 | Probabilistic distribution functions | v0.7
{{m-success}} 20.15 | Stochastic analysis tasks and functions | v0.7
{{m-success}} 20.16 | PLA modeling tasks | v0.9
{{m-success}} 20.17 | Miscellaneous tasks and functions | v0.4

## 21. Input/output system tasks and system functions

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 21.2 | Display system tasks | v0.2
{{m-success}} 21.3 | File I/O tasks and functions | v0.4
{{m-success}} 21.4 | Loading memory array data from file | v0.2
{{m-success}} 21.5 | Writing memory array data to file | v0.2
{{m-success}} 21.6 | Command line input | v0.2
{{m-success}} 21.7 | Value change dump files | v0.4

## 22. Compiler directives

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 22.3 | \`resetall | v0.2
{{m-success}} 22.4 | \`include | v0.2
{{m-success}} 22.5 | \`define, \`undef, and \`undefineall | v0.2
{{m-success}} 22.6 | \`ifdef, \`else, \`elsif, \`endif, \`ifndef | v0.2
{{m-success}} 22.7 | \`timescale | v0.2
{{m-success}} 22.8 | \`default_nettype | v0.2
{{m-success}} 22.9 | \`unconnected_drive and \`nounconnected_drive | v0.3
{{m-success}} 22.10 | \`celldefine and \`endcelldefine | v0.2
{{m-success}} 22.11 | \`pragma | v0.3
{{m-success}} 22.12 | \`line | v0.2
{{m-success}} 22.13 | \`__FILE__ and \`__LINE__ | v0.2
{{m-success}} 22.14 | \`begin_keywords, \`end_keywords | v0.2

## 23. Modules and hierarchy

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 23.2.1 | Module definitions | v0.2
{{m-success}} 23.2.2 | Port declarations | v1.0
{{m-success}} 23.2.3 | Parameterized modules | v0.2
{{m-success}} 23.2.4 | Module contents | v0.2
{{m-success}} 23.3.1 | Top-level modules and $root | v0.2
{{m-success}} 23.3.2 | Module instantiation syntax | v0.2
{{m-success}} 23.3.3 | Port connection rules | v1.0
{{m-success}} 23.4 | Nested modules | v1.0
{{m-success}} 23.5 | Extern modules | v4.0
{{m-success}} 23.6 | Hierarchical names | v0.2
{{m-success}} 23.7 | Member selects and hierarchical names | v0.2
{{m-success}} 23.8 | Upwards name referencing | v0.7
{{m-success}} 23.9 | Scope rules | v0.2
{{m-success}} 23.10 | Overriding module parameters | v0.2
{{m-success}} 23.10.1 | defparam statement | v3.0
{{m-success}} 23.11 | Binding auxiliary code to scopes or instances | v3.0

## 24. Programs

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 24.3 | The program construct | v3.0
{{m-dim}}     24.4 | Eliminating testbench races | N/A
{{m-success}} 24.5 | Blocking tasks in cycle/event mode | v3.0
{{m-success}} 24.6 | Programwide space and anonymous programs | v3.0
{{m-success}} 24.7 | Program control tasks | v0.2

## 25. Interfaces

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 25.3 | Interface syntax | v0.2
{{m-success}} 25.4 | Ports in interfaces | v0.2
{{m-success}} 25.5 | Modports | v0.6
{{m-success}} 25.5.4 | Modport expressions | v2.0
{{m-success}} 25.5.5 | Clocking blocks and modports | v2.0
{{m-success}} 25.6 | Interfaces and specify blocks | v3.0
{{m-success}} 25.7 | Tasks and functions in interfaces | v2.0
{{m-success}} 25.8 | Parameterized interfaces | v0.2
{{m-success}} 25.9 | Virtual interfaces | v8.0
{{m-success}} 25.10 | Access to interface objects | v0.6

## 26. Packages

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 26.2 | Package declarations | v0.2
{{m-success}} 26.3 | Referencing data in packages | v0.2
{{m-success}} 26.4 | Using packages in module headers | v0.2
{{m-success}} 26.5 | Search order rules | v0.2
{{m-success}} 26.6 | Exporting imported names from packages | v0.9
{{m-success}} 26.7 | The std built-in package | v0.6

## 27. Generate constructs

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 27.3 | Generate construct syntax | v0.2
{{m-success}} 27.4 | Loop generate constructs | v0.2
{{m-success}} 27.5 | Conditional generate constructs | v0.2
{{m-success}} 27.6 | External names for unnamed generate blocks | v0.4

## 28. Gate-level and switch-level modeling

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 28.3 | Gate and switch declaration syntax | v0.7
{{m-success}} 28.4 | and, nand, nor, or, xor, and xnor gates | v0.7
{{m-success}} 28.5 | buf and not gates | v0.7
{{m-success}} 28.6 | bufif1, bufif0, notif1, and notif0 gates | v0.7
{{m-success}} 28.7 | MOS switches | v0.7
{{m-success}} 28.8 | Bidirectional pass switches | v0.7
{{m-success}} 28.9 | CMOS switches | v0.7
{{m-success}} 28.10 | pullup and pulldown sources | v0.7
{{m-dim}}     28.11 | Logic strength modeling | N/A
{{m-dim}}     28.12 | Strengths and values of combined signals | N/A
{{m-dim}}     28.13 | Strength reduction by nonresistive devices | N/A
{{m-dim}}     28.14 | Strength reduction by resistive devices | N/A
{{m-dim}}     28.15 | Strengths of net types | N/A
{{m-success}} 28.16 | Gate and net delays | v0.7

## 29. User-defined primitives

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 29.3 | UDP definition | v4.0
{{m-success}} 29.4 | Combinational UDPs | v4.0
{{m-success}} 29.5 | Level-sensitive sequential UDPs | v4.0
{{m-success}} 29.6 | Edge-sensitive sequential UDPs | v4.0
{{m-success}} 29.7 | Sequential UDP initialization | v0.7
{{m-success}} 29.8 | UDP instances | v0.7
{{m-dim}}     29.9 | Mixing level-sensitive and edge-sensitive descriptions | N/A
{{m-dim}}     29.10 | Level-sensitive dominance | N/A

## 30. Specify blocks

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 30.3 | Specify block declaration | v3.0
{{m-success}} 30.4 | Module path declarations | v3.0
{{m-success}} 30.5 | Assigning delays to module paths | v3.0
{{m-dim}}     30.6 | Mixing module path delays and distributed delays | N/A
{{m-success}} 30.7 | Detailed control of pulse filtering behavior | v3.0

## 31. Timing checks

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 31.2 | Overview | v3.0
{{m-success}} 31.3 | Timing checks using a stability window | v3.0
{{m-success}} 31.4 | Timing checks for clock and control signals | v3.0
{{m-success}} 31.5 | Edge-control specifiers | v3.0
{{m-success}} 31.6 | Notifiers: user-defined responses to timing violations | v3.0
{{m-success}} 31.7 | Enabling timing checks with conditioned events | v3.0
{{m-success}} 31.8 | Vector signals in timing checks | v3.0
{{m-success}} 31.9 | Negative timing checks | v3.0

## 32. Backannotation using the standard delay format

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-dim}}     32.3 | The SDF annotator | N/A
{{m-dim}}     32.4 | Mapping of SDF constructs to SystemVerilog | N/A
{{m-dim}}     32.5 | Multiple annotations | N/A
{{m-dim}}     32.6 | Multiple SDF files | N/A
{{m-dim}}     32.7 | Pulse limit annotation | N/A
{{m-dim}}     32.8 | SDF to SystemVerilog delay value mapping | N/A
{{m-success}} 32.9 | Loading timing data from an SDF file | v0.7

## 33. Configuring the contents of a design

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 33.2 | Overview | v6.0
{{m-success}} 33.3 | Libraries | v4.0
{{m-success}} 33.4 | Configurations | v6.0
{{m-success}} 33.5 | Using libraries and configs | v5.0
{{m-success}} 33.6 | Configuration examples | v6.0
{{m-success}} 33.7 | Displaying library binding information | v6.0
{{m-success}} 33.8 | Library mapping examples | v4.0

## 34. Protected envelopes

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-dim}}     34.2 | Overview | N/A
{{m-dim}}     34.3 | Processing protected envelopes | N/A
{{m-success}} 34.4 | Protect pragma directives | v2.0
{{m-success}} 34.5 | Protect pragma keywords | v2.0

## 35. Direct programming interface

{{w:15%}} LRM | Feature | {{w:15%}} Supported
--- | ------- | ---------
{{m-success}} 35.2 | Overview | v0.6
{{m-dim}}     35.3 | Two layers of DPI | N/A
{{m-success}} 35.4 | Global namespace of imported and exported functions | v0.6
{{m-success}} 35.5 | Imported tasks and functions | v0.6
{{m-success}}  35.5.6.1 | Open arrays | v3.0
{{m-success}} 35.6 | Calling imported functions | v0.6
{{m-success}} 35.7 | Exported functions | v0.6
{{m-success}} 35.8 | Exported tasks | v0.6
{{m-dim}}     35.9 | Disabling DPI tasks and functions | N/A

@section simulation Simulation

Simulation is not yet supported at all, but it will be at some point in the future.

*/
