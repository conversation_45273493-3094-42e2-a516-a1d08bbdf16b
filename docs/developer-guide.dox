/** @page developer-guide Developer Guide
@brief Information on the internals of slang and its use as a library

slang is designed first and foremost as a collection of software libraries that
can be used, in whole or in part, in other projects. Those might include things
like linting tools, code editor completion, code generation as part of a build,
automated refactoring tools, runtime JITing or interpretation, simulators,
and synthesis tools.

This section gives an overview of the slang libraries and codebase,
and serves as a starting point for learning to use it in your own projects.

- @ref building - Instructions for obtaining and building the library
- @ref commoncomp - Overview of shared code and practices used throughout the library
- @ref sourcemanagement - APIs for loading and managing source code for compilation
- @ref diagnostics - APIs that support issuing diagnostics
- @ref parsing - APIs for parsing SystemVerilog source code

*/
