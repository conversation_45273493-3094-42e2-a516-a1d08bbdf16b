/** @page command-line-ref Command Line Reference
@brief Reference information about all command line options

@tableofcontents

@section general General

`-h,--help`

Display help text about command line options and exit.

`--version`

Display slang version information and exit.

`-q,--quiet`

Don't print non-essential output status.

`--std (1800-2017 | 1800-2023 | latest)`

Sets the version of the SystemVerilog language to use. This changes how the code is parsed
and elaborated and which language features are available to use. The current default is
1800-2017, though this may change in the future. Using "latest" will use the latest available
version, currently 1800-2023.

`positional arguments`

Paths to files (using @ref file-patterns "file patterns") that should be included in the compilation.

`--single-unit`

Causes the tool to treat all input files as being part of a single compilation unit.
By default all files are considered to be separate compilation units and ordering between them
does not matter. When this option is provided, all files are concatenated together, in order, to
produce a single compilation unit. See @ref compilation-units for more discussion.

`-v,--libfile <file-pattern>[,...]`

Adds files to the compilation, like a positional argument, except that the files are
considered to be Verilog @ref source-libraries "source libraries". Libraries are always their
own compilation unit even when compiling with `--single-unit`, and modules within them are
never automatically instantiated.

`-f <file-pattern>[,...]`

Opens the given "command files" containing lists of arguments to process in
addition to the ones provided on the command line itself. Any file paths in
the files are relative to the current working directory.

See @ref command-files for more information about command files.

`-F <file-pattern>[,...]`

Opens the given "command files" containing lists of arguments to process in
addition to the ones provided on the command line itself. Any file paths in
the files are relative to the command file itself.

See @ref command-files for more information about command files.

`-C <file-pattern>[,...]`

Opens the given "compilation unit listing files" containing lists of files
and arguments used to create separate compilation units. Any file paths in
the files are relative to the unit listing file itself.

See @ref unit-listing for more information about compilation unit listings.

`--exclude-ext <ext>`

Any provided positional arguments that have extensions matching &lt;ext> will be ignored.
This option is typically used when projects have existing command files listing sources
that are not SystemVerilog code.

`-j,--threads <count>`

Controls the number of threads used for parallel compilation. slang will by default
use the number of threads supported by the system -- this can be set to some other
value to more specifically control the concurrency. Setting it to 1 will disable
the use of threading.

Note that multithreading *parsing* cannot be supported when running
with `--single-unit`, though other parts of the compilation process
may still take advantage of threading where possible.

@section Actions

These options control what action the tool will perform when run.
Some of them are mutually exclusive. If none of these are provided,
the default action is to elaborate the design, print any diagnostics,
and exit.

`-E,--preprocess`

Treat all files as a single input file (as if `--single-unit` had been passed),
run the preprocessor on them, and then print the preprocessed text to stdout.
If errors occur during preprocessing, they will be printed instead of the preprocessed text.

`--macros-only`

Run the preprocessor on all input files, print out any macros that are discovered, and exit.
No diagnostics will be printed.

`--parse-only`

Perform parsing of all input files but don't perform type checking and elaboration.
All diagnostics encountered will be printed.

`--lint-only`

Only perform linting of code, don't try to elaborate a full hierarchy.

`--disable-analysis`

Disable the post-elaboration analysis pass. This will improve performance, at the
expense of preventing various errors and warnings from being diagnosed. This option
should probably not be needed unless there are bugs in the analysis pass that need
to be worked around.

@section Depfiles

These options output various depfiles as filelists in addition to other actions.
The default format places a file on each line.
The `--depfile-target` changes this to the makefile format that is emitted by C++ compilers.

`--Mall,--all-deps <file>`

Generate dependency file list of all files used during parsing.

`--Minclude,--include-deps <file>`

Generate dependency file list of just include files that were used during parsing.

`--Mmodule,--module-deps`

Generate dependency file list of source files parsed, excluding include files.

`--depfile-target <target>`

Output depfile lists in makefile format, creating the file with `<target>:` as the make target.


@section include-paths Include paths and macros

```
-I,--include-directory <dir-pattern>[,...]
+incdir+<dir>[+<dir>...]
```

Add the given directory paths to the list of directories searched by \`include directives that use
quotes to specify the path.

`--isystem <dir-pattern>[,...]`

Add the given directory paths to the list of directories searched by \`include directives that use
angle brackets to specify the path.

```
-D,--define-macro <macro>=<value>
+define+<macro>=<value>[+<macro>=<value>...]
```

Define \<macro\> to \<value\> (or `1` if \<value\> is ommitted) at the start of all source files.
Example:

@code{.ansi}
slang -DFOO=2 -DBAR=asdf -D BAZ=3
@endcode

`-U,--undefine-macro <macro>`

Undefine the given macro at the start of all source files.

@section clr-preprocessor Preprocessor

`--comments`

When running in preprocessor-only mode (using `-E`) include comments in the preprocessed output text.

`--directives`

When running in preprocessor-only mode (using `-E`) include directives in the preprocessed output text.

`--max-include-depth <depth>`

Set the maximum depth of nested include files. Exceeding this limit will cause an error.
The default is 1024.

`--libraries-inherit-macros`

If true, library files will inherit macro definitions from the primary source files.
By default library files are independent and will not inherit macros from the main project.
`--single-unit` must also be passed when this option is used.

`--enable-legacy-protect`

Enables use of legacy source protection directives for compatibility with older tools.

`--translate-off-format <common>,<start>,<end>`

Specify the format of a "translate off" directive in a comment that denotes a span
of text that is skipped from processing (as if it had been commented out). All source
text between the start comment and the end comment will be skipped.

@note It is generally preferred to use `ifdef` directives instead for this functionality,
but legacy code may contain these directives in comment form so this option can be
used for compatibility.

The format of the argument is three comma-separated words containing alphanumeric
and underscore characters. The first word is the common keyword at the beginning
of the comment. The second word is the directive that starts a skipped region,
and the third is the directive that ends the region.

More than one of these options can be provided to skip regions with differing styles
in the same run of the tool.

For example, using an option like `--translate-off-format pragma,translate_off,translate_on`
will work with code like the following:

@code{.sv}
module m;
    // pragma translate_off
    ... some code to skip ...
    // pragma translate_on
endmodule
@endcode

`--obfuscate-ids`

Causes all identifiers in the preprocessed output to be replaced with obfuscated
alphanumeric strings.

@section clr-parsing Parsing

`--max-parse-depth <depth>`

Set the maximum depth of nested language elements. This is a measure of the depth of the parsing
stack, which is checked against this limit to avoid stack overflows. The default is 1024.

`--max-lexer-errors <depth>`

Set the maximum number of errors that can occur during lexing before the rest of the file is skipped.
The default is 64.

`-y,--libdir <dir-pattern>[,...]`

Add the given directory paths to the list of directories searched when an unknown module instantiation
or package import is encountered. Combined with `--libext`, files are automatically included based on the
name that is unknown. This list is empty by default.

The search works as follows: all known modules, interfaces, programs, packages, and classes add their
names to a list of known definitions. Any instantiations, package import directives, or double colon-scoped
names that reference a name not in the list of known definitions will trigger a search in all library
directories, trying all specified library extensions. If a matching file is found it will be loaded and
parsed in its entirety, and the algorithm will be triggered again on any new names found.

See @ref library-search for more details.

`-Y,--libext <ext>`

Add the given extension to the list of extensions tried when searching for files to satisfy
unknown module instantiations and package imports. This list automatically includes '.v' and '.sv' by default.

@section json-output JSON Output

`--ast-json <file>`

Dump the compiled AST in JSON format to the specified file, or '-' for stdout.

`--ast-json-scope <path>`

When dumping AST to JSON, include only the scope (or symbol) specified by the given hierarchical path.
This option can be specified more than once to include more than one scope. If not provided, all
symbols are dumped.

`--ast-json-source-info`

When dumping AST to JSON, include source line and file information.

`--ast-json-detailed-types`

When dumping AST to JSON, include expanded type information in the output instead of
condensing all types into a human-friendly string.

@section compilation-limits Compilation

`--top <name>`

Specifies the name of a module or program that should be instantiated at the root
of the design. Can be specified more than once to instantiate multiple top-level modules.
The module (or program) specified must not have any non-defaulted parameters or
interface ports.

Additionally, you can specify the name of one or more SystemVerilog configurations.
The cells specified in the `design` statement of those configurations will become
the top levels of the design. If the name of a configuration overlaps with that of
a module/interface/program definition, the latter will be taken by default. You can use
a `:config` suffix on the name to explicitly select the configuration.

If no top modules are specified manually, they will be automatically inferred by
finding all modules that are not instantiated elsewhere in the design.

`-L <library>[,...]`

A list of library names in the order in which they should be used to resolve
module names. Libraries that are earlier in the list will be searched before
ones later in the list.

If no explicit ordering is given the default order is
the order in which the libraries are specified to the tool.

`--defaultLibName <name>`

Sets the name of the default library. If not set, defaults to "work".

`--disable-instance-caching`

Disable the use of instance caching, which normally allows skipping duplicate instance bodies to
save time when elaborating. This shouldn't need to be turned off except to work around bugs in
the caching implementation.

`--max-hierarchy-depth <depth>`

Set the maximum depth of the design hierarchy. Used to detect infinite
module instantiation recursion. The default is 128.

`--max-generate-steps <steps>`

Set the maximum number of steps that can occur during generate block evaluation
before giving up. Used to detect infinite generate loops. The default is 131072.

`--max-constexpr-depth <depth>`

Set the maximum depth of the constant evaluation call stack. Used to detect
infinite recursion during constant evaluation. The default is 128.

`--max-constexpr-steps <steps>`

Set the maximum number of steps that can occur during constant evaluation
before giving up. Used to detect infinite constant evaluation loops.
The default is 1000000.

`--constexpr-backtrace-limit <limit>`

Set the maximum number of frames to show when printing a constant evaluation
backtrace in diagnostics; the rest will be abbreviated to avoid spamming output.
The default is 10.

`--max-instance-array <limit>`

Set the maximum number of instances allowed in a single instance array.
The limit exists to prevent runaway compilation times on invalid input.
The default is 65535.

`--max-udp-coverage-notes <limit>`

When reporting a warning for a UDP that doesn't cover all possible input
edge transitions, limit the number of notes printed to this value to avoid
excessively large diagnostic output. The default is 8.

`-T,--timing min|typ|max`

Select which expressions in min:typ:max triplets should be processed as part of
the compilation. By default this will be the "typical", or middle expression.

`--timescale <base>/<precision>`

Specifies a default time scale to use for design elements that don't explicitly
provide one. If this option is not set, there is no default and an error will be
issued if not all elements have a time scale specified. Example: `--timescale=1ns/1ns`

`-G <name>=<value>`

Overrides parameters to the provided value. If the 'name' is a simple undotted name,
all parameters in all top-level modules that match the given name will be overridden.
Otherwise, the name is assumed to be hierarchical and will be treated as if it had
been written in a `defparam` statement in the design.

The 'value' may be any SystemVerilog literal or evaluatable constant expression,
including references to packages and enums in the design.

@section analysis Analysis

`--dfa-unique-priority`

Respect the 'unique' and 'priority' keywords when analyzing data flow through case statements
(on by default). This affects things like whether the following example will report a warning
for an inferred latch or not:

@code{.sv}
module m(input [3:0] a, output logic b);
    always_comb begin
        unique case (a)
            0: b = 1;
            1: b = 1;
        endcase
    end
endmodule
@endcode

The case statement doesn't cover all possible values of `a` but since the `unique`
keyword is provided it will be assumed that all values are covered in practice and
no warning will be issued.

`--dfa-four-state`

When set, case statements must include items that cover all possible X and Z bit
combinations of their input in order to be assumed to be fully covered, meaning
that things like inferred latches won't be reported if their target is assigned
in all such case item statements.

By default X and Z bit coverage is not required; having items that cover all possible
0 and 1 bit combinations is sufficient to assume full coverage of the case input.

`--max-case-analysis-steps <steps>`

The maximum number of steps to take while trying to analyze a wildcard case statement.
The default is 65535.

Determining whether a list of wildcard case clauses fully cover the entire input space
is, in general, NP-complete, so there is a limit to prevent exponential explosion in
analysis time. In practice case statements written by humans will rarely, if ever,
exhibit this kind of behavior.

`--max-loop-analysis-steps <steps>`

The maximum number of steps to take while trying to unroll for loops during data flow analysis.
The default is 65535.

This setting controls @ref loop-unroll behavior. When the limit is hit, it is assumed that the
loop (or loops when nested) cannot be unrolled and are analyzed in one pass instead.
Setting the limit to zero will prevent loop unrolling at all, which better matches the LRM's
rules about assigning to lvalues with loop indices used to select subsets of a variable.

@section compat-option Compatibility

`--compat vcs`

Attempt to increase compatibility with the specified tool. Various options will
be set and some warnings will be silenced to mimic the given tool as closely as possible.
Currently only 'vcs' is supported as a value.

`--allow-use-before-declare`

Don't issue an error if an identifier is used before its declaration. This is not allowed in
SystemVerilog -- this option is provided for compatibility with tools that have weaker enforcement
of this rule.

`--allow-hierarchical-const`

Allow hierarchical references in constant expressions. SystemVerilog doesn't permit this,
but some tools allow it anyway so this flag can be used for compatibility purposes.

`--relax-enum-conversions`

Allow all integral types to convert implicitly to enum types in assignments. SystemVerilog doesn't
permit this, but some tools allow it anyway so this flag can be used for compatibility purposes.

`--relax-string-conversions`

Allow string types to convert implicitly to integral types in assignments. SystemVerilog doesn't
permit this, but some tools allow it anyway so this flag can be used for compatibility purposes.

`--allow-dup-initial-drivers`

Allow signals driven by always_comb or always_ff procedures to also be driven by initial blocks.
SystemVerilog doesn't permit this, but some tools allow it anyway so this flag can be used for
compatibility purposes.

`--allow-toplevel-iface-ports`

Allow top-level modules to have interface ports. SystemVerilog doesn't permit this, but some tools
allow it anyway so this flag can be used for compatibility purposes.

`--allow-recursive-implicit-call`

Allow implicit call expressions (ones that lack parentheses) to be recursive function calls.
The LRM states that directly recursive function calls require parentheses but many other tools
allow this anyway so this flag can be used for compatibility purposes.

`--allow-bare-value-param-assigment`

Allow module instantiations to provide a single parameter value assignment without enclosing it
in parentheses. SystemVerilog doesn't permit this, but some tools allow it anyway so this flag
can be used for compatibility purposes.

`--allow-self-determined-stream-concat`

Allow self-determined streaming concatenation expressions. SystemVerilog only permits these
expressions in assignment-like contexts but some tools allow it anyway so this flag can be
used for compatibility purposes.

`--allow-multi-driven-locals`

Allow subroutine local variables to be driven from multiple always_comb/always_ff blocks.
The LRM does not make an exception for local variables in assignment rules but
most tools allow it anyway so this flag exists for compatibility with those tools.

`--allow-merging-ansi-ports`

Allow ANSI port declarations to merge with a net or variable declaration internal to the
instance, instead of creating a new internal symbol to connect to. This is not allowed in
SystemVerilog but some tools support it anyway so this flag can be used for compatibility purposes.

For example:

@code{.sv}
module m(input a, output b);
    wire [31:0] a;
    logic [31:0] b;
    assign b = a;
endmodule
@endcode

`--cmd-ignore <vendor_cmd>,<N>`

Define rule to ignore vendor command &lt;vendor_cmd> with its following &lt;N> parameters.
A command of the form +xyz will also match any vendor command of the form +xyz+abc,
as +abc is the command's argument, and doesn't need to be matched.
This option is typically used to ignore commands listed in command files that are meant
for some other tool.

`--cmd-rename <vendor_cmd>,<slang_cmd>`

Define rule to rename vendor command &lt;vendor_cmd> into existing &lt;slang_cmd>.
Similarly to `--cmd-ignore`, this exists to support existing command files that
specify options with different names.

`--ignore-directive <vendor_directive>`

Some tool vendors declare custom preprocessor directives, and slang fails when
encountering those. By using this command line option with the vendor's directive,
it will be ignored (the vendor directive should be specified without the leading \`
symbol). It is possible to use this command line option multiple times, to ignore
multiple vendor directives.
Please note that any vendor directive ignored also ignores all optional parameters
until the end of the line.

@section diag-control Diagnostic Control

`--color-diagnostics`

Always print diagnostics in color. If this option is unset, colors will be enabled
if a color-capable terminal is detected.

`--diag-column`

Show or hide column numbers in diagnostic output. The default is to show.

`--diag-location`

Show or hide location information (file name, line and column numbers) in diagnostic output. The default is to show.

`--diag-source`

Show or hide source code and caret location in diagnostic output. The default is to show.

`--diag-option`

Show or hide warning option names in diagnostic output. The default is to show.

`--diag-include-stack`

Show or hide file include stacks in diagnostic output. The default is to show.

`--diag-macro-expansion`

Show or hide macro expansion backtraces in diagnostic output. The default is to show.

`--diag-abs-paths`

Display file names as absolute paths in diagnostic output. The default is to display relative paths.

`--diag-hierarchy always|never|auto`

Show or hide hierarchy locations in diagnostic output. The default is 'auto', which decides whether to
show the hierarchical path based on heuristics. 'always' will show the paths on every diagnostic,
and 'never' will suppress them.

`--diag-json <file>`

Dump diagnostics in JSON format to the specified file, or '-' for stdout.
If printing to stdout, the usual text-style diagnostics will be supressed.
Otherwise, they will be printed like normal.

`--suppress-warnings <file-pattern>[,...]`

One or more paths in which to suppress warnings. Use this if you want to generally turn on warnings
for your project and have it build cleanly but have some files which you can't modify for some reason.

`--suppress-macro-warnings <file-pattern>[,...]`

One or more paths in which to suppress warnings that originate from macro expansions. This means that
warnings inside of macros that are defined within these paths will be suppressed, even if the macros are
expanded into files that are not within these paths.

`--error-limit`

Set a limit on the number of errors that will be printed. Setting this to zero will
disable the limit. The default is 64.

`--ignore-unknown-modules`

Don't issue an error for instantiations of unknown modules, interface, and programs.

`--disallow-refs-to-unknown-instances`

When using `--ignore-unknown-modules`, explicitly disallow references to ignored module
instances by issuing an error. Otherwise such references will be silently ignored by
inserting error nodes into the elaborated AST.

@section clr-warnings Warnings

Warnings are diagnostic messages that report constructions that are not inherently illegal according to
the LRM but that are risky or suggest there may have been a mistake in the source. slang has a minimal
set of warnings enabled by default, which are selected such that running with the default set should
rarely result in warnings on most codebases. It is recommended that additional warnings be enabled to
discover potential errors in the design, with the `-Wextra` group being a good recommended starting set,
but many more are available. See @ref warning-ref for a complete list of warnings that can be enabled.

Some controllable warning names are actually a "group" of warnings that can be enabled or disabled
all at once. See @ref WarningGroups for a list of such groups.

Which warnings are enabled can be controlled with the options below. Except where noted, options
later in the command line override those that came earlier if they target the same warning with
conflicting settings. If an individual warning and a group that also contains that warning are
given conflicting settings, the option controlling the individual warning will win out,
regardless of what order it appears in the command line.

`-Wfoo`

Enable warning (or group of warnings) "foo".

`-Wno-foo`

Disable warning (or group of warnings) "foo".

`-Wnone`

Disable all warnings. This does not apply to any warnings that have been explicitly controlled by
another option, either before or after this one in the command line.

`-Weverything`

Enable all warnings. This does not apply to any warnings that have been explicitly controlled by
another option, either before or after this one in the command line.

`-Werror`

Treat all warnings as errors. This only applies to warnings that are enabled by some other option,
either before or after this one in the command line.

`-Werror=foo`

Treat warning (or group of warnings) "foo" as an error. This also enables the specified warning.

`-Wno-error=foo`

Treat warning (or group of warnings) "foo" as a warning even if `-Werror` is specified, regardless
of whether `-Werror` appears before or after in the command line. Note that unlike `-Werror=`, this
will not turn on the warning if it hasn't been enabled with a separate option.

@section clr-profiling Profiling

`--time-trace <path>`

Run slang with time tracing enabled, which collects information about how long
various parts of the compilation take. When the program exits it will write the
trace results to the given file, which is JSON text containing events in
the Chrome Trace Event format.

*/
