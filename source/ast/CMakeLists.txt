# ~~~
# SPDX-FileCopyrightText: <PERSON>
# SPDX-License-Identifier: MIT
# ~~~

target_sources(
  slang_slang
  PRIVATE builtins/ArrayMethods.cpp
          builtins/ConversionFuncs.cpp
          builtins/CoverageFuncs.cpp
          builtins/EnumMethods.cpp
          builtins/GateTypes.cpp
          builtins/MathFuncs.cpp
          builtins/MiscSystemFuncs.cpp
          builtins/NonConstFuncs.cpp
          builtins/QueryFuncs.cpp
          builtins/StdPackage.cpp
          builtins/StringMethods.cpp
          builtins/SystemTasks.cpp
          expressions/AssertionExpr.cpp
          expressions/AssignmentExpressions.cpp
          expressions/CallExpression.cpp
          expressions/ConversionExpression.cpp
          expressions/LiteralExpressions.cpp
          expressions/MiscExpressions.cpp
          expressions/Operator.cpp
          expressions/OperatorExpressions.cpp
          expressions/SelectExpressions.cpp
          statements/ConditionalStatements.cpp
          statements/LoopStatements.cpp
          statements/MiscStatements.cpp
          symbols/AttributeSymbol.cpp
          symbols/BlockSymbols.cpp
          symbols/ClassSymbols.cpp
          symbols/CompilationUnitSymbols.cpp
          symbols/CoverSymbols.cpp
          symbols/InstanceSymbols.cpp
          symbols/MemberSymbols.cpp
          symbols/ParameterBuilder.cpp
          symbols/ParameterSymbols.cpp
          symbols/PortSymbols.cpp
          symbols/SpecifySymbols.cpp
          symbols/SubroutineSymbols.cpp
          symbols/SymbolBuilders.cpp
          symbols/ValueSymbol.cpp
          symbols/VariableSymbols.cpp
          types/AllTypes.cpp
          types/DeclaredType.cpp
          types/NetType.cpp
          types/TypePrinter.cpp
          types/Type.cpp
          ASTContext.cpp
          ASTDiagMap.cpp
          ASTSerializer.cpp
          Bitstream.cpp
          Compilation.cpp
          Constraints.cpp
          EvalContext.cpp
          Expression.cpp
          FmtHelpers.cpp
          HierarchicalReference.cpp
          Lookup.cpp
          LSPUtilities.cpp
          LValue.cpp
          OpaqueInstancePath.cpp
          Patterns.cpp
          Scope.cpp
          ScriptSession.cpp
          SemanticFacts.cpp
          SFormat.cpp
          Statement.cpp
          Symbol.cpp
          SystemSubroutine.cpp
          TimingControl.cpp)
